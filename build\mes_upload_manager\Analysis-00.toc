(['E:\\vscode\\tomesmo\\app.py'],
 ['E:\\vscode\\tomesmo'],
 ['flask',
  'kafka',
  'pika',
  'json',
  'threading',
  'datetime',
  'requests',
  'uuid',
  'time',
  'os',
  'sys',
  'hashlib',
  'urllib.request',
  'urllib.error',
  'codecs'],
 ['e:\\python\\lib\\site-packages\\pygame\\__pyinstaller',
  'e:\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'e:\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks'],
 {},
 [],
 [],
 False,
 False,
 False,
 {},
 [],
 [('devices.json', 'E:\\vscode\\tomesmo\\devices.json', 'DATA'),
  ('fake_data_generator.py',
   'E:\\vscode\\tomesmo\\fake_data_generator.py',
   'DATA'),
  ('kafka_config.json', 'E:\\vscode\\tomesmo\\kafka_config.json', 'DATA'),
  ('pqm_api.py', 'E:\\vscode\\tomesmo\\pqm_api.py', 'DATA'),
  ('rabbitmq_config.json', 'E:\\vscode\\tomesmo\\rabbitmq_config.json', 'DATA'),
  ('static\\logo.png', 'E:\\vscode\\tomesmo\\static\\logo.png', 'DATA'),
  ('static\\script.js', 'E:\\vscode\\tomesmo\\static\\script.js', 'DATA'),
  ('static\\style.css', 'E:\\vscode\\tomesmo\\static\\style.css', 'DATA'),
  ('templates\\index.html',
   'E:\\vscode\\tomesmo\\templates\\index.html',
   'DATA'),
  ('test_config.py', 'E:\\vscode\\tomesmo\\test_config.py', 'DATA'),
  ('test_data_generator.py',
   'E:\\vscode\\tomesmo\\test_data_generator.py',
   'DATA')],
 '3.7.9 (tags/v3.7.9:13c94747c7, Aug 17 2020, 18:01:55) [MSC v.1900 32 bit '
 '(Intel)]',
 [('pyi_rth_inspect',
   'e:\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'e:\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'e:\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('app', 'E:\\vscode\\tomesmo\\app.py', 'PYSOURCE')],
 [('multiprocessing.popen_forkserver',
   'e:\\python\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'e:\\python\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'e:\\python\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'e:\\python\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('signal', 'e:\\python\\lib\\signal.py', 'PYMODULE'),
  ('selectors', 'e:\\python\\lib\\selectors.py', 'PYMODULE'),
  ('xmlrpc.client', 'e:\\python\\lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'e:\\python\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('gzip', 'e:\\python\\lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'e:\\python\\lib\\_compression.py', 'PYMODULE'),
  ('xml.parsers.expat', 'e:\\python\\lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.parsers', 'e:\\python\\lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'e:\\python\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'e:\\python\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'e:\\python\\lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax', 'e:\\python\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'e:\\python\\lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'e:\\python\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader', 'e:\\python\\lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('http.client', 'e:\\python\\lib\\http\\client.py', 'PYMODULE'),
  ('ssl', 'e:\\python\\lib\\ssl.py', 'PYMODULE'),
  ('calendar', 'e:\\python\\lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'e:\\python\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'e:\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'e:\\python\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'e:\\python\\lib\\gettext.py', 'PYMODULE'),
  ('http', 'e:\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('email.message', 'e:\\python\\lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'e:\\python\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'e:\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'e:\\python\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('string', 'e:\\python\\lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'e:\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'e:\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators', 'e:\\python\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'e:\\python\\lib\\email\\generator.py', 'PYMODULE'),
  ('random', 'e:\\python\\lib\\random.py', 'PYMODULE'),
  ('bisect', 'e:\\python\\lib\\bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   'e:\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email.charset', 'e:\\python\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'e:\\python\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'e:\\python\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase', 'e:\\python\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.header', 'e:\\python\\lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'e:\\python\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'e:\\python\\lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr', 'e:\\python\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email', 'e:\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('quopri', 'e:\\python\\lib\\quopri.py', 'PYMODULE'),
  ('getopt', 'e:\\python\\lib\\getopt.py', 'PYMODULE'),
  ('uu', 'e:\\python\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'e:\\python\\lib\\optparse.py', 'PYMODULE'),
  ('email.parser', 'e:\\python\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'e:\\python\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('decimal', 'e:\\python\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'e:\\python\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'e:\\python\\lib\\contextvars.py', 'PYMODULE'),
  ('numbers', 'e:\\python\\lib\\numbers.py', 'PYMODULE'),
  ('base64', 'e:\\python\\lib\\base64.py', 'PYMODULE'),
  ('hmac', 'e:\\python\\lib\\hmac.py', 'PYMODULE'),
  ('tempfile', 'e:\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('shutil', 'e:\\python\\lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'e:\\python\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'e:\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'e:\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib', 'e:\\python\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib.abc', 'e:\\python\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'e:\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'e:\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.util', 'e:\\python\\lib\\importlib\\util.py', 'PYMODULE'),
  ('contextlib', 'e:\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('tarfile', 'e:\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('lzma', 'e:\\python\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'e:\\python\\lib\\bz2.py', 'PYMODULE'),
  ('struct', 'e:\\python\\lib\\struct.py', 'PYMODULE'),
  ('socket', 'e:\\python\\lib\\socket.py', 'PYMODULE'),
  ('multiprocessing.util',
   'e:\\python\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('logging', 'e:\\python\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'e:\\python\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'e:\\python\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle', 'e:\\python\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('multiprocessing.popen_fork',
   'e:\\python\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'e:\\python\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.semaphore_tracker',
   'e:\\python\\lib\\multiprocessing\\semaphore_tracker.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'e:\\python\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'e:\\python\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'e:\\python\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'e:\\python\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.wintypes', 'e:\\python\\lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('ctypes._endian', 'e:\\python\\lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'e:\\python\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'e:\\python\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'e:\\python\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'e:\\python\\lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'e:\\python\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'e:\\python\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'e:\\python\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'e:\\python\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'e:\\python\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'e:\\python\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('subprocess', 'e:\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'e:\\python\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'e:\\python\\lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'e:\\python\\lib\\pkgutil.py', 'PYMODULE'),
  ('inspect', 'e:\\python\\lib\\inspect.py', 'PYMODULE'),
  ('ast', 'e:\\python\\lib\\ast.py', 'PYMODULE'),
  ('dis', 'e:\\python\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'e:\\python\\lib\\opcode.py', 'PYMODULE'),
  ('multiprocessing',
   'e:\\python\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('hashlib', 'e:\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('_strptime', 'e:\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('uuid', 'e:\\python\\lib\\uuid.py', 'PYMODULE'),
  ('ctypes.util', 'e:\\python\\lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'e:\\python\\lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'e:\\python\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'e:\\python\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'e:\\python\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'e:\\python\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('requests',
   'e:\\python\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'e:\\python\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'e:\\python\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'e:\\python\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies', 'e:\\python\\lib\\http\\cookies.py', 'PYMODULE'),
  ('http.cookiejar', 'e:\\python\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('requests.models',
   'e:\\python\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna', 'e:\\python\\lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core', 'e:\\python\\lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.uts46data',
   'e:\\python\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.intranges',
   'e:\\python\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.idnadata',
   'e:\\python\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'e:\\python\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('requests.hooks',
   'e:\\python\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'e:\\python\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('dummy_threading', 'e:\\python\\lib\\dummy_threading.py', 'PYMODULE'),
  ('_dummy_thread', 'e:\\python\\lib\\_dummy_thread.py', 'PYMODULE'),
  ('requests.auth',
   'e:\\python\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'e:\\python\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'e:\\python\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'e:\\python\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'e:\\python\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'e:\\python\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages',
   'e:\\python\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'e:\\python\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'e:\\python\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'e:\\python\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'e:\\python\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'e:\\python\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'e:\\python\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'e:\\python\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'e:\\python\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('__future__', 'e:\\python\\lib\\__future__.py', 'PYMODULE'),
  ('urllib3.filepost',
   'e:\\python\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'e:\\python\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('mimetypes', 'e:\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('requests.api',
   'e:\\python\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'e:\\python\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'e:\\python\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'e:\\python\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'e:\\python\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.packages.backports.weakref_finalize',
   'e:\\python\\lib\\site-packages\\urllib3\\packages\\backports\\weakref_finalize.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'e:\\python\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'e:\\python\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('ipaddress', 'e:\\python\\lib\\ipaddress.py', 'PYMODULE'),
  ('urllib3.util.queue',
   'e:\\python\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'e:\\python\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.request',
   'e:\\python\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3._collections',
   'e:\\python\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3.connection',
   'e:\\python\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3._version',
   'e:\\python\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.response',
   'e:\\python\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'e:\\python\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'e:\\python\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'e:\\python\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('netrc', 'e:\\python\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'e:\\python\\lib\\shlex.py', 'PYMODULE'),
  ('requests.certs',
   'e:\\python\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'e:\\python\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'e:\\python\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('typing', 'e:\\python\\lib\\typing.py', 'PYMODULE'),
  ('importlib.resources',
   'e:\\python\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('requests.packages',
   'e:\\python\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'e:\\python\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'e:\\python\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'e:\\python\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'e:\\python\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('chardet',
   'e:\\python\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.version',
   'e:\\python\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'e:\\python\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'e:\\python\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'e:\\python\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'e:\\python\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'e:\\python\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'e:\\python\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'e:\\python\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'e:\\python\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'e:\\python\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langcyrillicmodel',
   'e:\\python\\lib\\site-packages\\chardet\\langcyrillicmodel.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'e:\\python\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'e:\\python\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'e:\\python\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'e:\\python\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'e:\\python\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'e:\\python\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'e:\\python\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'e:\\python\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'e:\\python\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'e:\\python\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'e:\\python\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'e:\\python\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'e:\\python\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'e:\\python\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'e:\\python\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'e:\\python\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'e:\\python\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'e:\\python\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'e:\\python\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'e:\\python\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'e:\\python\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   'e:\\python\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'e:\\python\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.enums',
   'e:\\python\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'e:\\python\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.compat',
   'e:\\python\\lib\\site-packages\\chardet\\compat.py',
   'PYMODULE'),
  ('charset_normalizer',
   'e:\\python\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'e:\\python\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'e:\\python\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'e:\\python\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'e:\\python\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'e:\\python\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'e:\\python\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('typing_extensions',
   'e:\\python\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'e:\\python\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'e:\\python\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'e:\\python\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('kafka', 'e:\\python\\lib\\site-packages\\kafka\\__init__.py', 'PYMODULE'),
  ('kafka.structs',
   'e:\\python\\lib\\site-packages\\kafka\\structs.py',
   'PYMODULE'),
  ('kafka.serializer',
   'e:\\python\\lib\\site-packages\\kafka\\serializer\\__init__.py',
   'PYMODULE'),
  ('kafka.serializer.abstract',
   'e:\\python\\lib\\site-packages\\kafka\\serializer\\abstract.py',
   'PYMODULE'),
  ('kafka.conn', 'e:\\python\\lib\\site-packages\\kafka\\conn.py', 'PYMODULE'),
  ('kafka.socks5_wrapper',
   'e:\\python\\lib\\site-packages\\kafka\\socks5_wrapper.py',
   'PYMODULE'),
  ('kafka.sasl',
   'e:\\python\\lib\\site-packages\\kafka\\sasl\\__init__.py',
   'PYMODULE'),
  ('kafka.sasl.sspi',
   'e:\\python\\lib\\site-packages\\kafka\\sasl\\sspi.py',
   'PYMODULE'),
  ('kafka.sasl.abc',
   'e:\\python\\lib\\site-packages\\kafka\\sasl\\abc.py',
   'PYMODULE'),
  ('kafka.sasl.scram',
   'e:\\python\\lib\\site-packages\\kafka\\sasl\\scram.py',
   'PYMODULE'),
  ('kafka.sasl.plain',
   'e:\\python\\lib\\site-packages\\kafka\\sasl\\plain.py',
   'PYMODULE'),
  ('kafka.sasl.oauth',
   'e:\\python\\lib\\site-packages\\kafka\\sasl\\oauth.py',
   'PYMODULE'),
  ('kafka.sasl.msk',
   'e:\\python\\lib\\site-packages\\kafka\\sasl\\msk.py',
   'PYMODULE'),
  ('kafka.sasl.gssapi',
   'e:\\python\\lib\\site-packages\\kafka\\sasl\\gssapi.py',
   'PYMODULE'),
  ('platform', 'e:\\python\\lib\\platform.py', 'PYMODULE'),
  ('kafka.protocol.types',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\types.py',
   'PYMODULE'),
  ('kafka.protocol',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\__init__.py',
   'PYMODULE'),
  ('kafka.protocol.abstract',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\abstract.py',
   'PYMODULE'),
  ('kafka.protocol.sasl_handshake',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\sasl_handshake.py',
   'PYMODULE'),
  ('kafka.protocol.api',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\api.py',
   'PYMODULE'),
  ('kafka.protocol.struct',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\struct.py',
   'PYMODULE'),
  ('kafka.util', 'e:\\python\\lib\\site-packages\\kafka\\util.py', 'PYMODULE'),
  ('kafka.protocol.sasl_authenticate',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\sasl_authenticate.py',
   'PYMODULE'),
  ('kafka.protocol.produce',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\produce.py',
   'PYMODULE'),
  ('kafka.protocol.parser',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\parser.py',
   'PYMODULE'),
  ('kafka.protocol.frame',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\frame.py',
   'PYMODULE'),
  ('kafka.protocol.metadata',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\metadata.py',
   'PYMODULE'),
  ('kafka.protocol.list_offsets',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\list_offsets.py',
   'PYMODULE'),
  ('kafka.protocol.find_coordinator',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\find_coordinator.py',
   'PYMODULE'),
  ('kafka.protocol.fetch',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\fetch.py',
   'PYMODULE'),
  ('kafka.protocol.commit',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\commit.py',
   'PYMODULE'),
  ('kafka.protocol.broker_api_versions',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\broker_api_versions.py',
   'PYMODULE'),
  ('kafka.protocol.api_versions',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\api_versions.py',
   'PYMODULE'),
  ('kafka.protocol.admin',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\admin.py',
   'PYMODULE'),
  ('kafka.vendor.enum34',
   'e:\\python\\lib\\site-packages\\kafka\\vendor\\enum34.py',
   'PYMODULE'),
  ('kafka.metrics.stats',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\stats\\__init__.py',
   'PYMODULE'),
  ('kafka.metrics.stats.total',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\stats\\total.py',
   'PYMODULE'),
  ('kafka.metrics.measurable_stat',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\measurable_stat.py',
   'PYMODULE'),
  ('kafka.metrics.stat',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\stat.py',
   'PYMODULE'),
  ('kafka.metrics.measurable',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\measurable.py',
   'PYMODULE'),
  ('kafka.metrics.stats.sensor',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\stats\\sensor.py',
   'PYMODULE'),
  ('kafka.metrics.stats.rate',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\stats\\rate.py',
   'PYMODULE'),
  ('kafka.metrics.stats.sampled_stat',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\stats\\sampled_stat.py',
   'PYMODULE'),
  ('kafka.metrics.stats.percentiles',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\stats\\percentiles.py',
   'PYMODULE'),
  ('kafka.metrics.compound_stat',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\compound_stat.py',
   'PYMODULE'),
  ('kafka.metrics.stats.percentile',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\stats\\percentile.py',
   'PYMODULE'),
  ('kafka.metrics.stats.min_stat',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\stats\\min_stat.py',
   'PYMODULE'),
  ('kafka.metrics.stats.max_stat',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\stats\\max_stat.py',
   'PYMODULE'),
  ('kafka.metrics.stats.histogram',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\stats\\histogram.py',
   'PYMODULE'),
  ('kafka.metrics.stats.count',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\stats\\count.py',
   'PYMODULE'),
  ('kafka.metrics.stats.avg',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\stats\\avg.py',
   'PYMODULE'),
  ('kafka.metrics',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\__init__.py',
   'PYMODULE'),
  ('kafka.metrics.quota',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\quota.py',
   'PYMODULE'),
  ('kafka.metrics.metrics',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\metrics.py',
   'PYMODULE'),
  ('kafka.metrics.metric_name',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\metric_name.py',
   'PYMODULE'),
  ('kafka.metrics.metric_config',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\metric_config.py',
   'PYMODULE'),
  ('kafka.metrics.kafka_metric',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\kafka_metric.py',
   'PYMODULE'),
  ('kafka.metrics.dict_reporter',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\dict_reporter.py',
   'PYMODULE'),
  ('kafka.metrics.metrics_reporter',
   'e:\\python\\lib\\site-packages\\kafka\\metrics\\metrics_reporter.py',
   'PYMODULE'),
  ('kafka.future',
   'e:\\python\\lib\\site-packages\\kafka\\future.py',
   'PYMODULE'),
  ('kafka.errors',
   'e:\\python\\lib\\site-packages\\kafka\\errors.py',
   'PYMODULE'),
  ('kafka.vendor.six',
   'e:\\python\\lib\\site-packages\\kafka\\vendor\\six.py',
   'PYMODULE'),
  ('kafka.vendor.selectors34',
   'e:\\python\\lib\\site-packages\\kafka\\vendor\\selectors34.py',
   'PYMODULE'),
  ('kafka.vendor',
   'e:\\python\\lib\\site-packages\\kafka\\vendor\\__init__.py',
   'PYMODULE'),
  ('kafka.vendor.socketpair',
   'e:\\python\\lib\\site-packages\\kafka\\vendor\\socketpair.py',
   'PYMODULE'),
  ('kafka.producer',
   'e:\\python\\lib\\site-packages\\kafka\\producer\\__init__.py',
   'PYMODULE'),
  ('kafka.producer.kafka',
   'e:\\python\\lib\\site-packages\\kafka\\producer\\kafka.py',
   'PYMODULE'),
  ('kafka.record.legacy_records',
   'e:\\python\\lib\\site-packages\\kafka\\record\\legacy_records.py',
   'PYMODULE'),
  ('kafka.record',
   'e:\\python\\lib\\site-packages\\kafka\\record\\__init__.py',
   'PYMODULE'),
  ('kafka.record.memory_records',
   'e:\\python\\lib\\site-packages\\kafka\\record\\memory_records.py',
   'PYMODULE'),
  ('kafka.record.util',
   'e:\\python\\lib\\site-packages\\kafka\\record\\util.py',
   'PYMODULE'),
  ('kafka.record._crc32c',
   'e:\\python\\lib\\site-packages\\kafka\\record\\_crc32c.py',
   'PYMODULE'),
  ('kafka.record.abc',
   'e:\\python\\lib\\site-packages\\kafka\\record\\abc.py',
   'PYMODULE'),
  ('kafka.record.default_records',
   'e:\\python\\lib\\site-packages\\kafka\\record\\default_records.py',
   'PYMODULE'),
  ('kafka.producer.transaction_manager',
   'e:\\python\\lib\\site-packages\\kafka\\producer\\transaction_manager.py',
   'PYMODULE'),
  ('kafka.protocol.txn_offset_commit',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\txn_offset_commit.py',
   'PYMODULE'),
  ('kafka.protocol.init_producer_id',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\init_producer_id.py',
   'PYMODULE'),
  ('kafka.protocol.end_txn',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\end_txn.py',
   'PYMODULE'),
  ('kafka.protocol.add_partitions_to_txn',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\add_partitions_to_txn.py',
   'PYMODULE'),
  ('kafka.protocol.add_offsets_to_txn',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\add_offsets_to_txn.py',
   'PYMODULE'),
  ('kafka.producer.sender',
   'e:\\python\\lib\\site-packages\\kafka\\producer\\sender.py',
   'PYMODULE'),
  ('kafka.producer.record_accumulator',
   'e:\\python\\lib\\site-packages\\kafka\\producer\\record_accumulator.py',
   'PYMODULE'),
  ('kafka.producer.future',
   'e:\\python\\lib\\site-packages\\kafka\\producer\\future.py',
   'PYMODULE'),
  ('kafka.partitioner.default',
   'e:\\python\\lib\\site-packages\\kafka\\partitioner\\default.py',
   'PYMODULE'),
  ('kafka.partitioner',
   'e:\\python\\lib\\site-packages\\kafka\\partitioner\\__init__.py',
   'PYMODULE'),
  ('kafka.codec',
   'e:\\python\\lib\\site-packages\\kafka\\codec.py',
   'PYMODULE'),
  ('kafka.consumer.subscription_state',
   'e:\\python\\lib\\site-packages\\kafka\\consumer\\subscription_state.py',
   'PYMODULE'),
  ('kafka.consumer',
   'e:\\python\\lib\\site-packages\\kafka\\consumer\\__init__.py',
   'PYMODULE'),
  ('kafka.consumer.group',
   'e:\\python\\lib\\site-packages\\kafka\\consumer\\group.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors.roundrobin',
   'e:\\python\\lib\\site-packages\\kafka\\coordinator\\assignors\\roundrobin.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors',
   'e:\\python\\lib\\site-packages\\kafka\\coordinator\\assignors\\__init__.py',
   'PYMODULE'),
  ('kafka.coordinator',
   'e:\\python\\lib\\site-packages\\kafka\\coordinator\\__init__.py',
   'PYMODULE'),
  ('kafka.coordinator.protocol',
   'e:\\python\\lib\\site-packages\\kafka\\coordinator\\protocol.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors.abstract',
   'e:\\python\\lib\\site-packages\\kafka\\coordinator\\assignors\\abstract.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors.range',
   'e:\\python\\lib\\site-packages\\kafka\\coordinator\\assignors\\range.py',
   'PYMODULE'),
  ('kafka.coordinator.consumer',
   'e:\\python\\lib\\site-packages\\kafka\\coordinator\\consumer.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors.sticky.sticky_assignor',
   'e:\\python\\lib\\site-packages\\kafka\\coordinator\\assignors\\sticky\\sticky_assignor.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors.sticky',
   'e:\\python\\lib\\site-packages\\kafka\\coordinator\\assignors\\sticky\\__init__.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors.sticky.sorted_set',
   'e:\\python\\lib\\site-packages\\kafka\\coordinator\\assignors\\sticky\\sorted_set.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors.sticky.partition_movements',
   'e:\\python\\lib\\site-packages\\kafka\\coordinator\\assignors\\sticky\\partition_movements.py',
   'PYMODULE'),
  ('kafka.coordinator.base',
   'e:\\python\\lib\\site-packages\\kafka\\coordinator\\base.py',
   'PYMODULE'),
  ('kafka.protocol.group',
   'e:\\python\\lib\\site-packages\\kafka\\protocol\\group.py',
   'PYMODULE'),
  ('kafka.coordinator.heartbeat',
   'e:\\python\\lib\\site-packages\\kafka\\coordinator\\heartbeat.py',
   'PYMODULE'),
  ('kafka.consumer.fetcher',
   'e:\\python\\lib\\site-packages\\kafka\\consumer\\fetcher.py',
   'PYMODULE'),
  ('kafka.client_async',
   'e:\\python\\lib\\site-packages\\kafka\\client_async.py',
   'PYMODULE'),
  ('kafka.cluster',
   'e:\\python\\lib\\site-packages\\kafka\\cluster.py',
   'PYMODULE'),
  ('kafka.admin',
   'e:\\python\\lib\\site-packages\\kafka\\admin\\__init__.py',
   'PYMODULE'),
  ('kafka.admin.new_partitions',
   'e:\\python\\lib\\site-packages\\kafka\\admin\\new_partitions.py',
   'PYMODULE'),
  ('kafka.admin.new_topic',
   'e:\\python\\lib\\site-packages\\kafka\\admin\\new_topic.py',
   'PYMODULE'),
  ('kafka.admin.acl_resource',
   'e:\\python\\lib\\site-packages\\kafka\\admin\\acl_resource.py',
   'PYMODULE'),
  ('kafka.admin.client',
   'e:\\python\\lib\\site-packages\\kafka\\admin\\client.py',
   'PYMODULE'),
  ('kafka.admin.config_resource',
   'e:\\python\\lib\\site-packages\\kafka\\admin\\config_resource.py',
   'PYMODULE'),
  ('kafka.version',
   'e:\\python\\lib\\site-packages\\kafka\\version.py',
   'PYMODULE'),
  ('_py_abc', 'e:\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'e:\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('getpass', 'e:\\python\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'e:\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'e:\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('tracemalloc', 'e:\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('mes_api', 'E:\\vscode\\tomesmo\\mes_api.py', 'PYMODULE'),
  ('pqm_api', 'E:\\vscode\\tomesmo\\pqm_api.py', 'PYMODULE'),
  ('fake_data_generator',
   'E:\\vscode\\tomesmo\\fake_data_generator.py',
   'PYMODULE'),
  ('test_config', 'E:\\vscode\\tomesmo\\test_config.py', 'PYMODULE'),
  ('test_data_generator',
   'E:\\vscode\\tomesmo\\test_data_generator.py',
   'PYMODULE'),
  ('threading', 'e:\\python\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'e:\\python\\lib\\_threading_local.py', 'PYMODULE'),
  ('rabbitmq_monitor', 'E:\\vscode\\tomesmo\\rabbitmq_monitor.py', 'PYMODULE'),
  ('rabbitmq_config_manager',
   'E:\\vscode\\tomesmo\\rabbitmq_config_manager.py',
   'PYMODULE'),
  ('config', 'E:\\vscode\\tomesmo\\config.py', 'PYMODULE'),
  ('kafka_config_manager',
   'E:\\vscode\\tomesmo\\kafka_config_manager.py',
   'PYMODULE'),
  ('kafka_monitor', 'E:\\vscode\\tomesmo\\kafka_monitor.py', 'PYMODULE'),
  ('mes_forwarder', 'E:\\vscode\\tomesmo\\mes_forwarder.py', 'PYMODULE'),
  ('device_manager', 'E:\\vscode\\tomesmo\\device_manager.py', 'PYMODULE'),
  ('datetime', 'e:\\python\\lib\\datetime.py', 'PYMODULE'),
  ('json', 'e:\\python\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'e:\\python\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'e:\\python\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'e:\\python\\lib\\json\\scanner.py', 'PYMODULE'),
  ('flask', 'e:\\python\\lib\\site-packages\\flask\\__init__.py', 'PYMODULE'),
  ('flask.templating',
   'e:\\python\\lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'e:\\python\\lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.scaffold',
   'e:\\python\\lib\\site-packages\\flask\\scaffold.py',
   'PYMODULE'),
  ('flask.wrappers',
   'e:\\python\\lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'e:\\python\\lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug',
   'e:\\python\\lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.test',
   'e:\\python\\lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'e:\\python\\lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'e:\\python\\lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'e:\\python\\lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'e:\\python\\lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'e:\\python\\lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'e:\\python\\lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'e:\\python\\lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'e:\\python\\lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'e:\\python\\lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'e:\\python\\lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'e:\\python\\lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'e:\\python\\lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('secrets', 'e:\\python\\lib\\secrets.py', 'PYMODULE'),
  ('werkzeug.urls',
   'e:\\python\\lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'e:\\python\\lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('dataclasses', 'e:\\python\\lib\\dataclasses.py', 'PYMODULE'),
  ('werkzeug.http',
   'e:\\python\\lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'e:\\python\\lib\\site-packages\\werkzeug\\datastructures.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'e:\\python\\lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('colorama',
   'e:\\python\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'e:\\python\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'e:\\python\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'e:\\python\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'e:\\python\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'e:\\python\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'e:\\python\\lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'e:\\python\\lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'e:\\python\\lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'e:\\python\\lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('codeop', 'e:\\python\\lib\\codeop.py', 'PYMODULE'),
  ('werkzeug.debug.repr',
   'e:\\python\\lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('pydoc', 'e:\\python\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'e:\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('glob', 'e:\\python\\lib\\glob.py', 'PYMODULE'),
  ('pydoc_data.topics', 'e:\\python\\lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pydoc_data', 'e:\\python\\lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('tty', 'e:\\python\\lib\\tty.py', 'PYMODULE'),
  ('sysconfig', 'e:\\python\\lib\\sysconfig.py', 'PYMODULE'),
  ('code', 'e:\\python\\lib\\code.py', 'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'e:\\python\\lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'e:\\python\\lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'e:\\python\\lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('http.server', 'e:\\python\\lib\\http\\server.py', 'PYMODULE'),
  ('html', 'e:\\python\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'e:\\python\\lib\\html\\entities.py', 'PYMODULE'),
  ('socketserver', 'e:\\python\\lib\\socketserver.py', 'PYMODULE'),
  ('werkzeug.routing.rules',
   'e:\\python\\lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'e:\\python\\lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'e:\\python\\lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'e:\\python\\lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('difflib', 'e:\\python\\lib\\difflib.py', 'PYMODULE'),
  ('werkzeug.routing.converters',
   'e:\\python\\lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'e:\\python\\lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'e:\\python\\lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('jinja2', 'e:\\python\\lib\\site-packages\\jinja2\\__init__.py', 'PYMODULE'),
  ('jinja2.ext', 'e:\\python\\lib\\site-packages\\jinja2\\ext.py', 'PYMODULE'),
  ('jinja2.parser',
   'e:\\python\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'e:\\python\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'e:\\python\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'e:\\python\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   'e:\\python\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   'e:\\python\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'e:\\python\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'e:\\python\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.utils',
   'e:\\python\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   'e:\\python\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'e:\\python\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'e:\\python\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'e:\\python\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   'e:\\python\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('asyncio', 'e:\\python\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'e:\\python\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'e:\\python\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'e:\\python\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'e:\\python\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'e:\\python\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'e:\\python\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'e:\\python\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess', 'e:\\python\\lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.streams', 'e:\\python\\lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.queues', 'e:\\python\\lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'e:\\python\\lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.tasks', 'e:\\python\\lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('concurrent.futures',
   'e:\\python\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'e:\\python\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'e:\\python\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'e:\\python\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'e:\\python\\lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('asyncio.locks', 'e:\\python\\lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.base_tasks', 'e:\\python\\lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.sslproto', 'e:\\python\\lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.transports', 'e:\\python\\lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.base_events',
   'e:\\python\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'e:\\python\\lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.futures', 'e:\\python\\lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.events', 'e:\\python\\lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.coroutines', 'e:\\python\\lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.base_futures',
   'e:\\python\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'e:\\python\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants', 'e:\\python\\lib\\asyncio\\constants.py', 'PYMODULE'),
  ('jinja2.debug',
   'e:\\python\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'e:\\python\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'e:\\python\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'e:\\python\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'e:\\python\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'e:\\python\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'e:\\python\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('flask.signals',
   'e:\\python\\lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.helpers',
   'e:\\python\\lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.globals',
   'e:\\python\\lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.sessions',
   'e:\\python\\lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.json.tag',
   'e:\\python\\lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('itsdangerous',
   'e:\\python\\lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'e:\\python\\lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'e:\\python\\lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'e:\\python\\lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'e:\\python\\lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'e:\\python\\lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'e:\\python\\lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'e:\\python\\lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('werkzeug.local',
   'e:\\python\\lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('flask.ctx', 'e:\\python\\lib\\site-packages\\flask\\ctx.py', 'PYMODULE'),
  ('flask.config',
   'e:\\python\\lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.blueprints',
   'e:\\python\\lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.app', 'e:\\python\\lib\\site-packages\\flask\\app.py', 'PYMODULE'),
  ('flask.testing',
   'e:\\python\\lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('click.testing',
   'e:\\python\\lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.core', 'e:\\python\\lib\\site-packages\\click\\core.py', 'PYMODULE'),
  ('click.shell_completion',
   'e:\\python\\lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.decorators',
   'e:\\python\\lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('importlib_metadata',
   'e:\\python\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'e:\\python\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'e:\\python\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'e:\\python\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'e:\\python\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._py39compat',
   'e:\\python\\lib\\site-packages\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'e:\\python\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'e:\\python\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'e:\\python\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('zipp', 'e:\\python\\lib\\site-packages\\zipp\\__init__.py', 'PYMODULE'),
  ('zipp.py310compat',
   'e:\\python\\lib\\site-packages\\zipp\\py310compat.py',
   'PYMODULE'),
  ('csv', 'e:\\python\\lib\\csv.py', 'PYMODULE'),
  ('click.parser',
   'e:\\python\\lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.globals',
   'e:\\python\\lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.exceptions',
   'e:\\python\\lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.types',
   'e:\\python\\lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'e:\\python\\lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('click.termui',
   'e:\\python\\lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   'e:\\python\\lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click.formatting',
   'e:\\python\\lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   'e:\\python\\lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._compat',
   'e:\\python\\lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._winconsole',
   'e:\\python\\lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('flask.logging',
   'e:\\python\\lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.json.provider',
   'e:\\python\\lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('click', 'e:\\python\\lib\\site-packages\\click\\__init__.py', 'PYMODULE'),
  ('flask.cli', 'e:\\python\\lib\\site-packages\\flask\\cli.py', 'PYMODULE'),
  ('rlcompleter', 'e:\\python\\lib\\rlcompleter.py', 'PYMODULE'),
  ('flask.typing',
   'e:\\python\\lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.json',
   'e:\\python\\lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('markupsafe',
   'e:\\python\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'e:\\python\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE')],
 [('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('python37.dll', 'e:\\python\\python37.dll', 'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'e:\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\WINDOWS\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('select.pyd', 'e:\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('pyexpat.pyd', 'e:\\python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'e:\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'e:\\python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'e:\\python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'e:\\python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'e:\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'e:\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_socket.pyd', 'e:\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'e:\\python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'e:\\python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'e:\\python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp37-win32.pyd',
   'e:\\python\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp37-win32.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp37-win32.pyd',
   'e:\\python\\lib\\site-packages\\charset_normalizer\\md.cp37-win32.pyd',
   'EXTENSION'),
  ('_overlapped.pyd', 'e:\\python\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'e:\\python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp37-win32.pyd',
   'e:\\python\\lib\\site-packages\\markupsafe\\_speedups.cp37-win32.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libssl-1_1.dll', 'e:\\python\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('libcrypto-1_1.dll', 'e:\\python\\DLLs\\libcrypto-1_1.dll', 'BINARY')],
 [],
 [],
 [('devices.json', 'E:\\vscode\\tomesmo\\devices.json', 'DATA'),
  ('fake_data_generator.py',
   'E:\\vscode\\tomesmo\\fake_data_generator.py',
   'DATA'),
  ('kafka_config.json', 'E:\\vscode\\tomesmo\\kafka_config.json', 'DATA'),
  ('pqm_api.py', 'E:\\vscode\\tomesmo\\pqm_api.py', 'DATA'),
  ('rabbitmq_config.json', 'E:\\vscode\\tomesmo\\rabbitmq_config.json', 'DATA'),
  ('static\\logo.png', 'E:\\vscode\\tomesmo\\static\\logo.png', 'DATA'),
  ('static\\script.js', 'E:\\vscode\\tomesmo\\static\\script.js', 'DATA'),
  ('static\\style.css', 'E:\\vscode\\tomesmo\\static\\style.css', 'DATA'),
  ('templates\\index.html',
   'E:\\vscode\\tomesmo\\templates\\index.html',
   'DATA'),
  ('test_config.py', 'E:\\vscode\\tomesmo\\test_config.py', 'DATA'),
  ('test_data_generator.py',
   'E:\\vscode\\tomesmo\\test_data_generator.py',
   'DATA'),
  ('base_library.zip',
   'E:\\vscode\\tomesmo\\build\\mes_upload_manager\\base_library.zip',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\LICENSE',
   'e:\\python\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'e:\\python\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\RECORD',
   'e:\\python\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\METADATA',
   'e:\\python\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'e:\\python\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'e:\\python\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'e:\\python\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\WHEEL',
   'e:\\python\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\WHEEL',
   'DATA')],
 [])
