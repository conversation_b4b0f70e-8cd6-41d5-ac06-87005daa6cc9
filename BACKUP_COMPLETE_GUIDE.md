# MES設備數據轉發管理系統 - 完整備份指南

## 📦 備份概述

**備份時間**: 2025-06-16 14:06:56  
**備份文件**: `project_backup_20250616_140644.zip` (25.8 MB)  
**備份目錄**: `complete_backup_20250616_140644/`  
**Python版本**: 3.7.9 (32-bit)  

## 📋 備份內容清單

### 🔧 核心應用文件
- `app.py` - 主要Flask應用程序
- `start.py` - 應用啟動腳本
- `open_browser.py` - 瀏覽器自動開啟工具
- `config.py` - 基礎配置模組

### 📱 設備管理模組
- `device_manager.py` - 設備管理核心模組
- `devices.json` - 設備配置數據文件

### 📡 數據源監控模組
- `kafka_monitor.py` - Kafka數據監控
- `kafka_config_manager.py` - Kafka配置管理
- `kafka_config.json` - Kafka連接配置
- `rabbitmq_monitor.py` - RabbitMQ數據監控
- `rabbitmq_config_manager.py` - RabbitMQ配置管理
- `rabbitmq_config.json` - RabbitMQ連接配置
- `getdata.py` - 數據獲取參考實現
- `getdatafromrabbitmq.py` - RabbitMQ數據獲取

### 🔗 MES集成模組
- `mes_api.py` - MES API接口實現
- `mes_forwarder.py` - MES數據轉發器
- `MESnewtdc.py` - MES參考實現

### 🎨 前端界面
- `static/` - 靜態資源目錄
  - `logo.png` - 系統Logo
  - `script.js` - 前端JavaScript
  - `style.css` - 樣式表
- `templates/` - HTML模板目錄
  - `index.html` - 主頁面模板

### 🧪 測試和開發工具
- `test_config.py` - 測試配置
- `test_data_generator.py` - 測試數據生成器
- `test_data_simulator.py` - 數據模擬器
- `fake_data_generator.py` - 模擬數據生成器
- `test_devices_*.json` - 測試設備配置文件 (40/50/60/70/100設備)

### 🔄 數據遷移工具
- `migrate_devices.py` - 設備數據遷移
- `migrate_forwarding_stats.py` - 轉發統計遷移
- `migrate_period_logs.py` - 期間日誌遷移

### 📦 構建和部署
- `build_exe.py` - 可執行文件構建腳本
- `build_english_exe.py` - 英文版構建腳本
- `create_zip.py` - ZIP包創建腳本
- `create_english_zip.py` - 英文版ZIP包創建
- `create_docker_package.py` - Docker包創建
- `*.spec` - PyInstaller規格文件

### 💾 已編譯文件
- `dist/` - 編譯輸出目錄
  - `MES_Upload_Manager.exe` - 主程序可執行文件
  - `Open_Browser.exe` - 瀏覽器開啟工具
- `MES_Upload_Manager_v0.0.1-beta-1/` - 完整部署包

### 📚 文檔和說明
- `README.md` - 項目主要說明文檔
- `README_RabbitMQ.md` - RabbitMQ配置說明
- `AI編程項目大綱範例.md` - 項目開發大綱
- `BACKUP_SUMMARY.md` - 備份摘要
- `COMPLETE_FEATURE_SUMMARY.md` - 完整功能摘要
- `FEATURE_UPDATE.md` - 功能更新記錄
- `FINAL_FEATURE_SUMMARY.md` - 最終功能摘要
- `UPDATE_SUMMARY.md` - 更新摘要

### 🔧 環境和依賴信息
- `requirements.txt` - 基本Python依賴
- `pip_freeze.txt` - 完整依賴列表
- `python_version.txt` - Python版本信息
- `environment_info.json` - 環境配置信息
- `backup_info.json` - 備份詳細信息

### 🛠️ 恢復和安裝工具
- `restore_from_backup_20250616_140644.py` - 項目恢復腳本
- `install_dependencies.py` - 依賴安裝腳本

## 🚀 恢復和部署指南

### 1. 解壓備份文件
```bash
# 解壓到目標目錄
unzip project_backup_20250616_140644.zip
cd complete_backup_20250616_140644
```

### 2. 環境準備
```bash
# 檢查Python版本（建議使用Python 3.7+）
python --version

# 創建虛擬環境（可選但推薦）
python -m venv venv
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac
```

### 3. 安裝依賴
```bash
# 方法1: 使用自動安裝腳本
python install_dependencies.py

# 方法2: 手動安裝
pip install -r requirements.txt

# 方法3: 使用完整依賴列表
pip install -r pip_freeze.txt
```

### 4. 運行應用
```bash
# 開發模式運行
python app.py

# 或使用啟動腳本
python start.py

# 或直接運行可執行文件
dist\MES_Upload_Manager.exe
```

### 5. 配置調整
根據實際環境調整以下配置文件：
- `kafka_config.json` - Kafka服務器配置
- `rabbitmq_config.json` - RabbitMQ服務器配置
- `devices.json` - 設備配置

## 📊 系統要求

### 最低要求
- **操作系統**: Windows 10/11, Ubuntu 20.04+
- **Python版本**: 3.7+
- **內存**: 2GB RAM
- **存儲**: 500MB 可用空間

### 推薦配置
- **操作系統**: Windows 10/11
- **Python版本**: 3.7.9 或 3.8+
- **內存**: 4GB+ RAM
- **存儲**: 1GB+ 可用空間

### 網絡要求
- 能夠訪問Kafka服務器（如果使用Kafka）
- 能夠訪問RabbitMQ服務器（如果使用RabbitMQ）
- 能夠訪問MES系統API

## 🔧 故障排除

### 常見問題

1. **依賴安裝失敗**
   - 檢查Python版本是否兼容
   - 嘗試升級pip: `python -m pip install --upgrade pip`
   - 使用pip_freeze.txt安裝完整依賴

2. **無法連接Kafka/RabbitMQ**
   - 檢查網絡連接
   - 驗證配置文件中的服務器地址和端口
   - 確認服務器防火牆設置

3. **MES API連接失敗**
   - 檢查MES系統可用性
   - 驗證API端點和認證信息
   - 查看錯誤日誌獲取詳細信息

### 日誌和調試
- 應用運行日誌會顯示在控制台
- 檢查`rabbitmq_consumer.log`（如果存在）
- 使用測試模式進行功能驗證

## 📞 技術支持

如需技術支持，請提供以下信息：
- 備份版本: `project_backup_20250616_140644.zip`
- Python版本: 從`python_version.txt`獲取
- 錯誤信息: 完整的錯誤堆棧跟踪
- 環境信息: 從`environment_info.json`獲取

---

**備份創建時間**: 2025-06-16 14:06:56  
**備份工具版本**: backup_complete_project.py v2.0  
**項目版本**: MES_Upload_Manager v0.0.1-beta-1
