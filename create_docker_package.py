#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建Docker部署包压缩文件
"""

import zipfile
import os
from pathlib import Path

def create_docker_deployment_package():
    """创建Docker部署包压缩文件"""
    deploy_dir = Path('MES_Docker_Deploy')
    zip_name = 'MES_Docker_K8s_Deploy_v1.0.zip'
    
    if not deploy_dir.exists():
        print("❌ Docker deployment directory not found")
        return False
    
    print("📦 Creating Docker & Kubernetes deployment package...")
    print("🐳 Target: Ubuntu 20.04.6 LTS with Docker & K8s")
    print("")
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in deploy_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(deploy_dir.parent)
                zipf.write(file_path, arcname)
                print(f"  ✅ Added: {arcname}")
    
    # 检查压缩包大小
    zip_size = os.path.getsize(zip_name) / (1024 * 1024)  # MB
    print(f"\n🎉 Docker deployment package created!")
    print(f"📁 Filename: {zip_name}")
    print(f"📏 Size: {zip_size:.1f} MB")
    print(f"🐧 Platform: Linux (Ubuntu 20.04.6 LTS)")
    print(f"🐳 Container: Docker + Kubernetes")
    print("")
    print("📋 Package includes:")
    print("  - Dockerfile for image building")
    print("  - Kubernetes deployment & service configs")
    print("  - Build and deployment scripts")
    print("  - Complete application source code")
    print("  - Documentation and metadata")
    print("")
    print("🚀 Quick start:")
    print("  1. Extract package on Ubuntu system")
    print("  2. Run: chmod +x *.sh")
    print("  3. Run: ./build_docker.sh")
    print("  4. Run: ./deploy_k8s.sh")
    
    return True

if __name__ == '__main__':
    create_docker_deployment_package()
