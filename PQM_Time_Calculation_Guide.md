# PQM時間計算邏輯說明

## 概述
本文檔說明MES系統中PQM上傳功能的時間計算邏輯，包括`runningTime`和`cycleTime`的計算方式。

## 時間計算邏輯

### 1. runningTime（設備運行時間）

**計算方式**: 從啟動MES轉發功能開始計算到當前時間的秒數差值

**觸發時機**:
- ✅ **啟動MES轉發**: 記錄當前時間作為起點
- ⏱️ **上傳PQM**: 計算當前時間與起點的差值
- 🛑 **停止MES轉發**: 清除起點時間
- 🔄 **重新啟動**: 重新記錄新的起點時間

**示例**:
```
14:00:00 - 啟動MES轉發 (記錄起點)
14:05:30 - 上傳PQM → runningTime = 330秒
14:10:15 - 上傳PQM → runningTime = 615秒
14:15:00 - 停止MES轉發 (清除起點)
14:20:00 - 重新啟動MES轉發 (記錄新起點)
14:25:30 - 上傳PQM → runningTime = 330秒 (重新計算)
```

### 2. cycleTime（產品生產週期時間）

**計算方式**: 
- **首次產品**: 默認為0.0秒
- **後續產品**: 當前產品觸發時間 - 上一個產品觸發時間

**觸發時機**:
- 🚀 **啟動MES轉發**: 清除上次產品時間記錄
- 📦 **首次產品生產**: cycleTime = 0.0秒，記錄本次時間
- 📦 **後續產品生產**: 計算與上次產品的時間差，更新本次時間
- 🛑 **停止MES轉發**: 清除產品時間記錄

**示例**:
```
14:00:00 - 啟動MES轉發 (清除產品時間記錄)
14:02:00 - 第1個產品 → cycleTime = 0.0秒 (首次)
14:04:30 - 第2個產品 → cycleTime = 150秒 (2分30秒)
14:06:45 - 第3個產品 → cycleTime = 135秒 (2分15秒)
14:10:00 - 停止MES轉發 (清除記錄)
14:15:00 - 重新啟動MES轉發
14:17:00 - 第1個產品 → cycleTime = 0.0秒 (重新開始)
```

## 實現細節

### 設備管理器新增方法

#### `get_device_running_time(device_id)`
```python
def get_device_running_time(self, device_id):
    """獲取設備運行時間（秒）"""
    device = self.devices[device_id]
    mes_start_time = device.get('mesStartTime')
    
    if not mes_start_time:
        return 0.0
    
    start_time = datetime.fromisoformat(mes_start_time)
    current_time = datetime.now()
    running_seconds = (current_time - start_time).total_seconds()
    return max(0.0, running_seconds)
```

#### `get_and_update_cycle_time(device_id)`
```python
def get_and_update_cycle_time(self, device_id):
    """獲取並更新產品生產週期時間（秒）"""
    device = self.devices[device_id]
    current_time = datetime.now()
    last_product_time = device.get('lastProductTime')
    
    if not last_product_time:
        # 首次產品，cycleTime為0
        cycle_time = 0.0
    else:
        last_time = datetime.fromisoformat(last_product_time)
        cycle_time = (current_time - last_time).total_seconds()
    
    # 更新本次產品時間
    device['lastProductTime'] = current_time.isoformat()
    return cycle_time
```

### 設備數據結構新增字段

```python
device_info = {
    # ... 原有字段 ...
    'mesStartTime': None,      # MES轉發開始時間（ISO格式）
    'lastProductTime': None    # 上一個產品生產時間（ISO格式）
}
```

## 使用流程

### 1. 啟動MES轉發
```python
device_manager.toggle_device_monitoring(device_id, True)
# → 設置 mesStartTime = 當前時間
# → 清除 lastProductTime = None
```

### 2. 產品生產觸發PQM上傳
```python
# 在MES上傳成功後自動調用
running_time = device_manager.get_device_running_time(device_id)
cycle_time = device_manager.get_and_update_cycle_time(device_id)
# → 計算時間並上傳到PQM
```

### 3. 停止MES轉發
```python
device_manager.toggle_device_monitoring(device_id, False)
# → 清除 mesStartTime = None
# → 清除 lastProductTime = None
```

## 測試驗證

系統包含完整的時間計算測試，驗證以下場景：
- ✅ 未啟動MES時時間為0
- ✅ 啟動MES後runningTime正確累計
- ✅ 首次產品cycleTime為0
- ✅ 後續產品cycleTime正確計算時間差
- ✅ 停止並重新啟動後時間正確重置

運行測試：
```bash
python test_pqm_integration.py
```

## 注意事項

1. **時間精度**: 使用秒為單位，保留小數點
2. **異常處理**: 時間計算失敗時返回0.0
3. **數據持久化**: 時間記錄保存在設備配置文件中
4. **重啟恢復**: 程序重啟後需要重新啟動MES轉發才能開始計時
5. **時區處理**: 使用系統本地時間，確保一致性

## 更新日誌

**v0.0.1-beta-1 (PQM_Fixed_Time)**
- ✅ 簡化runningTime計算邏輯，使用MES轉發啟動時間作為起點
- ✅ 修正cycleTime計算，首次產品為0秒，後續計算時間差
- ✅ 添加完整的時間計算測試驗證
- ✅ 移除複雜的設備創建時間追蹤邏輯
- ✅ 優化代碼結構，提高可維護性
