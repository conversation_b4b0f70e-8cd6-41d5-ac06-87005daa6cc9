{"S720050001": {"deviceId": "S720050001", "lineName": "B2-01", "sectionName": "TESTING", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 27, "forwardedCount": 15, "unforwardedCount": 12, "lastLog": "測試產品條碼-eb5a1dd7", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-01-19T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO365602", "targetQuantity": 462, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-26T16:59:31.463859", "status": "active", "mesForwardedCount": 84, "completionRate": 71.0}, {"workOrderNumber": "WO528924", "targetQuantity": 96, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-28T16:59:31.463859", "status": "completed", "mesForwardedCount": 61, "completionRate": 1.8}, {"workOrderNumber": "WO509468", "targetQuantity": 388, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-12T16:59:31.463859", "status": "completed", "mesForwardedCount": 91, "completionRate": 7.7}], "currentWorkOrder": 2, "currentWorkOrderProgress": 25, "displayWorkOrder": {"workOrderNumber": "WO509468", "targetQuantity": 388, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-12T16:59:31.463859", "status": "completed", "mesForwardedCount": 91, "completionRate": 7.7}, "mesErrors": [{"timestamp": "2025-06-13T01:25:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050001"}}, {"timestamp": "2025-06-12T20:59:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050001"}}, {"timestamp": "2025-06-12T19:41:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050001"}}], "recentLogs": [{"message": "測試條碼-89b71662", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-41616dbe", "timestamp": "2025-06-13T16:04:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-16c1bffd", "timestamp": "2025-06-13T16:52:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-0bce69ae", "timestamp": "2025-06-13T16:50:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-089d2b3e", "timestamp": "2025-06-13T16:01:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-2fc5e050", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-fa2b4df9", "timestamp": "2025-06-13T16:04:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-cd940b94", "timestamp": "2025-06-13T16:18:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-dd491596", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-50db9aea", "timestamp": "2025-06-13T16:44:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-caede468", "timestamp": "2025-06-13T16:11:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-b028c17e", "timestamp": "2025-06-13T16:00:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f3cc831f", "timestamp": "2025-06-13T16:06:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-2464f65f", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-92a56b49", "timestamp": "2025-06-13T16:16:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-b3efe481", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-83c6bada", "timestamp": "2025-06-13T16:35:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}]}, "S720050002": {"deviceId": "S720050002", "lineName": "B1-02", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 871, "forwardedCount": 804, "unforwardedCount": 67, "lastLog": "測試產品條碼-c5ca46b6", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-06-06T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO635121", "targetQuantity": 268, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-18T16:59:31.463859", "status": "pending", "mesForwardedCount": 64, "completionRate": 92.2}, {"workOrderNumber": "WO375681", "targetQuantity": 435, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-31T16:59:31.463859", "status": "active", "mesForwardedCount": 82, "completionRate": 91.0}, {"workOrderNumber": "WO407198", "targetQuantity": 113, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-16T16:59:31.463859", "status": "pending", "mesForwardedCount": 2, "completionRate": 68.7}], "currentWorkOrder": 2, "currentWorkOrderProgress": 39, "displayWorkOrder": {"workOrderNumber": "WO407198", "targetQuantity": 113, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-16T16:59:31.463859", "status": "pending", "mesForwardedCount": 2, "completionRate": 68.7}, "mesErrors": [{"timestamp": "2025-06-13T09:49:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050002"}}, {"timestamp": "2025-06-13T08:11:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050002"}}, {"timestamp": "2025-06-12T23:51:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050002"}}, {"timestamp": "2025-06-13T04:33:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050002"}}, {"timestamp": "2025-06-13T14:11:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050002"}}], "recentLogs": [{"message": "測試條碼-04f1b106", "timestamp": "2025-06-13T16:04:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-0f1ed9ae", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-28010ea1", "timestamp": "2025-06-13T16:10:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-e32feb8c", "timestamp": "2025-06-13T16:43:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-1fc7b326", "timestamp": "2025-06-13T16:43:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-aa635d4c", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-58039f3d", "timestamp": "2025-06-13T16:39:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-440af8f8", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-13a0b212", "timestamp": "2025-06-13T16:39:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-185b7fc7", "timestamp": "2025-06-13T16:37:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-109e9d20", "timestamp": "2025-06-13T16:09:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}]}, "S720050003": {"deviceId": "S720050003", "lineName": "B1-02", "sectionName": "ASSEMBLY", "groupName": "ASSEMBLY", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 630, "forwardedCount": 69, "unforwardedCount": 561, "lastLog": "測試產品條碼-02df98f0", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-08-24T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO996172", "targetQuantity": 77, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-23T16:59:31.463859", "status": "active", "mesForwardedCount": 3, "completionRate": 37.9}, {"workOrderNumber": "WO562512", "targetQuantity": 104, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-20T16:59:31.463859", "status": "completed", "mesForwardedCount": 15, "completionRate": 16.4}, {"workOrderNumber": "WO988020", "targetQuantity": 440, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-10T16:59:31.463859", "status": "pending", "mesForwardedCount": 83, "completionRate": 91.1}], "currentWorkOrder": 2, "currentWorkOrderProgress": 40, "displayWorkOrder": {"workOrderNumber": "WO988020", "targetQuantity": 440, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-10T16:59:31.463859", "status": "pending", "mesForwardedCount": 83, "completionRate": 91.1}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-827da47a", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-1f6a0b13", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-7961ff6b", "timestamp": "2025-06-13T16:23:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-85cd46f3", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-29d33ce0", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-f5b7b517", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-0c2ceae7", "timestamp": "2025-06-13T16:44:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-82240c50", "timestamp": "2025-06-13T16:09:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-ae69dd0d", "timestamp": "2025-06-13T16:01:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-b4df4a6d", "timestamp": "2025-06-13T16:22:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-8156b200", "timestamp": "2025-06-13T16:16:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-52663b7e", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-2a3ea844", "timestamp": "2025-06-13T16:45:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}]}, "S720050004": {"deviceId": "S720050004", "lineName": "A2-01", "sectionName": "TESTING", "groupName": "TEST", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 529, "forwardedCount": 218, "unforwardedCount": 311, "lastLog": "測試產品條碼-b83118db", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-05-18T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO458583", "targetQuantity": 384, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-23T16:59:31.463859", "status": "completed", "mesForwardedCount": 34, "completionRate": 62.2}, {"workOrderNumber": "WO190636", "targetQuantity": 358, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-13T16:59:31.463859", "status": "completed", "mesForwardedCount": 78, "completionRate": 30.7}, {"workOrderNumber": "WO922982", "targetQuantity": 448, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-05T16:59:31.463859", "status": "active", "mesForwardedCount": 80, "completionRate": 9.7}], "currentWorkOrder": 2, "currentWorkOrderProgress": 17, "displayWorkOrder": {"workOrderNumber": "WO922982", "targetQuantity": 448, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-05T16:59:31.463859", "status": "active", "mesForwardedCount": 80, "completionRate": 9.7}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-8258daec", "timestamp": "2025-06-13T16:44:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-41af3751", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-fb23d20f", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-a5b27d19", "timestamp": "2025-06-13T16:22:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-8024a80b", "timestamp": "2025-06-13T16:09:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-720c8b29", "timestamp": "2025-06-13T16:37:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}]}, "S720050005": {"deviceId": "S720050005", "lineName": "A2-01", "sectionName": "ASSEMBLY", "groupName": "INJECTION", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 228, "forwardedCount": 82, "unforwardedCount": 146, "lastLog": "測試產品條碼-945e5f2b", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-10-03T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO950069", "targetQuantity": 454, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-27T16:59:31.463859", "status": "active", "mesForwardedCount": 67, "completionRate": 52.4}, {"workOrderNumber": "WO848390", "targetQuantity": 294, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-31T16:59:31.463859", "status": "pending", "mesForwardedCount": 43, "completionRate": 68.8}, {"workOrderNumber": "WO799984", "targetQuantity": 372, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-31T16:59:31.463859", "status": "completed", "mesForwardedCount": 75, "completionRate": 7.9}], "currentWorkOrder": 0, "currentWorkOrderProgress": 56, "displayWorkOrder": {"workOrderNumber": "WO950069", "targetQuantity": 454, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-27T16:59:31.463859", "status": "active", "mesForwardedCount": 67, "completionRate": 52.4}, "mesErrors": [{"timestamp": "2025-06-13T13:28:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050005"}}, {"timestamp": "2025-06-13T00:53:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050005"}}, {"timestamp": "2025-06-13T05:06:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050005"}}, {"timestamp": "2025-06-13T08:01:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050005"}}, {"timestamp": "2025-06-13T10:47:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050005"}}], "recentLogs": [{"message": "測試條碼-f9ff1eb4", "timestamp": "2025-06-13T16:40:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-20481251", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a09ac56b", "timestamp": "2025-06-13T16:18:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-62ebdff1", "timestamp": "2025-06-13T16:03:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-f15ac374", "timestamp": "2025-06-13T16:48:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-c6754e90", "timestamp": "2025-06-13T16:41:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-a24c8aa0", "timestamp": "2025-06-13T15:59:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}]}, "S720050006": {"deviceId": "S720050006", "lineName": "B1-02", "sectionName": "TESTING", "groupName": "INJECTION", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 989, "forwardedCount": 547, "unforwardedCount": 442, "lastLog": "測試產品條碼-36694b13", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-11-11T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO393068", "targetQuantity": 307, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-13T16:59:31.463859", "status": "completed", "mesForwardedCount": 47, "completionRate": 18.1}, {"workOrderNumber": "WO512836", "targetQuantity": 349, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-04T16:59:31.463859", "status": "active", "mesForwardedCount": 14, "completionRate": 72.2}, {"workOrderNumber": "WO590607", "targetQuantity": 358, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-19T16:59:31.463859", "status": "pending", "mesForwardedCount": 27, "completionRate": 82.7}], "currentWorkOrder": 0, "currentWorkOrderProgress": 94, "displayWorkOrder": {"workOrderNumber": "WO393068", "targetQuantity": 307, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-13T16:59:31.463859", "status": "completed", "mesForwardedCount": 47, "completionRate": 18.1}, "mesErrors": [{"timestamp": "2025-06-13T12:39:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050006"}}], "recentLogs": [{"message": "測試條碼-2fd257b6", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-cc749acb", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-c457fc10", "timestamp": "2025-06-13T16:10:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-d236e98e", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e6deead1", "timestamp": "2025-06-13T16:36:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-505a3472", "timestamp": "2025-06-13T16:36:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-3b07df62", "timestamp": "2025-06-13T16:35:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-0740d79d", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-cf2e1397", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-7d69486e", "timestamp": "2025-06-13T16:18:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-97f20b43", "timestamp": "2025-06-13T16:10:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-8fe7f975", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-16c1d735", "timestamp": "2025-06-13T16:06:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-6fff2398", "timestamp": "2025-06-13T16:28:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1a0ddafb", "timestamp": "2025-06-13T16:11:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-b328a67b", "timestamp": "2025-06-13T16:58:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-fbf19618", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-fd5ea163", "timestamp": "2025-06-13T16:49:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}]}, "S720050007": {"deviceId": "S720050007", "lineName": "A1-01", "sectionName": "TESTING", "groupName": "INJECTION", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 640, "forwardedCount": 621, "unforwardedCount": 19, "lastLog": "測試產品條碼-73e04b69", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-03-26T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO711768", "targetQuantity": 479, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-08T16:59:31.463859", "status": "pending", "mesForwardedCount": 40, "completionRate": 39.2}, {"workOrderNumber": "WO308791", "targetQuantity": 92, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-14T16:59:31.463859", "status": "active", "mesForwardedCount": 42, "completionRate": 38.7}, {"workOrderNumber": "WO550837", "targetQuantity": 87, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-18T16:59:31.463859", "status": "active", "mesForwardedCount": 56, "completionRate": 64.9}], "currentWorkOrder": 2, "currentWorkOrderProgress": 76, "displayWorkOrder": {"workOrderNumber": "WO550837", "targetQuantity": 87, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-18T16:59:31.463859", "status": "active", "mesForwardedCount": 56, "completionRate": 64.9}, "mesErrors": [{"timestamp": "2025-06-13T16:08:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050007"}}, {"timestamp": "2025-06-12T17:09:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050007"}}, {"timestamp": "2025-06-13T08:24:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050007"}}, {"timestamp": "2025-06-13T14:11:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050007"}}, {"timestamp": "2025-06-13T09:42:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050007"}}], "recentLogs": [{"message": "測試條碼-ad2397f2", "timestamp": "2025-06-13T16:07:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-9104b836", "timestamp": "2025-06-13T16:29:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-1364a6e6", "timestamp": "2025-06-13T16:03:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-c02719f6", "timestamp": "2025-06-13T16:44:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-a35141e9", "timestamp": "2025-06-13T16:40:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-1a9e8e6c", "timestamp": "2025-06-13T16:54:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-beeb608d", "timestamp": "2025-06-13T16:24:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-5c0527b2", "timestamp": "2025-06-13T16:29:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-2e0476b0", "timestamp": "2025-06-13T16:11:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-5843942f", "timestamp": "2025-06-13T16:01:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-6bda6579", "timestamp": "2025-06-13T16:50:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-cc02071a", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}]}, "S720050008": {"deviceId": "S720050008", "lineName": "B2-02", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 440, "forwardedCount": 230, "unforwardedCount": 210, "lastLog": "測試產品條碼-f079e1f8", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-06-22T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO314396", "targetQuantity": 141, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-19T16:59:31.463859", "status": "active", "mesForwardedCount": 47, "completionRate": 61.1}, {"workOrderNumber": "WO521520", "targetQuantity": 178, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-11T16:59:31.463859", "status": "active", "mesForwardedCount": 93, "completionRate": 18.4}, {"workOrderNumber": "WO566659", "targetQuantity": 183, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-28T16:59:31.463859", "status": "active", "mesForwardedCount": 51, "completionRate": 17.9}], "currentWorkOrder": 1, "currentWorkOrderProgress": 32, "displayWorkOrder": {"workOrderNumber": "WO521520", "targetQuantity": 178, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-11T16:59:31.463859", "status": "active", "mesForwardedCount": 93, "completionRate": 18.4}, "mesErrors": [{"timestamp": "2025-06-13T10:40:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050008"}}, {"timestamp": "2025-06-13T03:57:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050008"}}, {"timestamp": "2025-06-13T16:02:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050008"}}, {"timestamp": "2025-06-13T13:05:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050008"}}], "recentLogs": [{"message": "測試條碼-26fbbb24", "timestamp": "2025-06-13T16:33:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-adf48265", "timestamp": "2025-06-13T16:21:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-85d78f82", "timestamp": "2025-06-13T16:21:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-97c26841", "timestamp": "2025-06-13T16:28:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-93768416", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f0a36ad5", "timestamp": "2025-06-13T16:45:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-96b266bc", "timestamp": "2025-06-13T16:21:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-85da3efb", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-af7d0ddf", "timestamp": "2025-06-13T16:22:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-8f64485c", "timestamp": "2025-06-13T16:04:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-4e234bbb", "timestamp": "2025-06-13T16:45:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-cb827870", "timestamp": "2025-06-13T16:44:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-7804fee7", "timestamp": "2025-06-13T15:59:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-84ec4a8c", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-67fbd489", "timestamp": "2025-06-13T16:04:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-0cf770b7", "timestamp": "2025-06-13T16:44:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-f6c03042", "timestamp": "2025-06-13T16:46:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-143e11e1", "timestamp": "2025-06-13T16:06:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-4e3e7b2c", "timestamp": "2025-06-13T16:11:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e16175cb", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}]}, "S720050009": {"deviceId": "S720050009", "lineName": "B2-02", "sectionName": "ASSEMBLY", "groupName": "TEST", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 28, "forwardedCount": 6, "unforwardedCount": 22, "lastLog": "測試產品條碼-6c96384e", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-04-24T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO657372", "targetQuantity": 469, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-12T16:59:31.463859", "status": "completed", "mesForwardedCount": 51, "completionRate": 1.5}, {"workOrderNumber": "WO300102", "targetQuantity": 285, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-23T16:59:31.463859", "status": "active", "mesForwardedCount": 48, "completionRate": 80.9}, {"workOrderNumber": "WO390370", "targetQuantity": 252, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-29T16:59:31.463859", "status": "pending", "mesForwardedCount": 72, "completionRate": 35.4}], "currentWorkOrder": 1, "currentWorkOrderProgress": 79, "displayWorkOrder": {"workOrderNumber": "WO300102", "targetQuantity": 285, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-23T16:59:31.463859", "status": "active", "mesForwardedCount": 48, "completionRate": 80.9}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-a5a2077c", "timestamp": "2025-06-13T16:53:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-ce0b6332", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-3cf23f31", "timestamp": "2025-06-13T16:53:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-3287d000", "timestamp": "2025-06-13T16:54:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-57726682", "timestamp": "2025-06-13T16:10:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1e481ad5", "timestamp": "2025-06-13T16:13:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-ef2c1227", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-bc56290b", "timestamp": "2025-06-13T16:51:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-166272f6", "timestamp": "2025-06-13T16:20:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-ab2f844b", "timestamp": "2025-06-13T16:24:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-9fac1029", "timestamp": "2025-06-13T16:58:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-9de06eb7", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-beb929c3", "timestamp": "2025-06-13T16:41:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}]}, "S720050010": {"deviceId": "S720050010", "lineName": "B2-01", "sectionName": "INJECTION", "groupName": "ASSEMBLY", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 391, "forwardedCount": 196, "unforwardedCount": 195, "lastLog": "測試產品條碼-6736fe1f", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-05-17T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO540164", "targetQuantity": 351, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-26T16:59:31.463859", "status": "completed", "mesForwardedCount": 23, "completionRate": 70.7}, {"workOrderNumber": "WO812917", "targetQuantity": 144, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-08T16:59:31.463859", "status": "active", "mesForwardedCount": 10, "completionRate": 76.3}, {"workOrderNumber": "WO259025", "targetQuantity": 73, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-09T16:59:31.463859", "status": "pending", "mesForwardedCount": 32, "completionRate": 91.3}], "currentWorkOrder": 1, "currentWorkOrderProgress": 60, "displayWorkOrder": {"workOrderNumber": "WO812917", "targetQuantity": 144, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-08T16:59:31.463859", "status": "active", "mesForwardedCount": 10, "completionRate": 76.3}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-2fd731f4", "timestamp": "2025-06-13T16:43:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-c69ff3f5", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-ab3f19f6", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-25423c2d", "timestamp": "2025-06-13T16:05:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-9939684d", "timestamp": "2025-06-13T16:52:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-32f33ded", "timestamp": "2025-06-13T16:51:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-9d610009", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-a179396e", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-ca04bdd0", "timestamp": "2025-06-13T16:18:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-b854aae1", "timestamp": "2025-06-13T16:52:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-f27aba37", "timestamp": "2025-06-13T16:48:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-caea690a", "timestamp": "2025-06-13T16:54:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-6d75c9ba", "timestamp": "2025-06-13T16:50:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-687ab0b4", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-4cbe38c6", "timestamp": "2025-06-13T16:45:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-949bca15", "timestamp": "2025-06-13T16:38:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-f9e95c90", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-ce2915b4", "timestamp": "2025-06-13T16:57:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}]}, "S720050011": {"deviceId": "S720050011", "lineName": "B2-02", "sectionName": "ASSEMBLY", "groupName": "TEST", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 418, "forwardedCount": 142, "unforwardedCount": 276, "lastLog": "測試產品條碼-3728b797", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-08-07T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO710925", "targetQuantity": 320, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-23T16:59:31.463859", "status": "pending", "mesForwardedCount": 66, "completionRate": 0.2}, {"workOrderNumber": "WO518141", "targetQuantity": 173, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-18T16:59:31.463859", "status": "pending", "mesForwardedCount": 4, "completionRate": 5.8}, {"workOrderNumber": "WO688270", "targetQuantity": 436, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-13T16:59:31.463859", "status": "completed", "mesForwardedCount": 77, "completionRate": 79.0}], "currentWorkOrder": 0, "currentWorkOrderProgress": 72, "displayWorkOrder": {"workOrderNumber": "WO710925", "targetQuantity": 320, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-23T16:59:31.463859", "status": "pending", "mesForwardedCount": 66, "completionRate": 0.2}, "mesErrors": [{"timestamp": "2025-06-13T04:21:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050011"}}, {"timestamp": "2025-06-12T18:16:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050011"}}], "recentLogs": [{"message": "測試條碼-b3d3445c", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-d2d1fbcd", "timestamp": "2025-06-13T16:10:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-b6c78f4c", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-ce326153", "timestamp": "2025-06-13T16:06:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-c99f93fb", "timestamp": "2025-06-13T16:10:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-d9a73db3", "timestamp": "2025-06-13T16:05:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-fecc1f87", "timestamp": "2025-06-13T16:46:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-1a894bdb", "timestamp": "2025-06-13T16:34:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-9c5f63e4", "timestamp": "2025-06-13T16:34:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-53661815", "timestamp": "2025-06-13T16:57:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-4992f61e", "timestamp": "2025-06-13T16:41:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}]}, "S720050012": {"deviceId": "S720050012", "lineName": "B2-02", "sectionName": "ASSEMBLY", "groupName": "PACK", "stationName": "STATION_03", "isActive": true, "isMonitoring": true, "productCount": 337, "forwardedCount": 198, "unforwardedCount": 139, "lastLog": "測試產品條碼-f0ddd780", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-11-16T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO416911", "targetQuantity": 422, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-24T16:59:31.463859", "status": "completed", "mesForwardedCount": 3, "completionRate": 59.0}, {"workOrderNumber": "WO723686", "targetQuantity": 376, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-13T16:59:31.463859", "status": "completed", "mesForwardedCount": 0, "completionRate": 41.0}, {"workOrderNumber": "WO762970", "targetQuantity": 443, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-01T16:59:31.463859", "status": "pending", "mesForwardedCount": 77, "completionRate": 20.5}], "currentWorkOrder": 0, "currentWorkOrderProgress": 29, "displayWorkOrder": {"workOrderNumber": "WO416911", "targetQuantity": 422, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-24T16:59:31.463859", "status": "completed", "mesForwardedCount": 3, "completionRate": 59.0}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-6d038c32", "timestamp": "2025-06-13T16:53:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-7b1e445d", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-8df550fd", "timestamp": "2025-06-13T16:43:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-6c76cac3", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-42feed8d", "timestamp": "2025-06-13T16:03:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-aa5351ed", "timestamp": "2025-06-13T16:04:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-8c0a2410", "timestamp": "2025-06-13T16:35:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-fb4c2918", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-c5c02802", "timestamp": "2025-06-13T16:40:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-391f2379", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-a9a56df2", "timestamp": "2025-06-13T16:04:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-fa5266e8", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1c4a73b9", "timestamp": "2025-06-13T16:09:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-773e59f2", "timestamp": "2025-06-13T16:21:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-acbe9083", "timestamp": "2025-06-13T16:15:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-c13fe3bb", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}]}, "S720050013": {"deviceId": "S720050013", "lineName": "A2-02", "sectionName": "PACKAGING", "groupName": "ASSEMBLY", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 186, "forwardedCount": 91, "unforwardedCount": 95, "lastLog": "測試產品條碼-0ba94e47", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-02-22T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO637618", "targetQuantity": 390, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-11T16:59:31.463859", "status": "pending", "mesForwardedCount": 45, "completionRate": 81.0}, {"workOrderNumber": "WO816182", "targetQuantity": 348, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-07T16:59:31.463859", "status": "completed", "mesForwardedCount": 86, "completionRate": 47.6}, {"workOrderNumber": "WO672858", "targetQuantity": 417, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-26T16:59:31.463859", "status": "pending", "mesForwardedCount": 45, "completionRate": 95.6}], "currentWorkOrder": 1, "currentWorkOrderProgress": 66, "displayWorkOrder": {"workOrderNumber": "WO816182", "targetQuantity": 348, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-07T16:59:31.463859", "status": "completed", "mesForwardedCount": 86, "completionRate": 47.6}, "mesErrors": [{"timestamp": "2025-06-13T07:29:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050013"}}, {"timestamp": "2025-06-13T12:21:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050013"}}, {"timestamp": "2025-06-13T06:12:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050013"}}, {"timestamp": "2025-06-13T01:50:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050013"}}], "recentLogs": [{"message": "測試條碼-e19df6c9", "timestamp": "2025-06-13T16:37:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-c01c4457", "timestamp": "2025-06-13T16:38:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-d0d98126", "timestamp": "2025-06-13T16:54:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-c8d0b3d2", "timestamp": "2025-06-13T16:48:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-94c51a0b", "timestamp": "2025-06-13T16:32:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-a70df858", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-ef659846", "timestamp": "2025-06-13T16:49:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-140d0a29", "timestamp": "2025-06-13T16:28:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-3af83830", "timestamp": "2025-06-13T16:39:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-4b71b463", "timestamp": "2025-06-13T16:14:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-260897d1", "timestamp": "2025-06-13T16:54:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-ba6cfaed", "timestamp": "2025-06-13T16:07:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-fa7937e3", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-31c63cd2", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}]}, "S720050014": {"deviceId": "S720050014", "lineName": "B2-02", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 652, "forwardedCount": 100, "unforwardedCount": 552, "lastLog": "測試產品條碼-adc7e2ab", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-12-15T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO715424", "targetQuantity": 206, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-19T16:59:31.463859", "status": "completed", "mesForwardedCount": 25, "completionRate": 87.8}, {"workOrderNumber": "WO325399", "targetQuantity": 165, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-01T16:59:31.463859", "status": "pending", "mesForwardedCount": 37, "completionRate": 8.9}, {"workOrderNumber": "WO800684", "targetQuantity": 122, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-01T16:59:31.463859", "status": "pending", "mesForwardedCount": 34, "completionRate": 77.2}], "currentWorkOrder": 2, "currentWorkOrderProgress": 30, "displayWorkOrder": {"workOrderNumber": "WO800684", "targetQuantity": 122, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-01T16:59:31.463859", "status": "pending", "mesForwardedCount": 34, "completionRate": 77.2}, "mesErrors": [{"timestamp": "2025-06-13T02:47:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050014"}}, {"timestamp": "2025-06-13T14:12:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050014"}}, {"timestamp": "2025-06-13T05:24:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050014"}}, {"timestamp": "2025-06-12T18:19:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050014"}}, {"timestamp": "2025-06-13T14:42:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050014"}}], "recentLogs": [{"message": "測試條碼-fcbce482", "timestamp": "2025-06-13T16:13:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-cfbe72ed", "timestamp": "2025-06-13T16:40:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-31c3d66b", "timestamp": "2025-06-13T16:46:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-c4068419", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-1245c28d", "timestamp": "2025-06-13T16:20:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-c4655c13", "timestamp": "2025-06-13T16:37:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-f1338a7a", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-c0a93d8d", "timestamp": "2025-06-13T16:35:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}]}, "S720050015": {"deviceId": "S720050015", "lineName": "B2-01", "sectionName": "ASSEMBLY", "groupName": "ASSEMBLY", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 414, "forwardedCount": 178, "unforwardedCount": 236, "lastLog": "測試產品條碼-0f6ca363", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-04-04T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO487189", "targetQuantity": 188, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-19T16:59:31.463859", "status": "pending", "mesForwardedCount": 51, "completionRate": 18.0}, {"workOrderNumber": "WO983460", "targetQuantity": 195, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-28T16:59:31.463859", "status": "active", "mesForwardedCount": 70, "completionRate": 34.0}, {"workOrderNumber": "WO367520", "targetQuantity": 472, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-05T16:59:31.463859", "status": "pending", "mesForwardedCount": 60, "completionRate": 61.0}], "currentWorkOrder": 1, "currentWorkOrderProgress": 82, "displayWorkOrder": {"workOrderNumber": "WO983460", "targetQuantity": 195, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-28T16:59:31.463859", "status": "active", "mesForwardedCount": 70, "completionRate": 34.0}, "mesErrors": [{"timestamp": "2025-06-12T19:45:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050015"}}, {"timestamp": "2025-06-12T20:06:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050015"}}, {"timestamp": "2025-06-12T20:25:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050015"}}], "recentLogs": [{"message": "測試條碼-13f5d07d", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-6c8e28bc", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-67fa6c9a", "timestamp": "2025-06-13T16:03:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-372d558d", "timestamp": "2025-06-13T16:34:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-f1ac2044", "timestamp": "2025-06-13T16:31:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-6779011d", "timestamp": "2025-06-13T16:57:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-31afbe9c", "timestamp": "2025-06-13T16:52:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-750d3079", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}]}, "S720050016": {"deviceId": "S720050016", "lineName": "A2-01", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 566, "forwardedCount": 451, "unforwardedCount": 115, "lastLog": "測試產品條碼-5a94392f", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-04-17T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO982403", "targetQuantity": 466, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-17T16:59:31.463859", "status": "pending", "mesForwardedCount": 35, "completionRate": 46.9}, {"workOrderNumber": "WO619783", "targetQuantity": 500, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-17T16:59:31.463859", "status": "pending", "mesForwardedCount": 90, "completionRate": 47.0}, {"workOrderNumber": "WO644737", "targetQuantity": 232, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-27T16:59:31.463859", "status": "active", "mesForwardedCount": 89, "completionRate": 83.7}], "currentWorkOrder": 1, "currentWorkOrderProgress": 64, "displayWorkOrder": {"workOrderNumber": "WO619783", "targetQuantity": 500, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-17T16:59:31.463859", "status": "pending", "mesForwardedCount": 90, "completionRate": 47.0}, "mesErrors": [{"timestamp": "2025-06-13T10:28:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050016"}}, {"timestamp": "2025-06-12T20:19:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050016"}}, {"timestamp": "2025-06-13T09:45:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050016"}}, {"timestamp": "2025-06-13T08:21:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050016"}}], "recentLogs": [{"message": "測試條碼-d7c6850c", "timestamp": "2025-06-13T16:21:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-eed8d1bb", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-e31e4df0", "timestamp": "2025-06-13T16:18:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-c149f539", "timestamp": "2025-06-13T16:01:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-fe12309a", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-5012ad5e", "timestamp": "2025-06-13T16:35:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-84ae2404", "timestamp": "2025-06-13T16:24:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}]}, "S720050017": {"deviceId": "S720050017", "lineName": "B2-02", "sectionName": "INJECTION", "groupName": "TEST", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 117, "forwardedCount": 117, "unforwardedCount": 0, "lastLog": "測試產品條碼-5aefe686", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-10-09T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO530775", "targetQuantity": 105, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-17T16:59:31.463859", "status": "completed", "mesForwardedCount": 42, "completionRate": 32.1}, {"workOrderNumber": "WO814428", "targetQuantity": 369, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-29T16:59:31.463859", "status": "pending", "mesForwardedCount": 8, "completionRate": 96.1}, {"workOrderNumber": "WO710079", "targetQuantity": 389, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-25T16:59:31.463859", "status": "completed", "mesForwardedCount": 68, "completionRate": 98.2}], "currentWorkOrder": 2, "currentWorkOrderProgress": 0, "displayWorkOrder": {"workOrderNumber": "WO710079", "targetQuantity": 389, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-25T16:59:31.463859", "status": "completed", "mesForwardedCount": 68, "completionRate": 98.2}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-bc9248b9", "timestamp": "2025-06-13T16:13:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-18bc25bc", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-f9ac3471", "timestamp": "2025-06-13T16:52:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-c4d0a49e", "timestamp": "2025-06-13T16:39:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-417fbb71", "timestamp": "2025-06-13T16:15:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-4480fe23", "timestamp": "2025-06-13T16:18:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-51ca2740", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}]}, "S720050018": {"deviceId": "S720050018", "lineName": "B2-01", "sectionName": "ASSEMBLY", "groupName": "TEST", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 300, "forwardedCount": 272, "unforwardedCount": 28, "lastLog": "測試產品條碼-8eafc8a4", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-03-18T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO770928", "targetQuantity": 332, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-18T16:59:31.463859", "status": "active", "mesForwardedCount": 41, "completionRate": 97.7}, {"workOrderNumber": "WO757199", "targetQuantity": 368, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-23T16:59:31.463859", "status": "active", "mesForwardedCount": 96, "completionRate": 31.6}, {"workOrderNumber": "WO669055", "targetQuantity": 236, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-12T16:59:31.463859", "status": "completed", "mesForwardedCount": 35, "completionRate": 5.4}], "currentWorkOrder": 0, "currentWorkOrderProgress": 78, "displayWorkOrder": {"workOrderNumber": "WO770928", "targetQuantity": 332, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-18T16:59:31.463859", "status": "active", "mesForwardedCount": 41, "completionRate": 97.7}, "mesErrors": [{"timestamp": "2025-06-13T00:17:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050018"}}, {"timestamp": "2025-06-13T09:21:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050018"}}, {"timestamp": "2025-06-12T23:54:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050018"}}], "recentLogs": [{"message": "測試條碼-6b4ef7c2", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-0724f340", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-79c5fd2b", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-dfb614ad", "timestamp": "2025-06-13T16:23:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-6f71b4b3", "timestamp": "2025-06-13T16:41:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-151e74b0", "timestamp": "2025-06-13T16:39:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ef224b4d", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-1d16abc3", "timestamp": "2025-06-13T16:58:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-115c2905", "timestamp": "2025-06-13T16:30:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-929daee6", "timestamp": "2025-06-13T16:14:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-469ab87a", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-2f9a495c", "timestamp": "2025-06-13T16:28:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}]}, "S720050019": {"deviceId": "S720050019", "lineName": "A2-01", "sectionName": "PACKAGING", "groupName": "INJECTION", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 549, "forwardedCount": 526, "unforwardedCount": 23, "lastLog": "測試產品條碼-021e4396", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-08-03T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO596604", "targetQuantity": 398, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-30T16:59:31.463859", "status": "completed", "mesForwardedCount": 27, "completionRate": 34.1}, {"workOrderNumber": "WO623634", "targetQuantity": 486, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-12T16:59:31.463859", "status": "completed", "mesForwardedCount": 69, "completionRate": 51.9}, {"workOrderNumber": "WO680832", "targetQuantity": 241, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-13T16:59:31.463859", "status": "pending", "mesForwardedCount": 90, "completionRate": 72.8}], "currentWorkOrder": 1, "currentWorkOrderProgress": 42, "displayWorkOrder": {"workOrderNumber": "WO623634", "targetQuantity": 486, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-12T16:59:31.463859", "status": "completed", "mesForwardedCount": 69, "completionRate": 51.9}, "mesErrors": [{"timestamp": "2025-06-13T12:13:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050019"}}, {"timestamp": "2025-06-13T11:25:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050019"}}, {"timestamp": "2025-06-13T11:26:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050019"}}, {"timestamp": "2025-06-13T12:47:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050019"}}], "recentLogs": [{"message": "測試條碼-aa912a65", "timestamp": "2025-06-13T16:22:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-a9ed4b0e", "timestamp": "2025-06-13T16:01:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-40c35a00", "timestamp": "2025-06-13T16:43:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-d2b1ba97", "timestamp": "2025-06-13T16:03:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-67505b7f", "timestamp": "2025-06-13T16:24:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-60542e0e", "timestamp": "2025-06-13T16:18:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-dcbaefe6", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-ba7d17b3", "timestamp": "2025-06-13T16:33:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-226b4d20", "timestamp": "2025-06-13T16:29:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-745a6694", "timestamp": "2025-06-13T16:43:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-58e3e992", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-cc5cc253", "timestamp": "2025-06-13T16:10:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-e0f972e7", "timestamp": "2025-06-13T16:45:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-af3e6473", "timestamp": "2025-06-13T16:45:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-2f9186e6", "timestamp": "2025-06-13T16:53:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-c7b9d72d", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-9f9cb32b", "timestamp": "2025-06-13T16:47:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-74cd616e", "timestamp": "2025-06-13T16:28:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-204d5204", "timestamp": "2025-06-13T16:31:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}]}, "S720050020": {"deviceId": "S720050020", "lineName": "A2-02", "sectionName": "INJECTION", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 244, "forwardedCount": 192, "unforwardedCount": 52, "lastLog": "測試產品條碼-09916d3a", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-09-23T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO484078", "targetQuantity": 397, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-21T16:59:31.463859", "status": "completed", "mesForwardedCount": 100, "completionRate": 6.7}, {"workOrderNumber": "WO103819", "targetQuantity": 195, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-25T16:59:31.463859", "status": "pending", "mesForwardedCount": 30, "completionRate": 34.1}, {"workOrderNumber": "WO318202", "targetQuantity": 179, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-10T16:59:31.463859", "status": "active", "mesForwardedCount": 50, "completionRate": 49.6}], "currentWorkOrder": 2, "currentWorkOrderProgress": 24, "displayWorkOrder": {"workOrderNumber": "WO318202", "targetQuantity": 179, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-10T16:59:31.463859", "status": "active", "mesForwardedCount": 50, "completionRate": 49.6}, "mesErrors": [{"timestamp": "2025-06-13T16:42:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050020"}}, {"timestamp": "2025-06-13T12:59:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050020"}}, {"timestamp": "2025-06-13T02:30:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050020"}}], "recentLogs": [{"message": "測試條碼-ccfe9da3", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-edc94e02", "timestamp": "2025-06-13T16:24:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-5774897c", "timestamp": "2025-06-13T16:53:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-b99cc24a", "timestamp": "2025-06-13T16:39:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-6717bc24", "timestamp": "2025-06-13T16:22:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-13b5fa8a", "timestamp": "2025-06-13T16:29:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-4bd3a626", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-2f04a1d7", "timestamp": "2025-06-13T16:33:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-d52d7217", "timestamp": "2025-06-13T16:28:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e77aafbe", "timestamp": "2025-06-13T16:11:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-c793300f", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-5ebbcd8c", "timestamp": "2025-06-13T16:11:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-530ff568", "timestamp": "2025-06-13T16:44:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-fa620cd8", "timestamp": "2025-06-13T16:10:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-b72f8646", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}]}, "S720050021": {"deviceId": "S720050021", "lineName": "A2-02", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 669, "forwardedCount": 296, "unforwardedCount": 373, "lastLog": "測試產品條碼-39d0e2c1", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-04-12T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO475870", "targetQuantity": 244, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-18T16:59:31.463859", "status": "pending", "mesForwardedCount": 23, "completionRate": 33.5}, {"workOrderNumber": "WO557365", "targetQuantity": 174, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-14T16:59:31.463859", "status": "active", "mesForwardedCount": 33, "completionRate": 66.8}, {"workOrderNumber": "WO486330", "targetQuantity": 260, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-16T16:59:31.463859", "status": "pending", "mesForwardedCount": 55, "completionRate": 65.9}], "currentWorkOrder": 0, "currentWorkOrderProgress": 18, "displayWorkOrder": {"workOrderNumber": "WO475870", "targetQuantity": 244, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-18T16:59:31.463859", "status": "pending", "mesForwardedCount": 23, "completionRate": 33.5}, "mesErrors": [{"timestamp": "2025-06-12T20:11:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050021"}}, {"timestamp": "2025-06-13T06:13:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050021"}}, {"timestamp": "2025-06-12T23:38:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050021"}}, {"timestamp": "2025-06-12T19:00:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050021"}}], "recentLogs": [{"message": "測試條碼-70d1a7e8", "timestamp": "2025-06-13T16:37:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-23d4a03a", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-b2b4cf53", "timestamp": "2025-06-13T16:30:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-aa6ddf64", "timestamp": "2025-06-13T16:40:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-63a4801f", "timestamp": "2025-06-13T16:35:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-57b7b854", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-84075f94", "timestamp": "2025-06-13T16:15:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-6f6cdce3", "timestamp": "2025-06-13T16:00:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f8aef57c", "timestamp": "2025-06-13T16:24:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-4f8984bf", "timestamp": "2025-06-13T16:28:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-eaabcccd", "timestamp": "2025-06-13T16:24:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}]}, "S720050022": {"deviceId": "S720050022", "lineName": "B1-01", "sectionName": "TESTING", "groupName": "TEST", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 67, "forwardedCount": 15, "unforwardedCount": 52, "lastLog": "測試產品條碼-e8255f43", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-12-06T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO362385", "targetQuantity": 129, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-04T16:59:31.463859", "status": "pending", "mesForwardedCount": 26, "completionRate": 43.9}, {"workOrderNumber": "WO768203", "targetQuantity": 54, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-26T16:59:31.463859", "status": "pending", "mesForwardedCount": 56, "completionRate": 4.3}, {"workOrderNumber": "WO941692", "targetQuantity": 347, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-18T16:59:31.463859", "status": "pending", "mesForwardedCount": 84, "completionRate": 29.2}], "currentWorkOrder": 1, "currentWorkOrderProgress": 15, "displayWorkOrder": {"workOrderNumber": "WO768203", "targetQuantity": 54, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-26T16:59:31.463859", "status": "pending", "mesForwardedCount": 56, "completionRate": 4.3}, "mesErrors": [{"timestamp": "2025-06-13T08:47:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050022"}}, {"timestamp": "2025-06-12T18:00:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050022"}}], "recentLogs": [{"message": "測試條碼-c677858b", "timestamp": "2025-06-13T16:35:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-359aae4c", "timestamp": "2025-06-13T16:51:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-b00a2f75", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-bf02f791", "timestamp": "2025-06-13T16:58:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-eefc28d8", "timestamp": "2025-06-13T15:59:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-85c9d8f2", "timestamp": "2025-06-13T16:10:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-f1dce859", "timestamp": "2025-06-13T16:45:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-8b783cde", "timestamp": "2025-06-13T16:16:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-985632ce", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-29ade326", "timestamp": "2025-06-13T16:11:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-7508fedb", "timestamp": "2025-06-13T16:23:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-644d1d82", "timestamp": "2025-06-13T16:00:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-2872df02", "timestamp": "2025-06-13T16:53:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-5f7f47a5", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}]}, "S720050023": {"deviceId": "S720050023", "lineName": "B2-01", "sectionName": "INJECTION", "groupName": "ASSEMBLY", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 2, "forwardedCount": 0, "unforwardedCount": 2, "lastLog": "測試產品條碼-a92dfb7e", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-02-18T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO729404", "targetQuantity": 176, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-29T16:59:31.463859", "status": "pending", "mesForwardedCount": 86, "completionRate": 73.9}, {"workOrderNumber": "WO668168", "targetQuantity": 238, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-19T16:59:31.463859", "status": "pending", "mesForwardedCount": 66, "completionRate": 57.8}, {"workOrderNumber": "WO914168", "targetQuantity": 265, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-11T16:59:31.463859", "status": "active", "mesForwardedCount": 8, "completionRate": 20.3}], "currentWorkOrder": 2, "currentWorkOrderProgress": 29, "displayWorkOrder": {"workOrderNumber": "WO914168", "targetQuantity": 265, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-11T16:59:31.463859", "status": "active", "mesForwardedCount": 8, "completionRate": 20.3}, "mesErrors": [{"timestamp": "2025-06-13T00:18:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050023"}}], "recentLogs": [{"message": "測試條碼-ca99a3f8", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-97eddb04", "timestamp": "2025-06-13T16:35:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-5ae26b05", "timestamp": "2025-06-13T16:44:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-1fb0cdea", "timestamp": "2025-06-13T16:54:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-f57d7f5d", "timestamp": "2025-06-13T16:07:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-5c24fffa", "timestamp": "2025-06-13T16:06:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-ff3e9327", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-1b693da2", "timestamp": "2025-06-13T16:11:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-59c75d37", "timestamp": "2025-06-13T16:15:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-b8097b40", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-cb19ad36", "timestamp": "2025-06-13T16:43:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-eb694c1d", "timestamp": "2025-06-13T16:29:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-21f36e0e", "timestamp": "2025-06-13T16:51:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-e466de14", "timestamp": "2025-06-13T16:07:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-ecf57e4f", "timestamp": "2025-06-13T16:35:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-f25676f5", "timestamp": "2025-06-13T16:52:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-4759b753", "timestamp": "2025-06-13T16:52:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-6c314e2b", "timestamp": "2025-06-13T16:31:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-6ad44987", "timestamp": "2025-06-13T16:00:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}]}, "S720050024": {"deviceId": "S720050024", "lineName": "B2-01", "sectionName": "ASSEMBLY", "groupName": "ASSEMBLY", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 406, "forwardedCount": 89, "unforwardedCount": 317, "lastLog": "測試產品條碼-7de46c85", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-02-01T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO303283", "targetQuantity": 92, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-30T16:59:31.463859", "status": "pending", "mesForwardedCount": 50, "completionRate": 36.9}, {"workOrderNumber": "WO904467", "targetQuantity": 176, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-12T16:59:31.463859", "status": "active", "mesForwardedCount": 12, "completionRate": 37.6}, {"workOrderNumber": "WO140075", "targetQuantity": 409, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-04T16:59:31.463859", "status": "pending", "mesForwardedCount": 74, "completionRate": 2.3}], "currentWorkOrder": 0, "currentWorkOrderProgress": 90, "displayWorkOrder": {"workOrderNumber": "WO303283", "targetQuantity": 92, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-30T16:59:31.463859", "status": "pending", "mesForwardedCount": 50, "completionRate": 36.9}, "mesErrors": [{"timestamp": "2025-06-12T18:36:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050024"}}, {"timestamp": "2025-06-13T14:43:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050024"}}, {"timestamp": "2025-06-13T10:45:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050024"}}, {"timestamp": "2025-06-13T14:41:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050024"}}, {"timestamp": "2025-06-13T06:10:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050024"}}], "recentLogs": [{"message": "測試條碼-339be5a2", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-cd2bbec4", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-5a6081f6", "timestamp": "2025-06-13T16:47:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-758e716a", "timestamp": "2025-06-13T16:06:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-9641f64b", "timestamp": "2025-06-13T16:43:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-8e23c0db", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-0a3429de", "timestamp": "2025-06-13T16:31:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-93b4bbcb", "timestamp": "2025-06-13T16:06:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-3a94b1e8", "timestamp": "2025-06-13T16:39:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-3c9e4c59", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-b9c965e6", "timestamp": "2025-06-13T16:09:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-9b7de455", "timestamp": "2025-06-13T16:13:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}]}, "S720050025": {"deviceId": "S720050025", "lineName": "A1-01", "sectionName": "PACKAGING", "groupName": "TEST", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 231, "forwardedCount": 196, "unforwardedCount": 35, "lastLog": "測試產品條碼-fdca3d0b", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-06-09T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO714821", "targetQuantity": 174, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-14T16:59:31.463859", "status": "completed", "mesForwardedCount": 21, "completionRate": 33.9}, {"workOrderNumber": "WO895575", "targetQuantity": 224, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-12T16:59:31.463859", "status": "completed", "mesForwardedCount": 92, "completionRate": 18.2}, {"workOrderNumber": "WO545955", "targetQuantity": 261, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-21T16:59:31.463859", "status": "completed", "mesForwardedCount": 30, "completionRate": 45.8}], "currentWorkOrder": 2, "currentWorkOrderProgress": 44, "displayWorkOrder": {"workOrderNumber": "WO545955", "targetQuantity": 261, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-21T16:59:31.463859", "status": "completed", "mesForwardedCount": 30, "completionRate": 45.8}, "mesErrors": [{"timestamp": "2025-06-13T08:48:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050025"}}, {"timestamp": "2025-06-13T12:08:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050025"}}], "recentLogs": [{"message": "測試條碼-2247c248", "timestamp": "2025-06-13T16:57:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-1f2fcffa", "timestamp": "2025-06-13T16:29:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-58d48af0", "timestamp": "2025-06-13T16:45:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-92d8f91e", "timestamp": "2025-06-13T16:51:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-c47d9b4d", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-58de79d8", "timestamp": "2025-06-13T16:16:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-382d5625", "timestamp": "2025-06-13T16:10:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-36c233f7", "timestamp": "2025-06-13T16:33:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-9cd1837b", "timestamp": "2025-06-13T16:09:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}]}, "S720050026": {"deviceId": "S720050026", "lineName": "A2-01", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_03", "isActive": true, "isMonitoring": true, "productCount": 249, "forwardedCount": 95, "unforwardedCount": 154, "lastLog": "測試產品條碼-5aece153", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-01-11T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO232559", "targetQuantity": 369, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-23T16:59:31.463859", "status": "completed", "mesForwardedCount": 60, "completionRate": 90.5}, {"workOrderNumber": "WO645940", "targetQuantity": 314, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-21T16:59:31.463859", "status": "active", "mesForwardedCount": 43, "completionRate": 24.8}, {"workOrderNumber": "WO507862", "targetQuantity": 347, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-24T16:59:31.463859", "status": "completed", "mesForwardedCount": 24, "completionRate": 85.9}], "currentWorkOrder": 1, "currentWorkOrderProgress": 91, "displayWorkOrder": {"workOrderNumber": "WO645940", "targetQuantity": 314, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-21T16:59:31.463859", "status": "active", "mesForwardedCount": 43, "completionRate": 24.8}, "mesErrors": [{"timestamp": "2025-06-13T02:22:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050026"}}], "recentLogs": [{"message": "測試條碼-e5d4b68a", "timestamp": "2025-06-13T16:54:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-ac0e6b53", "timestamp": "2025-06-13T16:50:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-431720e2", "timestamp": "2025-06-13T16:38:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-15be3f8c", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-74dd29b3", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-4a12fb8e", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-db44e928", "timestamp": "2025-06-13T16:45:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-25b5fca4", "timestamp": "2025-06-13T16:04:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-ac5d035a", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-8a4b1fe4", "timestamp": "2025-06-13T16:57:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-6578603e", "timestamp": "2025-06-13T16:46:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-f0458b95", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-a7e4244d", "timestamp": "2025-06-13T16:15:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-8445025f", "timestamp": "2025-06-13T16:46:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-9860a48c", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d9958173", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-fd6571a2", "timestamp": "2025-06-13T16:36:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-e6927d3f", "timestamp": "2025-06-13T16:50:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-ca7a20c6", "timestamp": "2025-06-13T16:15:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-84dc1ef5", "timestamp": "2025-06-13T16:03:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}]}, "S720050027": {"deviceId": "S720050027", "lineName": "A2-02", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 568, "forwardedCount": 218, "unforwardedCount": 350, "lastLog": "測試產品條碼-4eca8af1", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-01-29T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO940453", "targetQuantity": 259, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-21T16:59:31.463859", "status": "pending", "mesForwardedCount": 1, "completionRate": 31.4}, {"workOrderNumber": "WO407290", "targetQuantity": 258, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-28T16:59:31.463859", "status": "pending", "mesForwardedCount": 14, "completionRate": 63.0}, {"workOrderNumber": "WO437486", "targetQuantity": 106, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-12T16:59:31.463859", "status": "active", "mesForwardedCount": 55, "completionRate": 64.7}], "currentWorkOrder": 1, "currentWorkOrderProgress": 70, "displayWorkOrder": {"workOrderNumber": "WO407290", "targetQuantity": 258, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-28T16:59:31.463859", "status": "pending", "mesForwardedCount": 14, "completionRate": 63.0}, "mesErrors": [{"timestamp": "2025-06-13T05:06:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050027"}}, {"timestamp": "2025-06-12T21:11:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050027"}}, {"timestamp": "2025-06-13T00:16:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050027"}}], "recentLogs": [{"message": "測試條碼-2fa4bdb6", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-a5980b15", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-a5de92b6", "timestamp": "2025-06-13T16:40:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-be750212", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ad9f1242", "timestamp": "2025-06-13T16:29:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-1e986cf5", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-54ffe8a0", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-238ae487", "timestamp": "2025-06-13T16:41:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-5598863b", "timestamp": "2025-06-13T16:57:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-9d007b19", "timestamp": "2025-06-13T16:24:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-e8d1a659", "timestamp": "2025-06-13T16:32:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-836e4bc2", "timestamp": "2025-06-13T16:51:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}]}, "S720050028": {"deviceId": "S720050028", "lineName": "B2-01", "sectionName": "TESTING", "groupName": "INJECTION", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 327, "forwardedCount": 76, "unforwardedCount": 251, "lastLog": "測試產品條碼-0d8661dd", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-05-31T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO489921", "targetQuantity": 81, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-09T16:59:31.463859", "status": "pending", "mesForwardedCount": 75, "completionRate": 58.4}, {"workOrderNumber": "WO999475", "targetQuantity": 442, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-11T16:59:31.463859", "status": "active", "mesForwardedCount": 13, "completionRate": 19.1}, {"workOrderNumber": "WO150083", "targetQuantity": 466, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-25T16:59:31.463859", "status": "active", "mesForwardedCount": 3, "completionRate": 8.2}], "currentWorkOrder": 1, "currentWorkOrderProgress": 84, "displayWorkOrder": {"workOrderNumber": "WO999475", "targetQuantity": 442, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-11T16:59:31.463859", "status": "active", "mesForwardedCount": 13, "completionRate": 19.1}, "mesErrors": [{"timestamp": "2025-06-13T07:50:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050028"}}, {"timestamp": "2025-06-12T23:14:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050028"}}, {"timestamp": "2025-06-13T07:22:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050028"}}], "recentLogs": [{"message": "測試條碼-97ef426d", "timestamp": "2025-06-13T16:31:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-d906f028", "timestamp": "2025-06-13T16:52:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-9054ab40", "timestamp": "2025-06-13T16:44:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-e003f209", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-102fae41", "timestamp": "2025-06-13T16:34:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-aa042b04", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-9066bdf8", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-03ac6dd5", "timestamp": "2025-06-13T16:04:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-bea7898a", "timestamp": "2025-06-13T16:07:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-7b2f1a52", "timestamp": "2025-06-13T16:57:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}]}, "S720050029": {"deviceId": "S720050029", "lineName": "A2-02", "sectionName": "INJECTION", "groupName": "PACK", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 507, "forwardedCount": 259, "unforwardedCount": 248, "lastLog": "測試產品條碼-db9dd035", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-12-20T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO421498", "targetQuantity": 280, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-24T16:59:31.463859", "status": "active", "mesForwardedCount": 9, "completionRate": 41.8}, {"workOrderNumber": "WO166957", "targetQuantity": 490, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-14T16:59:31.463859", "status": "pending", "mesForwardedCount": 28, "completionRate": 28.8}, {"workOrderNumber": "WO434133", "targetQuantity": 93, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-09T16:59:31.463859", "status": "completed", "mesForwardedCount": 6, "completionRate": 59.3}], "currentWorkOrder": 2, "currentWorkOrderProgress": 100, "displayWorkOrder": {"workOrderNumber": "WO434133", "targetQuantity": 93, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-09T16:59:31.463859", "status": "completed", "mesForwardedCount": 6, "completionRate": 59.3}, "mesErrors": [{"timestamp": "2025-06-13T09:55:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050029"}}, {"timestamp": "2025-06-12T20:37:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050029"}}, {"timestamp": "2025-06-12T23:10:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050029"}}, {"timestamp": "2025-06-13T15:14:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050029"}}, {"timestamp": "2025-06-13T10:54:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050029"}}], "recentLogs": [{"message": "測試條碼-39c1e82a", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-e14983e5", "timestamp": "2025-06-13T16:21:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-a623d1bc", "timestamp": "2025-06-13T16:07:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-efdf58e5", "timestamp": "2025-06-13T15:59:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-2e7a4a5b", "timestamp": "2025-06-13T16:07:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-62d44b2e", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-390a3ef9", "timestamp": "2025-06-13T16:21:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-a4036c2a", "timestamp": "2025-06-13T16:22:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-3bf58b8e", "timestamp": "2025-06-13T16:16:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-91f77403", "timestamp": "2025-06-13T16:10:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-f3a381ee", "timestamp": "2025-06-13T16:38:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-e8a96673", "timestamp": "2025-06-13T16:43:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}]}, "S720050030": {"deviceId": "S720050030", "lineName": "B1-02", "sectionName": "INJECTION", "groupName": "INJECTION", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 411, "forwardedCount": 63, "unforwardedCount": 348, "lastLog": "測試產品條碼-69988cdb", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-11-14T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO441707", "targetQuantity": 278, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-05T16:59:31.463859", "status": "active", "mesForwardedCount": 12, "completionRate": 20.7}, {"workOrderNumber": "WO541617", "targetQuantity": 196, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-29T16:59:31.463859", "status": "pending", "mesForwardedCount": 13, "completionRate": 41.7}, {"workOrderNumber": "WO295896", "targetQuantity": 375, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-30T16:59:31.463859", "status": "active", "mesForwardedCount": 92, "completionRate": 0.4}], "currentWorkOrder": 2, "currentWorkOrderProgress": 79, "displayWorkOrder": {"workOrderNumber": "WO295896", "targetQuantity": 375, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-30T16:59:31.463859", "status": "active", "mesForwardedCount": 92, "completionRate": 0.4}, "mesErrors": [{"timestamp": "2025-06-13T14:29:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050030"}}, {"timestamp": "2025-06-13T06:32:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050030"}}, {"timestamp": "2025-06-13T16:38:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050030"}}], "recentLogs": [{"message": "測試條碼-b8cdbc51", "timestamp": "2025-06-13T16:51:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-33cb28c9", "timestamp": "2025-06-13T16:54:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-c70fc562", "timestamp": "2025-06-13T16:34:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-6bff08ee", "timestamp": "2025-06-13T16:35:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ac673361", "timestamp": "2025-06-13T16:39:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-b446a1b0", "timestamp": "2025-06-13T16:22:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-c79db38b", "timestamp": "2025-06-13T16:57:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-8e11cb7b", "timestamp": "2025-06-13T16:58:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-b092a687", "timestamp": "2025-06-13T16:49:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-59270e29", "timestamp": "2025-06-13T16:38:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-d05fbc48", "timestamp": "2025-06-13T16:01:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-7ef0eb4e", "timestamp": "2025-06-13T16:03:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}]}, "S720050031": {"deviceId": "S720050031", "lineName": "B1-01", "sectionName": "ASSEMBLY", "groupName": "PACK", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 532, "forwardedCount": 510, "unforwardedCount": 22, "lastLog": "測試產品條碼-585ad63e", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-02-01T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO173650", "targetQuantity": 94, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-22T16:59:31.463859", "status": "active", "mesForwardedCount": 87, "completionRate": 49.1}, {"workOrderNumber": "WO342435", "targetQuantity": 238, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-10T16:59:31.463859", "status": "active", "mesForwardedCount": 43, "completionRate": 55.0}, {"workOrderNumber": "WO755397", "targetQuantity": 93, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-21T16:59:31.463859", "status": "completed", "mesForwardedCount": 89, "completionRate": 1.2}], "currentWorkOrder": 0, "currentWorkOrderProgress": 11, "displayWorkOrder": {"workOrderNumber": "WO173650", "targetQuantity": 94, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-22T16:59:31.463859", "status": "active", "mesForwardedCount": 87, "completionRate": 49.1}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-96a71558", "timestamp": "2025-06-13T16:14:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d6a4c88b", "timestamp": "2025-06-13T16:14:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-2fdb9578", "timestamp": "2025-06-13T16:51:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-24320442", "timestamp": "2025-06-13T16:37:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-7ebd447b", "timestamp": "2025-06-13T16:40:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-9f80e455", "timestamp": "2025-06-13T16:13:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-411e0cd3", "timestamp": "2025-06-13T16:16:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-6d44c069", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-90248bd7", "timestamp": "2025-06-13T16:09:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-848c6f9d", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-ed00baa0", "timestamp": "2025-06-13T16:45:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}]}, "S720050032": {"deviceId": "S720050032", "lineName": "A1-01", "sectionName": "ASSEMBLY", "groupName": "INJECTION", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 961, "forwardedCount": 768, "unforwardedCount": 193, "lastLog": "測試產品條碼-adcdabe1", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-01-11T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO850112", "targetQuantity": 252, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-04T16:59:31.463859", "status": "active", "mesForwardedCount": 98, "completionRate": 14.9}, {"workOrderNumber": "WO673863", "targetQuantity": 282, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-10T16:59:31.463859", "status": "active", "mesForwardedCount": 47, "completionRate": 50.7}, {"workOrderNumber": "WO298113", "targetQuantity": 368, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-09T16:59:31.463859", "status": "active", "mesForwardedCount": 40, "completionRate": 87.1}], "currentWorkOrder": 1, "currentWorkOrderProgress": 49, "displayWorkOrder": {"workOrderNumber": "WO673863", "targetQuantity": 282, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-10T16:59:31.463859", "status": "active", "mesForwardedCount": 47, "completionRate": 50.7}, "mesErrors": [{"timestamp": "2025-06-13T02:28:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050032"}}], "recentLogs": [{"message": "測試條碼-83c2bb31", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-714c4a6a", "timestamp": "2025-06-13T16:11:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-bc1746c1", "timestamp": "2025-06-13T16:48:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-9f9dca5b", "timestamp": "2025-06-13T16:41:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-83fd8a98", "timestamp": "2025-06-13T16:09:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-0c0cd73a", "timestamp": "2025-06-13T16:58:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-97671ecb", "timestamp": "2025-06-13T16:30:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-74ff1188", "timestamp": "2025-06-13T16:31:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-cd4c2653", "timestamp": "2025-06-13T16:52:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}]}, "S720050033": {"deviceId": "S720050033", "lineName": "A1-01", "sectionName": "INJECTION", "groupName": "ASSEMBLY", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 764, "forwardedCount": 610, "unforwardedCount": 154, "lastLog": "測試產品條碼-1f8865cf", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-05-25T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO579443", "targetQuantity": 65, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-17T16:59:31.463859", "status": "pending", "mesForwardedCount": 55, "completionRate": 68.0}, {"workOrderNumber": "WO399474", "targetQuantity": 399, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-03T16:59:31.463859", "status": "active", "mesForwardedCount": 17, "completionRate": 47.7}, {"workOrderNumber": "WO934779", "targetQuantity": 60, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-24T16:59:31.463859", "status": "active", "mesForwardedCount": 0, "completionRate": 58.3}], "currentWorkOrder": 1, "currentWorkOrderProgress": 62, "displayWorkOrder": {"workOrderNumber": "WO399474", "targetQuantity": 399, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-03T16:59:31.463859", "status": "active", "mesForwardedCount": 17, "completionRate": 47.7}, "mesErrors": [{"timestamp": "2025-06-12T19:48:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050033"}}, {"timestamp": "2025-06-13T05:14:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050033"}}, {"timestamp": "2025-06-12T20:31:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050033"}}, {"timestamp": "2025-06-13T09:56:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050033"}}, {"timestamp": "2025-06-12T18:21:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050033"}}], "recentLogs": [{"message": "測試條碼-5c975256", "timestamp": "2025-06-13T16:11:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-e1bdc8b0", "timestamp": "2025-06-13T16:22:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-4495ced6", "timestamp": "2025-06-13T16:41:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-dd04c9b1", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-c6e322b3", "timestamp": "2025-06-13T16:17:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-80ad00cc", "timestamp": "2025-06-13T16:15:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-fce711ce", "timestamp": "2025-06-13T16:22:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-fa377e4c", "timestamp": "2025-06-13T16:36:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-b9f16154", "timestamp": "2025-06-13T16:23:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-7cf7f6e0", "timestamp": "2025-06-13T16:20:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-300b9531", "timestamp": "2025-06-13T16:05:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-555e2efe", "timestamp": "2025-06-13T16:00:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-294277bc", "timestamp": "2025-06-13T16:35:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-c4ea857a", "timestamp": "2025-06-13T16:48:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-1ff32606", "timestamp": "2025-06-13T16:29:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-eff51648", "timestamp": "2025-06-13T16:57:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-6b25a269", "timestamp": "2025-06-13T16:44:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-d73a1e3d", "timestamp": "2025-06-13T16:20:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-35a51b00", "timestamp": "2025-06-13T16:34:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-77f45a78", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}]}, "S720050034": {"deviceId": "S720050034", "lineName": "A2-02", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 867, "forwardedCount": 617, "unforwardedCount": 250, "lastLog": "測試產品條碼-aa6a9a3b", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-12-06T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO535463", "targetQuantity": 458, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-28T16:59:31.463859", "status": "completed", "mesForwardedCount": 14, "completionRate": 34.3}, {"workOrderNumber": "WO967534", "targetQuantity": 176, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-13T16:59:31.463859", "status": "completed", "mesForwardedCount": 8, "completionRate": 93.4}, {"workOrderNumber": "WO188469", "targetQuantity": 209, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-21T16:59:31.463859", "status": "pending", "mesForwardedCount": 12, "completionRate": 15.7}], "currentWorkOrder": 0, "currentWorkOrderProgress": 3, "displayWorkOrder": {"workOrderNumber": "WO535463", "targetQuantity": 458, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-28T16:59:31.463859", "status": "completed", "mesForwardedCount": 14, "completionRate": 34.3}, "mesErrors": [{"timestamp": "2025-06-13T16:52:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050034"}}, {"timestamp": "2025-06-12T17:12:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050034"}}, {"timestamp": "2025-06-13T01:55:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050034"}}, {"timestamp": "2025-06-12T20:00:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050034"}}, {"timestamp": "2025-06-12T20:37:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050034"}}], "recentLogs": [{"message": "測試條碼-cea9d12b", "timestamp": "2025-06-13T16:36:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-2d961012", "timestamp": "2025-06-13T16:34:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-2d98c274", "timestamp": "2025-06-13T16:48:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-6afc0365", "timestamp": "2025-06-13T16:03:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-93be5628", "timestamp": "2025-06-13T16:06:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-db271f85", "timestamp": "2025-06-13T16:33:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-480f3abe", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-0819201f", "timestamp": "2025-06-13T16:23:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-2066e1f9", "timestamp": "2025-06-13T16:20:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-431e9580", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-4f7fe01c", "timestamp": "2025-06-13T16:14:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-37d60d65", "timestamp": "2025-06-13T16:14:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-de88f520", "timestamp": "2025-06-13T16:15:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-98f4874a", "timestamp": "2025-06-13T16:15:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}]}, "S720050035": {"deviceId": "S720050035", "lineName": "A2-02", "sectionName": "ASSEMBLY", "groupName": "INJECTION", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 874, "forwardedCount": 175, "unforwardedCount": 699, "lastLog": "測試產品條碼-34bfbeba", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-09-15T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO740903", "targetQuantity": 155, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-15T16:59:31.463859", "status": "completed", "mesForwardedCount": 94, "completionRate": 54.4}, {"workOrderNumber": "WO332240", "targetQuantity": 465, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-03T16:59:31.463859", "status": "pending", "mesForwardedCount": 31, "completionRate": 66.4}, {"workOrderNumber": "WO179570", "targetQuantity": 349, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-10T16:59:31.463859", "status": "active", "mesForwardedCount": 22, "completionRate": 59.6}], "currentWorkOrder": 1, "currentWorkOrderProgress": 96, "displayWorkOrder": {"workOrderNumber": "WO332240", "targetQuantity": 465, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-03T16:59:31.463859", "status": "pending", "mesForwardedCount": 31, "completionRate": 66.4}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-95cbcc48", "timestamp": "2025-06-13T16:05:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-904667b9", "timestamp": "2025-06-13T16:09:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-33a32e4e", "timestamp": "2025-06-13T16:40:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-c97322e9", "timestamp": "2025-06-13T16:11:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-0ce40a0e", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-4bc4dc26", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-240cf2cd", "timestamp": "2025-06-13T16:28:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-bf968a0c", "timestamp": "2025-06-13T16:21:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-25da8179", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-6ac6c98a", "timestamp": "2025-06-13T16:34:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-a4893fc5", "timestamp": "2025-06-13T16:53:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-4f1776e0", "timestamp": "2025-06-13T16:37:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-8a1add5b", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-2457de9a", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-bcc91f8a", "timestamp": "2025-06-13T16:39:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-bada9cd0", "timestamp": "2025-06-13T16:20:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-36eea0d8", "timestamp": "2025-06-13T16:35:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-4b0c0125", "timestamp": "2025-06-13T16:03:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-1a2b1a9f", "timestamp": "2025-06-13T16:24:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}]}, "S720050036": {"deviceId": "S720050036", "lineName": "B1-02", "sectionName": "INJECTION", "groupName": "PACK", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 519, "forwardedCount": 105, "unforwardedCount": 414, "lastLog": "測試產品條碼-c09b0c32", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-11-05T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO469793", "targetQuantity": 376, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-22T16:59:31.463859", "status": "active", "mesForwardedCount": 88, "completionRate": 11.5}, {"workOrderNumber": "WO934745", "targetQuantity": 441, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-06T16:59:31.463859", "status": "active", "mesForwardedCount": 52, "completionRate": 7.7}, {"workOrderNumber": "WO702573", "targetQuantity": 72, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-12T16:59:31.463859", "status": "pending", "mesForwardedCount": 57, "completionRate": 72.2}], "currentWorkOrder": 1, "currentWorkOrderProgress": 20, "displayWorkOrder": {"workOrderNumber": "WO934745", "targetQuantity": 441, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-06T16:59:31.463859", "status": "active", "mesForwardedCount": 52, "completionRate": 7.7}, "mesErrors": [{"timestamp": "2025-06-13T09:47:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050036"}}, {"timestamp": "2025-06-13T12:47:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050036"}}, {"timestamp": "2025-06-13T13:28:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050036"}}, {"timestamp": "2025-06-13T12:12:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050036"}}, {"timestamp": "2025-06-13T16:19:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050036"}}], "recentLogs": [{"message": "測試條碼-6aad9fda", "timestamp": "2025-06-13T16:54:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-e1bc34e5", "timestamp": "2025-06-13T16:06:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-aab9b852", "timestamp": "2025-06-13T16:52:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-7790fb7b", "timestamp": "2025-06-13T16:03:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-0c2fb3f3", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-c09a21ae", "timestamp": "2025-06-13T16:38:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-308ec0d3", "timestamp": "2025-06-13T16:23:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-934f2c33", "timestamp": "2025-06-13T16:35:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-8060770c", "timestamp": "2025-06-13T16:52:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-d2c4cc91", "timestamp": "2025-06-13T16:31:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-3da0136d", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-8d6a86a2", "timestamp": "2025-06-13T16:38:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-e4b7f169", "timestamp": "2025-06-13T16:37:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}]}, "S720050037": {"deviceId": "S720050037", "lineName": "A1-01", "sectionName": "PACKAGING", "groupName": "ASSEMBLY", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 18, "forwardedCount": 9, "unforwardedCount": 9, "lastLog": "測試產品條碼-74cd2499", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-06-09T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO140153", "targetQuantity": 262, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-24T16:59:31.463859", "status": "active", "mesForwardedCount": 46, "completionRate": 2.0}, {"workOrderNumber": "WO153777", "targetQuantity": 352, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-23T16:59:31.463859", "status": "pending", "mesForwardedCount": 47, "completionRate": 96.0}, {"workOrderNumber": "WO196245", "targetQuantity": 144, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-15T16:59:31.463859", "status": "completed", "mesForwardedCount": 39, "completionRate": 13.1}], "currentWorkOrder": 1, "currentWorkOrderProgress": 66, "displayWorkOrder": {"workOrderNumber": "WO153777", "targetQuantity": 352, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-23T16:59:31.463859", "status": "pending", "mesForwardedCount": 47, "completionRate": 96.0}, "mesErrors": [{"timestamp": "2025-06-13T13:41:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050037"}}, {"timestamp": "2025-06-13T15:26:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050037"}}, {"timestamp": "2025-06-13T09:16:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050037"}}], "recentLogs": [{"message": "測試條碼-4e073f2a", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-f35978c6", "timestamp": "2025-06-13T16:46:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-2e822883", "timestamp": "2025-06-13T16:41:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-327c17b4", "timestamp": "2025-06-13T16:08:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e6891078", "timestamp": "2025-06-13T16:21:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}]}, "S720050038": {"deviceId": "S720050038", "lineName": "A1-01", "sectionName": "TESTING", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 559, "forwardedCount": 281, "unforwardedCount": 278, "lastLog": "測試產品條碼-d5664326", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-04-03T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO537592", "targetQuantity": 463, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-23T16:59:31.463859", "status": "active", "mesForwardedCount": 66, "completionRate": 56.2}, {"workOrderNumber": "WO163427", "targetQuantity": 477, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-15T16:59:31.463859", "status": "active", "mesForwardedCount": 76, "completionRate": 86.2}, {"workOrderNumber": "WO181035", "targetQuantity": 342, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-14T16:59:31.463859", "status": "active", "mesForwardedCount": 19, "completionRate": 16.7}], "currentWorkOrder": 2, "currentWorkOrderProgress": 21, "displayWorkOrder": {"workOrderNumber": "WO181035", "targetQuantity": 342, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-14T16:59:31.463859", "status": "active", "mesForwardedCount": 19, "completionRate": 16.7}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-8ee7e45e", "timestamp": "2025-06-13T16:41:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-d4d76bfc", "timestamp": "2025-06-13T16:44:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-293c5d21", "timestamp": "2025-06-13T16:51:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-3d399963", "timestamp": "2025-06-13T16:23:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-025e3b46", "timestamp": "2025-06-13T16:07:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-36e0a54b", "timestamp": "2025-06-13T16:50:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-057d58f0", "timestamp": "2025-06-13T16:03:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-1d553250", "timestamp": "2025-06-13T16:09:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-2db75282", "timestamp": "2025-06-13T16:24:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-99ef5880", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}]}, "S720050039": {"deviceId": "S720050039", "lineName": "B1-02", "sectionName": "INJECTION", "groupName": "ASSEMBLY", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 15, "forwardedCount": 10, "unforwardedCount": 5, "lastLog": "測試產品條碼-82b6c4d1", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-12-10T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO449976", "targetQuantity": 352, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-21T16:59:31.463859", "status": "pending", "mesForwardedCount": 45, "completionRate": 44.0}, {"workOrderNumber": "WO230355", "targetQuantity": 470, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-29T16:59:31.463859", "status": "pending", "mesForwardedCount": 50, "completionRate": 23.0}, {"workOrderNumber": "WO813045", "targetQuantity": 317, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-06T16:59:31.463859", "status": "completed", "mesForwardedCount": 52, "completionRate": 1.5}], "currentWorkOrder": 0, "currentWorkOrderProgress": 2, "displayWorkOrder": {"workOrderNumber": "WO449976", "targetQuantity": 352, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-21T16:59:31.463859", "status": "pending", "mesForwardedCount": 45, "completionRate": 44.0}, "mesErrors": [{"timestamp": "2025-06-13T00:15:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050039"}}], "recentLogs": [{"message": "測試條碼-40a5b51f", "timestamp": "2025-06-13T16:16:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-4f6a77d1", "timestamp": "2025-06-13T16:33:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-ee3079d5", "timestamp": "2025-06-13T16:56:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-1474111a", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-5f525d54", "timestamp": "2025-06-13T16:41:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-2addb372", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-54e864bd", "timestamp": "2025-06-13T16:11:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-96e6dc26", "timestamp": "2025-06-13T16:09:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-71c4e1a1", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-1b85429b", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-20743afd", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1609f2af", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-c08f73d3", "timestamp": "2025-06-13T16:22:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-8e9257b3", "timestamp": "2025-06-13T16:30:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-5067f627", "timestamp": "2025-06-13T16:53:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-8369c818", "timestamp": "2025-06-13T16:01:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-086515e5", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}]}, "S720050040": {"deviceId": "S720050040", "lineName": "A2-02", "sectionName": "PACKAGING", "groupName": "INJECTION", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 915, "forwardedCount": 665, "unforwardedCount": 250, "lastLog": "測試產品條碼-0e58379e", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-04-04T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO421629", "targetQuantity": 168, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-25T16:59:31.463859", "status": "pending", "mesForwardedCount": 6, "completionRate": 32.0}, {"workOrderNumber": "WO100803", "targetQuantity": 405, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-23T16:59:31.463859", "status": "completed", "mesForwardedCount": 65, "completionRate": 91.3}, {"workOrderNumber": "WO477931", "targetQuantity": 295, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-30T16:59:31.463859", "status": "pending", "mesForwardedCount": 34, "completionRate": 42.0}], "currentWorkOrder": 1, "currentWorkOrderProgress": 75, "displayWorkOrder": {"workOrderNumber": "WO100803", "targetQuantity": 405, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-23T16:59:31.463859", "status": "completed", "mesForwardedCount": 65, "completionRate": 91.3}, "mesErrors": [{"timestamp": "2025-06-13T08:41:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050040"}}, {"timestamp": "2025-06-13T05:25:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050040"}}], "recentLogs": [{"message": "測試條碼-6f4cc682", "timestamp": "2025-06-13T16:43:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-e40274f5", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-277d7878", "timestamp": "2025-06-13T16:37:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-1be4193b", "timestamp": "2025-06-13T16:22:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a63cf013", "timestamp": "2025-06-13T16:50:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-c5ced10c", "timestamp": "2025-06-13T16:29:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-3c92149a", "timestamp": "2025-06-13T16:00:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-767746aa", "timestamp": "2025-06-13T16:37:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-c327ca5e", "timestamp": "2025-06-13T16:54:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-46d6fd72", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-f35ea11b", "timestamp": "2025-06-13T16:06:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-4d00ee51", "timestamp": "2025-06-13T16:34:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-78cf7e33", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-e00941ea", "timestamp": "2025-06-13T16:01:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-46f717db", "timestamp": "2025-06-13T16:35:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-c82a78b1", "timestamp": "2025-06-13T16:04:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-8dbfcbe8", "timestamp": "2025-06-13T15:59:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}]}, "S720050041": {"deviceId": "S720050041", "lineName": "A2-02", "sectionName": "INJECTION", "groupName": "ASSEMBLY", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 722, "forwardedCount": 56, "unforwardedCount": 666, "lastLog": "測試產品條碼-5a760bc6", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-09-11T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO901920", "targetQuantity": 459, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-25T16:59:31.463859", "status": "pending", "mesForwardedCount": 71, "completionRate": 73.1}, {"workOrderNumber": "WO384142", "targetQuantity": 347, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-07T16:59:31.463859", "status": "pending", "mesForwardedCount": 0, "completionRate": 22.8}, {"workOrderNumber": "WO296957", "targetQuantity": 199, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-11T16:59:31.463859", "status": "active", "mesForwardedCount": 38, "completionRate": 0.8}], "currentWorkOrder": 1, "currentWorkOrderProgress": 75, "displayWorkOrder": {"workOrderNumber": "WO384142", "targetQuantity": 347, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-07T16:59:31.463859", "status": "pending", "mesForwardedCount": 0, "completionRate": 22.8}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-4abb6729", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-7ecd6512", "timestamp": "2025-06-13T16:31:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-8cc33cd1", "timestamp": "2025-06-13T16:47:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-201bed5f", "timestamp": "2025-06-13T16:30:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-01260233", "timestamp": "2025-06-13T16:34:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-a5cba959", "timestamp": "2025-06-13T16:06:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-c61f5818", "timestamp": "2025-06-13T16:54:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-a07814bf", "timestamp": "2025-06-13T16:00:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-07759ff8", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-663b07bd", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-14cf2517", "timestamp": "2025-06-13T16:52:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-f6d41527", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-574d9c53", "timestamp": "2025-06-13T16:38:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-2daac468", "timestamp": "2025-06-13T16:51:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-9e59090c", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-e9bc120f", "timestamp": "2025-06-13T16:32:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-09e5185b", "timestamp": "2025-06-13T16:41:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}]}, "S720050042": {"deviceId": "S720050042", "lineName": "B2-01", "sectionName": "INJECTION", "groupName": "PACK", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 18, "forwardedCount": 12, "unforwardedCount": 6, "lastLog": "測試產品條碼-bca773f5", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-10-03T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO511532", "targetQuantity": 195, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-19T16:59:31.463859", "status": "pending", "mesForwardedCount": 45, "completionRate": 20.2}, {"workOrderNumber": "WO185309", "targetQuantity": 268, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-22T16:59:31.463859", "status": "pending", "mesForwardedCount": 10, "completionRate": 49.1}, {"workOrderNumber": "WO363114", "targetQuantity": 75, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-05T16:59:31.463859", "status": "pending", "mesForwardedCount": 78, "completionRate": 58.6}], "currentWorkOrder": 0, "currentWorkOrderProgress": 47, "displayWorkOrder": {"workOrderNumber": "WO511532", "targetQuantity": 195, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-19T16:59:31.463859", "status": "pending", "mesForwardedCount": 45, "completionRate": 20.2}, "mesErrors": [{"timestamp": "2025-06-13T02:55:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050042"}}, {"timestamp": "2025-06-13T01:55:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050042"}}, {"timestamp": "2025-06-13T16:58:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050042"}}, {"timestamp": "2025-06-13T16:50:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050042"}}, {"timestamp": "2025-06-12T17:53:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050042"}}], "recentLogs": [{"message": "測試條碼-943f3394", "timestamp": "2025-06-13T16:39:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-dbfd7fb5", "timestamp": "2025-06-13T16:20:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-744f3c0f", "timestamp": "2025-06-13T16:53:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-f452814e", "timestamp": "2025-06-13T16:43:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-5d7977a0", "timestamp": "2025-06-13T16:09:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-80924d23", "timestamp": "2025-06-13T16:29:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-29b77c11", "timestamp": "2025-06-13T16:03:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-0a61c3a8", "timestamp": "2025-06-13T16:37:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-f70bb0c0", "timestamp": "2025-06-13T16:08:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-278789d7", "timestamp": "2025-06-13T16:50:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-8463e090", "timestamp": "2025-06-13T16:50:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}]}, "S720050043": {"deviceId": "S720050043", "lineName": "B2-01", "sectionName": "PACKAGING", "groupName": "INJECTION", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 600, "forwardedCount": 277, "unforwardedCount": 323, "lastLog": "測試產品條碼-6dee4c48", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-05-31T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO807646", "targetQuantity": 367, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-27T16:59:31.463859", "status": "pending", "mesForwardedCount": 47, "completionRate": 97.0}, {"workOrderNumber": "WO590344", "targetQuantity": 359, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-04T16:59:31.463859", "status": "completed", "mesForwardedCount": 48, "completionRate": 51.9}, {"workOrderNumber": "WO440057", "targetQuantity": 261, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-11T16:59:31.463859", "status": "active", "mesForwardedCount": 89, "completionRate": 68.6}], "currentWorkOrder": 0, "currentWorkOrderProgress": 28, "displayWorkOrder": {"workOrderNumber": "WO807646", "targetQuantity": 367, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-27T16:59:31.463859", "status": "pending", "mesForwardedCount": 47, "completionRate": 97.0}, "mesErrors": [{"timestamp": "2025-06-13T06:28:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050043"}}], "recentLogs": [{"message": "測試條碼-bce0ba52", "timestamp": "2025-06-13T16:53:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-53448983", "timestamp": "2025-06-13T16:52:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-4cab62d7", "timestamp": "2025-06-13T16:18:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-0fd58027", "timestamp": "2025-06-13T16:32:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-2b6d00fe", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-7123bdf0", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-4ce4576c", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}]}, "S720050044": {"deviceId": "S720050044", "lineName": "B1-01", "sectionName": "INJECTION", "groupName": "TEST", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 168, "forwardedCount": 78, "unforwardedCount": 90, "lastLog": "測試產品條碼-8cb3cdbd", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-09-27T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO889550", "targetQuantity": 354, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-01T16:59:31.463859", "status": "pending", "mesForwardedCount": 48, "completionRate": 80.7}, {"workOrderNumber": "WO766668", "targetQuantity": 112, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-15T16:59:31.463859", "status": "active", "mesForwardedCount": 72, "completionRate": 53.8}, {"workOrderNumber": "WO228542", "targetQuantity": 401, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-20T16:59:31.463859", "status": "active", "mesForwardedCount": 79, "completionRate": 68.9}], "currentWorkOrder": 0, "currentWorkOrderProgress": 39, "displayWorkOrder": {"workOrderNumber": "WO889550", "targetQuantity": 354, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-01T16:59:31.463859", "status": "pending", "mesForwardedCount": 48, "completionRate": 80.7}, "mesErrors": [{"timestamp": "2025-06-13T02:50:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050044"}}, {"timestamp": "2025-06-13T13:24:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050044"}}, {"timestamp": "2025-06-13T01:36:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050044"}}, {"timestamp": "2025-06-12T22:56:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050044"}}, {"timestamp": "2025-06-12T23:55:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050044"}}], "recentLogs": [{"message": "測試條碼-1bb429e1", "timestamp": "2025-06-13T16:16:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-33fcaeab", "timestamp": "2025-06-13T16:25:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-dce02fe7", "timestamp": "2025-06-13T16:10:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-059d9997", "timestamp": "2025-06-13T16:15:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-3d0d104e", "timestamp": "2025-06-13T16:41:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-f346b8f8", "timestamp": "2025-06-13T16:14:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-8eb9ba99", "timestamp": "2025-06-13T16:28:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-ab36561e", "timestamp": "2025-06-13T16:15:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-960a69a6", "timestamp": "2025-06-13T16:13:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-c6b21543", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-237b2944", "timestamp": "2025-06-13T16:23:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-c7ed5149", "timestamp": "2025-06-13T16:13:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-7b633bf4", "timestamp": "2025-06-13T16:04:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-a75a2355", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-c975807d", "timestamp": "2025-06-13T16:52:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}]}, "S720050045": {"deviceId": "S720050045", "lineName": "B1-02", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_03", "isActive": true, "isMonitoring": true, "productCount": 257, "forwardedCount": 13, "unforwardedCount": 244, "lastLog": "測試產品條碼-f944481e", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-01-17T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO779240", "targetQuantity": 318, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-20T16:59:31.463859", "status": "active", "mesForwardedCount": 42, "completionRate": 59.2}, {"workOrderNumber": "WO712180", "targetQuantity": 182, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-16T16:59:31.463859", "status": "active", "mesForwardedCount": 93, "completionRate": 82.9}, {"workOrderNumber": "WO726573", "targetQuantity": 204, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-27T16:59:31.463859", "status": "pending", "mesForwardedCount": 91, "completionRate": 80.8}], "currentWorkOrder": 0, "currentWorkOrderProgress": 12, "displayWorkOrder": {"workOrderNumber": "WO779240", "targetQuantity": 318, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-20T16:59:31.463859", "status": "active", "mesForwardedCount": 42, "completionRate": 59.2}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-427723f8", "timestamp": "2025-06-13T16:30:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-be35d195", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-a774f67d", "timestamp": "2025-06-13T16:07:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-d0b34a8a", "timestamp": "2025-06-13T16:09:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-e9c06bf7", "timestamp": "2025-06-13T16:58:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-56e04f29", "timestamp": "2025-06-13T16:18:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-b8eecc97", "timestamp": "2025-06-13T16:37:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-1662cf41", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-b29670ae", "timestamp": "2025-06-13T16:54:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-a152d454", "timestamp": "2025-06-13T16:54:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-ab25df48", "timestamp": "2025-06-13T16:16:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-7d6df8b4", "timestamp": "2025-06-13T16:31:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-faa6a78a", "timestamp": "2025-06-13T16:21:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-bc232d1d", "timestamp": "2025-06-13T16:04:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-99562c10", "timestamp": "2025-06-13T16:38:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-355debe3", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-ea8a7a43", "timestamp": "2025-06-13T16:50:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-9edc221b", "timestamp": "2025-06-13T16:17:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a783ce39", "timestamp": "2025-06-13T16:10:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}]}, "S720050046": {"deviceId": "S720050046", "lineName": "A2-01", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 604, "forwardedCount": 417, "unforwardedCount": 187, "lastLog": "測試產品條碼-6d3ebe1c", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-06-03T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO868138", "targetQuantity": 271, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-10T16:59:31.463859", "status": "pending", "mesForwardedCount": 82, "completionRate": 80.5}, {"workOrderNumber": "WO568877", "targetQuantity": 175, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-17T16:59:31.463859", "status": "completed", "mesForwardedCount": 74, "completionRate": 45.7}, {"workOrderNumber": "WO739590", "targetQuantity": 385, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-20T16:59:31.463859", "status": "completed", "mesForwardedCount": 67, "completionRate": 95.8}], "currentWorkOrder": 0, "currentWorkOrderProgress": 56, "displayWorkOrder": {"workOrderNumber": "WO868138", "targetQuantity": 271, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-10T16:59:31.463859", "status": "pending", "mesForwardedCount": 82, "completionRate": 80.5}, "mesErrors": [{"timestamp": "2025-06-12T23:45:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050046"}}, {"timestamp": "2025-06-13T12:01:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050046"}}, {"timestamp": "2025-06-13T14:12:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050046"}}, {"timestamp": "2025-06-13T10:10:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050046"}}, {"timestamp": "2025-06-13T12:33:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050046"}}], "recentLogs": [{"message": "測試條碼-f521c122", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-0d61a3ed", "timestamp": "2025-06-13T16:31:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-22e78f4d", "timestamp": "2025-06-13T16:05:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-818d5b36", "timestamp": "2025-06-13T16:43:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-b393aec2", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-2f3b96b7", "timestamp": "2025-06-13T16:48:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-072762e8", "timestamp": "2025-06-13T16:13:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-8bd0d8b2", "timestamp": "2025-06-13T15:59:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-96350473", "timestamp": "2025-06-13T16:46:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-50fb4d2f", "timestamp": "2025-06-13T16:44:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-c7598bd7", "timestamp": "2025-06-13T16:03:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-dc12d0cc", "timestamp": "2025-06-13T15:59:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-c07625cb", "timestamp": "2025-06-13T16:39:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-1474394c", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-499aa57b", "timestamp": "2025-06-13T16:28:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-205aa<PERSON>e", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}]}, "S720050047": {"deviceId": "S720050047", "lineName": "A2-01", "sectionName": "PACKAGING", "groupName": "ASSEMBLY", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 12, "forwardedCount": 5, "unforwardedCount": 7, "lastLog": "測試產品條碼-2ee267eb", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-06-21T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO194756", "targetQuantity": 267, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-06T16:59:31.463859", "status": "active", "mesForwardedCount": 16, "completionRate": 10.4}, {"workOrderNumber": "WO723083", "targetQuantity": 373, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-06T16:59:31.463859", "status": "active", "mesForwardedCount": 54, "completionRate": 33.3}, {"workOrderNumber": "WO878769", "targetQuantity": 468, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-23T16:59:31.463859", "status": "pending", "mesForwardedCount": 94, "completionRate": 87.1}], "currentWorkOrder": 1, "currentWorkOrderProgress": 46, "displayWorkOrder": {"workOrderNumber": "WO723083", "targetQuantity": 373, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-06T16:59:31.463859", "status": "active", "mesForwardedCount": 54, "completionRate": 33.3}, "mesErrors": [{"timestamp": "2025-06-13T09:00:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050047"}}, {"timestamp": "2025-06-12T22:02:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050047"}}, {"timestamp": "2025-06-12T18:36:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050047"}}, {"timestamp": "2025-06-13T07:06:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050047"}}, {"timestamp": "2025-06-13T14:03:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050047"}}], "recentLogs": [{"message": "測試條碼-1667e245", "timestamp": "2025-06-13T16:06:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-84e3dfd5", "timestamp": "2025-06-13T16:14:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-1fcb5501", "timestamp": "2025-06-13T16:50:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-72700a2f", "timestamp": "2025-06-13T16:17:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-3b444223", "timestamp": "2025-06-13T16:29:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-6eefa896", "timestamp": "2025-06-13T16:57:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}]}, "S720050048": {"deviceId": "S720050048", "lineName": "B2-02", "sectionName": "PACKAGING", "groupName": "INJECTION", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 482, "forwardedCount": 388, "unforwardedCount": 94, "lastLog": "測試產品條碼-54c0abfd", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-08-26T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO497119", "targetQuantity": 134, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-06T16:59:31.463859", "status": "completed", "mesForwardedCount": 14, "completionRate": 51.0}, {"workOrderNumber": "WO852523", "targetQuantity": 179, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-07T16:59:31.463859", "status": "pending", "mesForwardedCount": 60, "completionRate": 96.1}, {"workOrderNumber": "WO860478", "targetQuantity": 235, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-16T16:59:31.463859", "status": "pending", "mesForwardedCount": 50, "completionRate": 67.1}], "currentWorkOrder": 2, "currentWorkOrderProgress": 9, "displayWorkOrder": {"workOrderNumber": "WO860478", "targetQuantity": 235, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-16T16:59:31.463859", "status": "pending", "mesForwardedCount": 50, "completionRate": 67.1}, "mesErrors": [{"timestamp": "2025-06-13T07:32:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050048"}}, {"timestamp": "2025-06-13T14:20:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050048"}}, {"timestamp": "2025-06-13T12:50:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050048"}}, {"timestamp": "2025-06-12T18:47:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050048"}}, {"timestamp": "2025-06-13T06:21:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050048"}}], "recentLogs": [{"message": "測試條碼-8869bd30", "timestamp": "2025-06-13T16:36:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-49b87d57", "timestamp": "2025-06-13T16:04:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-b760f80b", "timestamp": "2025-06-13T16:37:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-170f302c", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-f86e6ad6", "timestamp": "2025-06-13T16:57:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-4ac3526d", "timestamp": "2025-06-13T16:34:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-3a0d4e4d", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-7d1f0d4e", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-68180301", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-ff05a09b", "timestamp": "2025-06-13T16:13:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e467c392", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-03675a0e", "timestamp": "2025-06-13T16:29:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-d8b17607", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-df12332a", "timestamp": "2025-06-13T16:06:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-5fa5e8d2", "timestamp": "2025-06-13T16:34:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-74817dca", "timestamp": "2025-06-13T16:36:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}]}, "S720050049": {"deviceId": "S720050049", "lineName": "B2-02", "sectionName": "TESTING", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 464, "forwardedCount": 51, "unforwardedCount": 413, "lastLog": "測試產品條碼-c3169a6d", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-10-14T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO418015", "targetQuantity": 413, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-30T16:59:31.463859", "status": "active", "mesForwardedCount": 39, "completionRate": 14.8}, {"workOrderNumber": "WO871745", "targetQuantity": 317, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-21T16:59:31.463859", "status": "pending", "mesForwardedCount": 20, "completionRate": 90.1}, {"workOrderNumber": "WO239623", "targetQuantity": 268, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-20T16:59:31.463859", "status": "pending", "mesForwardedCount": 89, "completionRate": 20.7}], "currentWorkOrder": 0, "currentWorkOrderProgress": 53, "displayWorkOrder": {"workOrderNumber": "WO418015", "targetQuantity": 413, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-30T16:59:31.463859", "status": "active", "mesForwardedCount": 39, "completionRate": 14.8}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-f308ad71", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-3ed7b7b0", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-fc9cd158", "timestamp": "2025-06-13T16:38:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-a2838172", "timestamp": "2025-06-13T16:46:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-2dfc8f7d", "timestamp": "2025-06-13T16:58:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-1214ea79", "timestamp": "2025-06-13T16:18:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-a349debf", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}]}, "S720050050": {"deviceId": "S720050050", "lineName": "B2-02", "sectionName": "ASSEMBLY", "groupName": "ASSEMBLY", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 637, "forwardedCount": 187, "unforwardedCount": 450, "lastLog": "測試產品條碼-06cb8ecc", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-01-12T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO664830", "targetQuantity": 438, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-09T16:59:31.463859", "status": "pending", "mesForwardedCount": 89, "completionRate": 14.6}, {"workOrderNumber": "WO962461", "targetQuantity": 220, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-29T16:59:31.463859", "status": "completed", "mesForwardedCount": 40, "completionRate": 11.0}, {"workOrderNumber": "WO486879", "targetQuantity": 440, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-10T16:59:31.463859", "status": "active", "mesForwardedCount": 74, "completionRate": 3.0}], "currentWorkOrder": 0, "currentWorkOrderProgress": 8, "displayWorkOrder": {"workOrderNumber": "WO664830", "targetQuantity": 438, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-09T16:59:31.463859", "status": "pending", "mesForwardedCount": 89, "completionRate": 14.6}, "mesErrors": [{"timestamp": "2025-06-13T12:38:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050050"}}, {"timestamp": "2025-06-12T19:37:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050050"}}, {"timestamp": "2025-06-12T21:47:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050050"}}], "recentLogs": [{"message": "測試條碼-b60acfe0", "timestamp": "2025-06-13T16:47:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-92396ddb", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-6916ce15", "timestamp": "2025-06-13T16:00:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-6b3369c0", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-a4caaa47", "timestamp": "2025-06-13T16:36:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-e73d73d5", "timestamp": "2025-06-13T16:54:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-a623d985", "timestamp": "2025-06-13T16:57:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-50d02484", "timestamp": "2025-06-13T16:32:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-61032d92", "timestamp": "2025-06-13T16:57:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-4a401551", "timestamp": "2025-06-13T16:21:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-cbfcadc8", "timestamp": "2025-06-13T16:22:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-90b3006b", "timestamp": "2025-06-13T16:43:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-458cb9b4", "timestamp": "2025-06-13T16:05:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-3b0a354a", "timestamp": "2025-06-13T16:53:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d4b988c5", "timestamp": "2025-06-13T16:36:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}]}, "S720050051": {"deviceId": "S720050051", "lineName": "B1-01", "sectionName": "TESTING", "groupName": "TEST", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 687, "forwardedCount": 6, "unforwardedCount": 681, "lastLog": "測試產品條碼-13c31cfe", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-10-08T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO208750", "targetQuantity": 469, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-13T16:59:31.463859", "status": "pending", "mesForwardedCount": 61, "completionRate": 2.2}, {"workOrderNumber": "WO141404", "targetQuantity": 490, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-31T16:59:31.463859", "status": "completed", "mesForwardedCount": 27, "completionRate": 71.8}, {"workOrderNumber": "WO133490", "targetQuantity": 266, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-29T16:59:31.463859", "status": "pending", "mesForwardedCount": 86, "completionRate": 85.6}], "currentWorkOrder": 0, "currentWorkOrderProgress": 71, "displayWorkOrder": {"workOrderNumber": "WO208750", "targetQuantity": 469, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-13T16:59:31.463859", "status": "pending", "mesForwardedCount": 61, "completionRate": 2.2}, "mesErrors": [{"timestamp": "2025-06-13T10:52:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050051"}}, {"timestamp": "2025-06-12T19:41:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050051"}}, {"timestamp": "2025-06-13T03:26:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050051"}}], "recentLogs": [{"message": "測試條碼-c152154e", "timestamp": "2025-06-13T16:31:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-1c77d02a", "timestamp": "2025-06-13T16:36:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-dc39c81f", "timestamp": "2025-06-13T16:58:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-8347ac84", "timestamp": "2025-06-13T16:37:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-88b66955", "timestamp": "2025-06-13T16:50:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-9bb6d10d", "timestamp": "2025-06-13T16:20:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-4508930f", "timestamp": "2025-06-13T16:41:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-0d542b33", "timestamp": "2025-06-13T16:37:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-cfdbb42e", "timestamp": "2025-06-13T16:18:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-84ec4253", "timestamp": "2025-06-13T16:01:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-4c66e57f", "timestamp": "2025-06-13T16:20:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-922c1974", "timestamp": "2025-06-13T16:33:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-fc381a5f", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-f02090a8", "timestamp": "2025-06-13T16:35:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-6a672677", "timestamp": "2025-06-13T16:36:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-86ad49f6", "timestamp": "2025-06-13T16:13:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-e791a5cd", "timestamp": "2025-06-13T16:46:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f0b28a80", "timestamp": "2025-06-13T16:18:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-18906ac1", "timestamp": "2025-06-13T16:43:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}]}, "S720050052": {"deviceId": "S720050052", "lineName": "B1-01", "sectionName": "ASSEMBLY", "groupName": "TEST", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 250, "forwardedCount": 35, "unforwardedCount": 215, "lastLog": "測試產品條碼-75c990e2", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-04-04T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO420531", "targetQuantity": 294, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-21T16:59:31.463859", "status": "completed", "mesForwardedCount": 53, "completionRate": 95.3}, {"workOrderNumber": "WO299763", "targetQuantity": 286, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-09T16:59:31.463859", "status": "pending", "mesForwardedCount": 62, "completionRate": 51.6}, {"workOrderNumber": "WO758936", "targetQuantity": 370, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-24T16:59:31.463859", "status": "pending", "mesForwardedCount": 39, "completionRate": 11.1}], "currentWorkOrder": 0, "currentWorkOrderProgress": 76, "displayWorkOrder": {"workOrderNumber": "WO420531", "targetQuantity": 294, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-21T16:59:31.463859", "status": "completed", "mesForwardedCount": 53, "completionRate": 95.3}, "mesErrors": [{"timestamp": "2025-06-13T11:20:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050052"}}, {"timestamp": "2025-06-13T06:29:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050052"}}], "recentLogs": [{"message": "測試條碼-a2518c06", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e8<PERSON><PERSON>ab", "timestamp": "2025-06-13T16:29:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-5970174a", "timestamp": "2025-06-13T16:10:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-b87d54a6", "timestamp": "2025-06-13T16:05:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-11ba4f63", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-47ddb9d0", "timestamp": "2025-06-13T16:40:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-2f85bb00", "timestamp": "2025-06-13T16:30:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-c9f6386c", "timestamp": "2025-06-13T16:28:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-b2cd6d61", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-23d9b17c", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-6a7cc44a", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-7e3dae99", "timestamp": "2025-06-13T16:33:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-654297c2", "timestamp": "2025-06-13T16:03:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-235dc024", "timestamp": "2025-06-13T16:40:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-114022cb", "timestamp": "2025-06-13T16:04:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-6048d0d4", "timestamp": "2025-06-13T16:58:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e057ff08", "timestamp": "2025-06-13T16:08:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1b506546", "timestamp": "2025-06-13T16:43:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}]}, "S720050053": {"deviceId": "S720050053", "lineName": "A2-01", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 604, "forwardedCount": 344, "unforwardedCount": 260, "lastLog": "測試產品條碼-25255cf9", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-09-11T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO202468", "targetQuantity": 144, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-21T16:59:31.463859", "status": "completed", "mesForwardedCount": 17, "completionRate": 95.6}, {"workOrderNumber": "WO325616", "targetQuantity": 315, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-25T16:59:31.463859", "status": "active", "mesForwardedCount": 89, "completionRate": 56.4}, {"workOrderNumber": "WO131071", "targetQuantity": 119, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-01T16:59:31.463859", "status": "completed", "mesForwardedCount": 63, "completionRate": 56.4}], "currentWorkOrder": 0, "currentWorkOrderProgress": 55, "displayWorkOrder": {"workOrderNumber": "WO202468", "targetQuantity": 144, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-21T16:59:31.463859", "status": "completed", "mesForwardedCount": 17, "completionRate": 95.6}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-da<PERSON><PERSON>ff", "timestamp": "2025-06-13T16:00:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-5f93034b", "timestamp": "2025-06-13T16:11:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-b2803201", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-21c3d4aa", "timestamp": "2025-06-13T16:33:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-0905e029", "timestamp": "2025-06-13T16:05:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-4ebcac08", "timestamp": "2025-06-13T16:36:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}]}, "S720050054": {"deviceId": "S720050054", "lineName": "B2-02", "sectionName": "PACKAGING", "groupName": "INJECTION", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 284, "forwardedCount": 92, "unforwardedCount": 192, "lastLog": "測試產品條碼-93b72bf5", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-11-26T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO149754", "targetQuantity": 204, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-26T16:59:31.463859", "status": "completed", "mesForwardedCount": 96, "completionRate": 55.3}, {"workOrderNumber": "WO612401", "targetQuantity": 350, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-13T16:59:31.463859", "status": "active", "mesForwardedCount": 62, "completionRate": 15.7}, {"workOrderNumber": "WO907953", "targetQuantity": 482, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-07T16:59:31.463859", "status": "pending", "mesForwardedCount": 81, "completionRate": 92.9}], "currentWorkOrder": 0, "currentWorkOrderProgress": 37, "displayWorkOrder": {"workOrderNumber": "WO149754", "targetQuantity": 204, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-26T16:59:31.463859", "status": "completed", "mesForwardedCount": 96, "completionRate": 55.3}, "mesErrors": [{"timestamp": "2025-06-13T06:55:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050054"}}, {"timestamp": "2025-06-13T12:50:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050054"}}, {"timestamp": "2025-06-13T10:18:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050054"}}, {"timestamp": "2025-06-13T15:23:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050054"}}, {"timestamp": "2025-06-13T15:27:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050054"}}], "recentLogs": [{"message": "測試條碼-86d584a6", "timestamp": "2025-06-13T16:34:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-f8aec155", "timestamp": "2025-06-13T16:12:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-abec9958", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-b15d94b0", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-eb2b84e6", "timestamp": "2025-06-13T16:57:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-e8846ac5", "timestamp": "2025-06-13T16:18:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-37f46f2d", "timestamp": "2025-06-13T16:39:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}]}, "S720050055": {"deviceId": "S720050055", "lineName": "B2-02", "sectionName": "ASSEMBLY", "groupName": "ASSEMBLY", "stationName": "STATION_03", "isActive": true, "isMonitoring": true, "productCount": 634, "forwardedCount": 185, "unforwardedCount": 449, "lastLog": "測試產品條碼-26e3a832", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2025-04-21T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO438757", "targetQuantity": 153, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-04T16:59:31.463859", "status": "active", "mesForwardedCount": 13, "completionRate": 66.8}, {"workOrderNumber": "WO350287", "targetQuantity": 244, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-13T16:59:31.463859", "status": "pending", "mesForwardedCount": 35, "completionRate": 34.0}, {"workOrderNumber": "WO354146", "targetQuantity": 157, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-10T16:59:31.463859", "status": "active", "mesForwardedCount": 96, "completionRate": 56.1}], "currentWorkOrder": 2, "currentWorkOrderProgress": 19, "displayWorkOrder": {"workOrderNumber": "WO354146", "targetQuantity": 157, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-10T16:59:31.463859", "status": "active", "mesForwardedCount": 96, "completionRate": 56.1}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-2d022773", "timestamp": "2025-06-13T16:17:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-ca860180", "timestamp": "2025-06-13T16:22:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-427028a2", "timestamp": "2025-06-13T16:14:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-0294c1d8", "timestamp": "2025-06-13T16:45:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-8c9d73a6", "timestamp": "2025-06-13T16:29:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-d8571c18", "timestamp": "2025-06-13T16:29:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-cbabefbe", "timestamp": "2025-06-13T16:22:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-18c253ac", "timestamp": "2025-06-13T16:53:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}]}, "S720050056": {"deviceId": "S720050056", "lineName": "A2-01", "sectionName": "INJECTION", "groupName": "INJECTION", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 501, "forwardedCount": 255, "unforwardedCount": 246, "lastLog": "測試產品條碼-6e08a05c", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-12-12T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO117513", "targetQuantity": 276, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-23T16:59:31.463859", "status": "pending", "mesForwardedCount": 65, "completionRate": 95.2}, {"workOrderNumber": "WO223267", "targetQuantity": 311, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-02T16:59:31.463859", "status": "completed", "mesForwardedCount": 100, "completionRate": 64.7}, {"workOrderNumber": "WO299549", "targetQuantity": 379, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-13T16:59:31.463859", "status": "pending", "mesForwardedCount": 29, "completionRate": 88.6}], "currentWorkOrder": 0, "currentWorkOrderProgress": 99, "displayWorkOrder": {"workOrderNumber": "WO117513", "targetQuantity": 276, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-23T16:59:31.463859", "status": "pending", "mesForwardedCount": 65, "completionRate": 95.2}, "mesErrors": [{"timestamp": "2025-06-12T21:14:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050056"}}, {"timestamp": "2025-06-13T11:37:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050056"}}, {"timestamp": "2025-06-12T17:47:31.463859", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050056"}}, {"timestamp": "2025-06-13T00:47:31.463859", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050056"}}], "recentLogs": [{"message": "測試條碼-8d316818", "timestamp": "2025-06-13T16:58:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-3577116a", "timestamp": "2025-06-13T16:47:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-43e1fa25", "timestamp": "2025-06-13T16:26:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-884e8ca6", "timestamp": "2025-06-13T16:05:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-de6a3c90", "timestamp": "2025-06-13T16:53:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-95c92431", "timestamp": "2025-06-13T16:48:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f6345f77", "timestamp": "2025-06-13T16:42:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-d82f369b", "timestamp": "2025-06-13T16:13:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-bc205234", "timestamp": "2025-06-13T16:28:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-931475b6", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-47191b94", "timestamp": "2025-06-13T16:53:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-d91e075d", "timestamp": "2025-06-13T16:08:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-b6ddcb8c", "timestamp": "2025-06-13T16:51:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-6b312c66", "timestamp": "2025-06-13T16:21:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-33257b9c", "timestamp": "2025-06-13T16:06:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-1bd782ef", "timestamp": "2025-06-13T16:18:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-2a26652a", "timestamp": "2025-06-13T16:55:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-b5fef2a5", "timestamp": "2025-06-13T16:41:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-a557a95b", "timestamp": "2025-06-13T16:44:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-3a755a93", "timestamp": "2025-06-13T16:08:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}]}, "S720050057": {"deviceId": "S720050057", "lineName": "A2-01", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 264, "forwardedCount": 242, "unforwardedCount": 22, "lastLog": "測試產品條碼-db3f5ca3", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-11-13T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO645812", "targetQuantity": 401, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-03T16:59:31.463859", "status": "completed", "mesForwardedCount": 36, "completionRate": 69.7}, {"workOrderNumber": "WO645546", "targetQuantity": 131, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-27T16:59:31.463859", "status": "completed", "mesForwardedCount": 62, "completionRate": 54.0}, {"workOrderNumber": "WO739234", "targetQuantity": 264, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-04T16:59:31.463859", "status": "pending", "mesForwardedCount": 66, "completionRate": 44.2}], "currentWorkOrder": 2, "currentWorkOrderProgress": 81, "displayWorkOrder": {"workOrderNumber": "WO739234", "targetQuantity": 264, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-04T16:59:31.463859", "status": "pending", "mesForwardedCount": 66, "completionRate": 44.2}, "mesErrors": [{"timestamp": "2025-06-12T20:57:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050057"}}], "recentLogs": [{"message": "測試條碼-c33c8bbd", "timestamp": "2025-06-13T16:04:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-a7a5e3da", "timestamp": "2025-06-13T16:33:31.463859", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-a4770c56", "timestamp": "2025-06-13T16:57:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d054fc55", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-28a318f5", "timestamp": "2025-06-13T16:19:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-4ea70767", "timestamp": "2025-06-13T16:08:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-3e8648c7", "timestamp": "2025-06-13T16:24:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-4f48e248", "timestamp": "2025-06-13T16:03:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}]}, "S720050058": {"deviceId": "S720050058", "lineName": "A2-02", "sectionName": "PACKAGING", "groupName": "INJECTION", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 82, "forwardedCount": 37, "unforwardedCount": 45, "lastLog": "測試產品條碼-5f529e9b", "lastUpdateTime": "2025-06-13T16:59:31.463859", "createdAt": "2024-10-30T16:59:31.463859", "workOrders": [{"workOrderNumber": "WO141001", "targetQuantity": 278, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-07T16:59:31.463859", "status": "pending", "mesForwardedCount": 99, "completionRate": 82.1}, {"workOrderNumber": "WO254470", "targetQuantity": 255, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-12T16:59:31.463859", "status": "completed", "mesForwardedCount": 92, "completionRate": 52.9}, {"workOrderNumber": "WO864944", "targetQuantity": 216, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-20T16:59:31.463859", "status": "completed", "mesForwardedCount": 88, "completionRate": 88.3}], "currentWorkOrder": 2, "currentWorkOrderProgress": 66, "displayWorkOrder": {"workOrderNumber": "WO864944", "targetQuantity": 216, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-20T16:59:31.463859", "status": "completed", "mesForwardedCount": 88, "completionRate": 88.3}, "mesErrors": [{"timestamp": "2025-06-13T15:59:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050058"}}, {"timestamp": "2025-06-13T03:36:31.463859", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050058"}}, {"timestamp": "2025-06-13T15:31:31.463859", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050058"}}], "recentLogs": [{"message": "測試條碼-d591c71f", "timestamp": "2025-06-13T16:22:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-144a1189", "timestamp": "2025-06-13T16:28:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-ad3a80c9", "timestamp": "2025-06-13T16:27:31.463859", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-4fd710e4", "timestamp": "2025-06-13T16:02:31.463859", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-805d551e", "timestamp": "2025-06-13T16:13:31.463859", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-767bd9fa", "timestamp": "2025-06-13T16:51:31.473380", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-30b2e2f6", "timestamp": "2025-06-13T16:43:31.473380", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-fe631dca", "timestamp": "2025-06-13T16:07:31.473380", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-fa3aa532", "timestamp": "2025-06-13T16:58:31.473380", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-e2334ac2", "timestamp": "2025-06-13T16:10:31.473380", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-7000c03a", "timestamp": "2025-06-13T16:57:31.473380", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-c63a7c09", "timestamp": "2025-06-13T16:47:31.473380", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ee15124e", "timestamp": "2025-06-13T16:04:31.473380", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-bce6b6d2", "timestamp": "2025-06-13T16:49:31.473380", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-9e172fb9", "timestamp": "2025-06-13T16:09:31.473380", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-1ef91e45", "timestamp": "2025-06-13T16:16:31.473380", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-9846ac0f", "timestamp": "2025-06-13T16:00:31.473380", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-c0f07832", "timestamp": "2025-06-13T16:13:31.473380", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}]}, "S720050059": {"deviceId": "S720050059", "lineName": "B1-02", "sectionName": "ASSEMBLY", "groupName": "TEST", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 131, "forwardedCount": 36, "unforwardedCount": 95, "lastLog": "測試產品條碼-ff833384", "lastUpdateTime": "2025-06-13T16:59:31.473380", "createdAt": "2024-08-19T16:59:31.473380", "workOrders": [{"workOrderNumber": "WO127444", "targetQuantity": 496, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-13T16:59:31.473380", "status": "pending", "mesForwardedCount": 74, "completionRate": 28.5}, {"workOrderNumber": "WO812259", "targetQuantity": 254, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-19T16:59:31.473380", "status": "pending", "mesForwardedCount": 87, "completionRate": 18.8}, {"workOrderNumber": "WO391863", "targetQuantity": 283, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-16T16:59:31.473380", "status": "completed", "mesForwardedCount": 23, "completionRate": 31.5}], "currentWorkOrder": 0, "currentWorkOrderProgress": 14, "displayWorkOrder": {"workOrderNumber": "WO127444", "targetQuantity": 496, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-13T16:59:31.473380", "status": "pending", "mesForwardedCount": 74, "completionRate": 28.5}, "mesErrors": [{"timestamp": "2025-06-13T02:47:31.473380", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050059"}}, {"timestamp": "2025-06-12T20:58:31.473380", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050059"}}, {"timestamp": "2025-06-13T06:49:31.473380", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050059"}}, {"timestamp": "2025-06-13T02:09:31.473380", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050059"}}], "recentLogs": [{"message": "測試條碼-4ca56ce2", "timestamp": "2025-06-13T16:15:31.473380", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-d4295605", "timestamp": "2025-06-13T16:57:31.473380", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-a0e9657e", "timestamp": "2025-06-13T16:25:31.473380", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-2155d1ba", "timestamp": "2025-06-13T16:50:31.473380", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-f3776234", "timestamp": "2025-06-13T16:58:31.473380", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-ca87298c", "timestamp": "2025-06-13T16:19:31.473380", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-29692c87", "timestamp": "2025-06-13T16:41:31.473380", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-2b165b38", "timestamp": "2025-06-13T16:03:31.473380", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}]}, "S720050060": {"deviceId": "S720050060", "lineName": "A2-01", "sectionName": "PACKAGING", "groupName": "TEST", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 820, "forwardedCount": 560, "unforwardedCount": 260, "lastLog": "測試產品條碼-235e323c", "lastUpdateTime": "2025-06-13T16:59:31.473380", "createdAt": "2024-10-13T16:59:31.473380", "workOrders": [{"workOrderNumber": "WO903002", "targetQuantity": 79, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-13T16:59:31.473380", "status": "completed", "mesForwardedCount": 28, "completionRate": 85.5}, {"workOrderNumber": "WO154921", "targetQuantity": 285, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-11T16:59:31.473380", "status": "completed", "mesForwardedCount": 39, "completionRate": 72.3}, {"workOrderNumber": "WO668585", "targetQuantity": 435, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-29T16:59:31.473380", "status": "pending", "mesForwardedCount": 35, "completionRate": 21.2}], "currentWorkOrder": 1, "currentWorkOrderProgress": 7, "displayWorkOrder": {"workOrderNumber": "WO154921", "targetQuantity": 285, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-11T16:59:31.473380", "status": "completed", "mesForwardedCount": 39, "completionRate": 72.3}, "mesErrors": [{"timestamp": "2025-06-13T14:14:31.473380", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050060"}}], "recentLogs": [{"message": "測試條碼-ce1ddf34", "timestamp": "2025-06-13T16:52:31.473380", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-ca263a7f", "timestamp": "2025-06-13T16:37:31.473380", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-ab752ebe", "timestamp": "2025-06-13T16:15:31.473380", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-9034a52b", "timestamp": "2025-06-13T16:28:31.473380", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-c9a393f4", "timestamp": "2025-06-13T16:13:31.473380", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-94da51a4", "timestamp": "2025-06-13T16:19:31.473380", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-88a707a7", "timestamp": "2025-06-13T16:48:31.473380", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-1b5157d5", "timestamp": "2025-06-13T16:46:31.473380", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-1e693607", "timestamp": "2025-06-13T16:10:31.473380", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}]}}