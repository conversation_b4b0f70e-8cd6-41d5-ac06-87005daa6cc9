#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RabbitMQ監控服務
從RabbitMQ服務器獲取UnitsDeparted生產數據
"""

import pika
import json
import threading
import time
from datetime import datetime
import logging
from rabbitmq_config_manager import RabbitMQConfigManager

class RabbitMQMonitor:
    def __init__(self, device_manager, mes_api):
        self.device_manager = device_manager
        self.mes_api = mes_api
        self.config_manager = RabbitMQConfigManager()
        
        # 連接相關
        self.connection = None
        self.channel = None
        self.queue_name = None
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 設備相關
        self.device_routing_keys = {}
        
        # 日誌設置
        self.logger = logging.getLogger(__name__)
        
    def update_device_routing_keys(self):
        """更新設備routing_key映射"""
        try:
            devices = self.device_manager.get_all_devices()
            device_ids = list(devices.keys())
            
            # 生成routing_key列表
            config = self.config_manager.get_config()
            pattern = config.get('routing_key_pattern', 'edadata.cfx.#.{device_id}.CFX.Production.UnitsDeparted')
            
            self.device_routing_keys = {}
            for device_id in device_ids:
                routing_key = pattern.format(device_id=device_id)
                self.device_routing_keys[device_id] = routing_key
                
            print(f"📝 更新設備routing_key映射: {len(self.device_routing_keys)}個設備")
            for device_id, routing_key in self.device_routing_keys.items():
                print(f"   {device_id} -> {routing_key}")
                
        except Exception as e:
            print(f"❌ 更新設備routing_key映射失敗: {e}")

    def connect(self):
        """建立RabbitMQ連接"""
        try:
            config = self.config_manager.get_config()
            
            # 設置連接參數
            credentials = pika.PlainCredentials(config['username'], config['password'])
            parameters = pika.ConnectionParameters(
                host=config['host'],
                port=config['port'],
                credentials=credentials,
                heartbeat=config.get('heartbeat', 600),
                blocked_connection_timeout=config.get('blocked_connection_timeout', 300)
            )

            # 建立連接
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()

            # 聲明Exchange
            self.channel.exchange_declare(
                exchange=config['exchange_name'],
                exchange_type=config.get('exchange_type', 'topic'),
                durable=True
            )

            # 創建臨時隊列
            result = self.channel.queue_declare(queue='', exclusive=True)
            self.queue_name = result.method.queue

            # 綁定所有設備的routing_key到隊列
            for device_id, routing_key in self.device_routing_keys.items():
                self.channel.queue_bind(
                    exchange=config['exchange_name'],
                    queue=self.queue_name,
                    routing_key=routing_key
                )
                print(f"📡 綁定設備 {device_id}: {routing_key}")

            print(f"✅ 成功連接到RabbitMQ服務器 {config['host']}:{config['port']}")
            print(f"📡 監聽Exchange: {config['exchange_name']}")
            print(f"📋 隊列名稱: {self.queue_name}")
            print(f"🔗 綁定了 {len(self.device_routing_keys)} 個設備的routing_key")

            return True

        except Exception as e:
            print(f"❌ 連接RabbitMQ失敗: {e}")
            return False

    def message_callback(self, channel, method, properties, body):
        """處理接收到的消息"""
        try:
            # 解析消息內容
            message = body.decode('utf-8')
            routing_key = method.routing_key
            
            # 從routing_key中提取設備ID
            device_id = self.extract_device_id_from_routing_key(routing_key)
            
            if not device_id:
                print(f"⚠️ 無法從routing_key提取設備ID: {routing_key}")
                channel.basic_ack(delivery_tag=method.delivery_tag)
                return

            # 嘗試解析JSON格式
            try:
                data = json.loads(message)
                self.process_production_data(device_id, data, routing_key)
            except json.JSONDecodeError:
                print(f"⚠️ 消息不是有效的JSON格式: {message[:100]}...")

            # 確認消息處理完成
            channel.basic_ack(delivery_tag=method.delivery_tag)

        except Exception as e:
            print(f"❌ 處理消息時發生錯誤: {e}")
            # 拒絕消息但不重新排隊
            channel.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

    def extract_device_id_from_routing_key(self, routing_key):
        """從routing_key中提取設備ID"""
        try:
            # 根據routing_key模式提取設備ID
            # 例如: edadata.cfx.xxx.S720050039.CFX.Production.UnitsDeparted
            parts = routing_key.split('.')
            
            # 查找匹配的設備ID
            for device_id in self.device_routing_keys.keys():
                if device_id in parts:
                    return device_id
                    
            return None
            
        except Exception as e:
            print(f"❌ 提取設備ID失敗: {e}")
            return None

    def process_production_data(self, device_id, data, routing_key):
        """處理生產數據，支持UnitsDeparted和WorkCompleted兩種消息類型"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 檢查消息類型
            message_name = data.get('MessageName', '')
            print(f"📦 收到設備 {device_id} 的生產數據")
            print(f"🕒 時間: {timestamp}")
            print(f"🔑 Routing Key: {routing_key}")
            print(f"📋 消息類型: {message_name}")

            # 根據消息類型提取產品數據
            if 'UnitsDeparted' in message_name:
                self.process_units_departed_message(device_id, data, routing_key)
            elif 'WorkCompleted' in message_name:
                self.process_work_completed_message(device_id, data, routing_key)
            else:
                print(f"⚠️ 不支持的消息類型: {message_name}")

        except Exception as e:
            print(f"❌ 處理生產數據失敗: {e}")

    def process_units_departed_message(self, device_id, data, routing_key):
        """處理UnitsDeparted消息"""
        try:
            # 提取關鍵信息
            barcode = self.extract_barcode_from_data(data)

            if barcode:
                print(f"📋 產品條碼: {barcode}")
                self.handle_product_data(device_id, barcode, data, 1)
            else:
                print(f"⚠️ 無法從UnitsDeparted數據中提取產品條碼")

        except Exception as e:
            print(f"❌ 處理UnitsDeparted消息失敗: {e}")

    def process_work_completed_message(self, device_id, data, routing_key):
        """處理WorkCompleted消息"""
        try:
            message_body = data.get('MessageBody', {})
            units = message_body.get('Units', [])
            unit_count = message_body.get('UnitCount', len(units))
            result = message_body.get('Result', 'Unknown')

            print(f"📋 WorkCompleted - UnitCount: {unit_count}, Result: {result}")

            if units:
                # 處理每個產品
                for i, unit in enumerate(units):
                    unit_identifier = unit.get('UnitIdentifier', '')
                    status = unit.get('Status', 'Unknown')

                    if unit_identifier:
                        print(f"📋 產品 {i+1}: 條碼={unit_identifier}, 狀態={status}")
                        self.handle_product_data(device_id, unit_identifier, data, 1)
                    else:
                        print(f"⚠️ 產品 {i+1} 缺少UnitIdentifier")
            else:
                print(f"⚠️ WorkCompleted消息中沒有Units數據")

        except Exception as e:
            print(f"❌ 處理WorkCompleted消息失敗: {e}")

    def handle_product_data(self, device_id, barcode, original_data, quantity):
        """統一處理產品數據"""
        try:
            # 如果設備啟用了MES轉發，則轉發數據
            device = self.device_manager.get_device(device_id)
            if device and device.get('isMonitoring', False):
                self.forward_to_mes(device_id, barcode, original_data)
            else:
                print(f"⏸️ 設備 {device_id} 未啟用MES轉發")
                # 記錄為未轉發的數據
                self.device_manager.update_device_production(device_id, quantity, barcode, False)
        except Exception as e:
            print(f"❌ 處理產品數據失敗: {e}")

    def extract_barcode_from_data(self, data):
        """從RabbitMQ數據中提取產品條碼"""
        try:
            # 根據日誌樣本，嘗試多種可能的字段
            if isinstance(data, dict):
                # 嘗試常見的條碼字段
                barcode_fields = [
                    'PrimaryIdentifier', 'HermesIdentifier', 'UnitIdentifier',
                    'SerialNumber', 'Barcode', 'ProductId', 'UnitId'
                ]
                
                # 首先檢查MessageBody
                message_body = data.get('MessageBody', {})
                if isinstance(message_body, dict):
                    for field in barcode_fields:
                        if field in message_body and message_body[field]:
                            return str(message_body[field])
                    
                    # 檢查Units數組
                    units = message_body.get('Units', [])
                    if units and isinstance(units, list) and len(units) > 0:
                        unit = units[0]
                        for field in barcode_fields:
                            if field in unit and unit[field]:
                                return str(unit[field])
                
                # 檢查根級別
                for field in barcode_fields:
                    if field in data and data[field]:
                        return str(data[field])
                        
            return None
            
        except Exception as e:
            print(f"❌ 提取條碼失敗: {e}")
            return None

    def forward_to_mes(self, device_id, barcode, original_data):
        """轉發數據到MES系統"""
        try:
            # 使用MES API轉發數據
            success, response = self.mes_api.send_production_data(device_id, barcode)
            
            if success:
                print(f"✅ MES轉發成功: 設備={device_id}, 條碼={barcode}")
                # 更新轉發成功的數據
                self.device_manager.update_device_production(device_id, 1, barcode, True)
            else:
                print(f"❌ MES轉發失敗: 設備={device_id}, 條碼={barcode}")
                # 更新轉發失敗的數據（累計產量+1，未轉發+1）
                self.device_manager.update_device_production(device_id, 1, barcode, False)
                # 記錄錯誤
                self.device_manager.add_mes_error(device_id, f"❌ 失敗: {response.get('description', 'Unknown error')}", response, original_data)
                
        except Exception as e:
            print(f"❌ MES轉發異常: {e}")
            # 異常情況下也要更新統計數據（累計產量+1，未轉發+1）
            self.device_manager.update_device_production(device_id, 1, barcode, False)
            # 記錄異常錯誤
            self.device_manager.add_mes_error(device_id, f"❌ 異常: {str(e)}", {'error': str(e)}, original_data)

    def start_monitoring(self):
        """開始監控"""
        if self.is_monitoring:
            return True, "RabbitMQ監控已在運行"
            
        try:
            # 更新設備routing_key映射
            self.update_device_routing_keys()
            
            if not self.device_routing_keys:
                return False, "沒有可監控的設備"
            
            # 啟動監控線程
            self.monitor_thread = threading.Thread(target=self._monitor_worker, daemon=True)
            self.monitor_thread.start()
            
            return True, f"RabbitMQ監控已啟動，監控 {len(self.device_routing_keys)} 個設備"
            
        except Exception as e:
            return False, f"啟動RabbitMQ監控失敗: {e}"

    def _monitor_worker(self):
        """監控工作線程"""
        try:
            self.is_monitoring = True
            
            if not self.connect():
                self.is_monitoring = False
                return
            
            # 設置消息處理回調
            self.channel.basic_consume(
                queue=self.queue_name,
                on_message_callback=self.message_callback
            )

            print("🚀 RabbitMQ監控已啟動，開始監聽消息...")
            self.channel.start_consuming()
            
        except Exception as e:
            print(f"❌ RabbitMQ監控異常: {e}")
        finally:
            self.is_monitoring = False
            self.disconnect()

    def stop_monitoring(self):
        """停止監控"""
        if not self.is_monitoring:
            return True, "RabbitMQ監控未運行"
            
        try:
            self.is_monitoring = False
            
            if self.channel:
                self.channel.stop_consuming()
                
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5)
                
            self.disconnect()
            
            return True, "RabbitMQ監控已停止"
            
        except Exception as e:
            return False, f"停止RabbitMQ監控失敗: {e}"

    def disconnect(self):
        """斷開連接"""
        try:
            if self.connection and not self.connection.is_closed:
                self.connection.close()
            print("🔌 RabbitMQ連接已關閉")
        except Exception as e:
            print(f"❌ 關閉RabbitMQ連接失敗: {e}")

    def get_status(self):
        """獲取監控狀態"""
        return {
            'is_monitoring': self.is_monitoring,
            'device_count': len(self.device_routing_keys),
            'config': self.config_manager.get_connection_info()
        }
