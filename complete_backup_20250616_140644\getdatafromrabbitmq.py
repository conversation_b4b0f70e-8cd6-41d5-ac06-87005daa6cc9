#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RabbitMQ數據獲取程式
從RabbitMQ服務器持續獲取UnitsDeparted生產數據
"""

import pika
import json
import sys
import time
from datetime import datetime
import logging

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('rabbitmq_consumer.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class RabbitMQConsumer:
    def __init__(self):
        # RabbitMQ連接配置
        self.host = '************'
        self.port = 30025
        self.username = 'guest'
        self.password = 'guest'
        self.exchange_name = 'message-bus'
        self.exchange_type = 'topic'
        self.device_id = 'S720050039'  # 設備ID
        self.routing_key = f'edadata.cfx.#.{self.device_id}.CFX.Production.UnitsDeparted'

        self.connection = None
        self.channel = None
        self.queue_name = None

    def connect(self):
        """建立RabbitMQ連接"""
        try:
            # 設置連接參數
            credentials = pika.PlainCredentials(self.username, self.password)
            parameters = pika.ConnectionParameters(
                host=self.host,
                port=self.port,
                credentials=credentials,
                heartbeat=600,
                blocked_connection_timeout=300
            )

            # 建立連接
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()

            # 聲明Exchange
            self.channel.exchange_declare(
                exchange=self.exchange_name,
                exchange_type=self.exchange_type,
                durable=True
            )

            # 創建臨時隊列
            result = self.channel.queue_declare(queue='', exclusive=True)
            self.queue_name = result.method.queue

            # 綁定隊列到Exchange
            self.channel.queue_bind(
                exchange=self.exchange_name,
                queue=self.queue_name,
                routing_key=self.routing_key
            )

            logger.info(f"成功連接到RabbitMQ服務器 {self.host}:{self.port}")
            logger.info(f"監聽Exchange: {self.exchange_name}")
            logger.info(f"Routing Key: {self.routing_key}")
            logger.info(f"隊列名稱: {self.queue_name}")

            return True

        except Exception as e:
            logger.error(f"連接RabbitMQ失敗: {e}")
            return False

    def message_callback(self, channel, method, properties, body):
        """處理接收到的消息"""
        try:
            # 解析消息內容
            message = body.decode('utf-8')
            logger.info(f"收到消息: {message}")

            # 嘗試解析JSON格式
            try:
                data = json.loads(message)
                self.process_units_departed_data(data, method.routing_key)
            except json.JSONDecodeError:
                logger.warning(f"消息不是有效的JSON格式: {message}")
                self.process_raw_message(message, method.routing_key)

            # 確認消息處理完成
            channel.basic_ack(delivery_tag=method.delivery_tag)

        except Exception as e:
            logger.error(f"處理消息時發生錯誤: {e}")
            # 拒絕消息並重新排隊
            channel.basic_nack(delivery_tag=method.delivery_tag, requeue=True)

    def process_units_departed_data(self, data, routing_key):
        """處理UnitsDeparted生產數據"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        logger.info("=" * 60)
        logger.info(f"時間: {timestamp}")
        logger.info(f"Routing Key: {routing_key}")
        logger.info(f"數據類型: UnitsDeparted")

        # 提取關鍵信息
        if isinstance(data, dict):
            # 常見的生產數據字段
            unit_id = data.get('UnitId', data.get('unitId', data.get('SerialNumber', 'Unknown')))
            station = data.get('Station', data.get('station', 'Unknown'))
            timestamp_msg = data.get('Timestamp', data.get('timestamp', timestamp))

            logger.info(f"產品序號: {unit_id}")
            logger.info(f"工站: {station}")
            logger.info(f"消息時間: {timestamp_msg}")

            # 如果有更多詳細信息
            if 'Activities' in data:
                logger.info(f"活動數量: {len(data['Activities'])}")
            if 'TransactionId' in data:
                logger.info(f"事務ID: {data['TransactionId']}")

        logger.info(f"完整數據: {json.dumps(data, indent=2, ensure_ascii=False)}")
        logger.info("=" * 60)

    def process_raw_message(self, message, routing_key):
        """處理原始消息"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        logger.info("=" * 60)
        logger.info(f"時間: {timestamp}")
        logger.info(f"Routing Key: {routing_key}")
        logger.info(f"原始消息: {message}")
        logger.info("=" * 60)

    def start_consuming(self):
        """開始消費消息"""
        if not self.connect():
            return False

        try:
            # 設置消息處理回調
            self.channel.basic_consume(
                queue=self.queue_name,
                on_message_callback=self.message_callback
            )

            logger.info("開始監聽消息... 按 Ctrl+C 停止")
            self.channel.start_consuming()

        except KeyboardInterrupt:
            logger.info("收到停止信號，正在關閉...")
            self.stop_consuming()
        except Exception as e:
            logger.error(f"消費消息時發生錯誤: {e}")
            return False

        return True

    def stop_consuming(self):
        """停止消費消息"""
        if self.channel:
            self.channel.stop_consuming()
        if self.connection and not self.connection.is_closed:
            self.connection.close()
        logger.info("RabbitMQ連接已關閉")

def main():
    """主函數"""
    logger.info("啟動RabbitMQ消費者程式")

    consumer = RabbitMQConsumer()

    # 支持重連機制
    max_retries = 5
    retry_count = 0

    while retry_count < max_retries:
        try:
            if consumer.start_consuming():
                break
        except Exception as e:
            retry_count += 1
            logger.error(f"連接失敗 (嘗試 {retry_count}/{max_retries}): {e}")
            if retry_count < max_retries:
                logger.info(f"等待5秒後重試...")
                time.sleep(5)
            else:
                logger.error("達到最大重試次數，程式退出")
                sys.exit(1)

if __name__ == "__main__":
    main()