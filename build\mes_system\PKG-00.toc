('E:\\vscode\\tomesmo\\build\\mes_system\\MES_Upload_Manager.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True},
 [('PYZ-00.pyz', 'E:\\vscode\\tomesmo\\build\\mes_system\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'E:\\vscode\\tomesmo\\build\\mes_system\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\vscode\\tomesmo\\build\\mes_system\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\vscode\\tomesmo\\build\\mes_system\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\vscode\\tomesmo\\build\\mes_system\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\vscode\\tomesmo\\build\\mes_system\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('app', 'E:\\vscode\\tomesmo\\app.py', 'PYSOURCE'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'E:\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('python37.dll', 'E:\\python\\python37.dll', 'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\WINDOWS\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('select.pyd', 'E:\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('pyexpat.pyd', 'E:\\python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'E:\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'E:\\python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'E:\\python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'E:\\python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'E:\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'E:\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_socket.pyd', 'E:\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'E:\\python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'E:\\python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'E:\\python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp37-win32.pyd',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp37-win32.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp37-win32.pyd',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\charset_normalizer\\md.cp37-win32.pyd',
   'EXTENSION'),
  ('_overlapped.pyd', 'E:\\python\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'E:\\python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp37-win32.pyd',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\markupsafe\\_speedups.cp37-win32.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1.dll', 'E:\\python\\DLLs\\libcrypto-1_1.dll', 'BINARY'),
  ('libssl-1_1.dll', 'E:\\python\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('config.py', 'E:\\vscode\\tomesmo\\config.py', 'DATA'),
  ('devices.json', 'E:\\vscode\\tomesmo\\devices.json', 'DATA'),
  ('kafka_config.json', 'E:\\vscode\\tomesmo\\kafka_config.json', 'DATA'),
  ('rabbitmq_config.json', 'E:\\vscode\\tomesmo\\rabbitmq_config.json', 'DATA'),
  ('static\\logo.png', 'E:\\vscode\\tomesmo\\static\\logo.png', 'DATA'),
  ('static\\script.js', 'E:\\vscode\\tomesmo\\static\\script.js', 'DATA'),
  ('static\\style.css', 'E:\\vscode\\tomesmo\\static\\style.css', 'DATA'),
  ('templates\\index.html',
   'E:\\vscode\\tomesmo\\templates\\index.html',
   'DATA'),
  ('base_library.zip',
   'E:\\vscode\\tomesmo\\build\\mes_system\\base_library.zip',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\WHEEL',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\METADATA',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\LICENSE',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\RECORD',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\RECORD',
   'DATA')],
 False,
 False,
 False,
 [],
 None,
 None,
 None)
