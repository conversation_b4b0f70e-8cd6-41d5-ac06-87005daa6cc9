#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
import zipfile
from datetime import datetime

def backup_current_system():
    """備份當前系統到backup目錄"""
    
    # 創建備份目錄
    backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    # 需要備份的文件和目錄
    items_to_backup = [
        # 主要應用文件
        'app.py',
        'start.py',
        'open_browser.py',

        # 核心模組
        'device_manager.py',
        'kafka_monitor.py',
        'kafka_config_manager.py',
        'rabbitmq_monitor.py',
        'rabbitmq_config_manager.py',
        'mes_api.py',
        'mes_forwarder.py',
        'config.py',

        # 數據獲取模組
        'getdata.py',
        'getdatafromrabbitmq.py',
        'MESnewtdc.py',

        # 測試文件
        'test_data_simulator.py',
        'test_forwarding_stats.py',
        'test_mes_forwarding_states.py',
        'test_multi_products.py',
        'rengong.py',

        # 遷移腳本
        'migrate_devices.py',
        'migrate_forwarding_stats.py',
        'migrate_period_logs.py',

        # 打包腳本
        'build_exe.py',
        'build_english_exe.py',
        'create_zip.py',
        'create_english_zip.py',
        'create_docker_package.py',

        # 前端資源
        'static/',
        'templates/',

        # 配置文件
        'devices.json',
        'kafka_config.json',
        'rabbitmq_config.json',
        'requirements.txt',

        # 規格文件
        'mes_system.spec',
        'mes_system_english.spec',
        'mes_upload_manager.spec',
        'open_browser_english.spec',
        'Open_Browser.spec',

        # 資源文件
        'logo.png',

        # 文檔文件
        'README.md',
        'README_RabbitMQ.md',
        'COMPLETE_FEATURE_SUMMARY.md',
        'FEATURE_UPDATE.md',
        'FINAL_FEATURE_SUMMARY.md',
        'UPDATE_SUMMARY.md'
    ]
    
    print(f"🔄 開始備份當前系統到 {backup_dir}/")
    
    for item in items_to_backup:
        if os.path.exists(item):
            if os.path.isfile(item):
                shutil.copy2(item, backup_dir)
                print(f"✅ 已備份文件: {item}")
            elif os.path.isdir(item):
                shutil.copytree(item, os.path.join(backup_dir, item))
                print(f"✅ 已備份目錄: {item}")
        else:
            print(f"⚠️  文件不存在: {item}")
    
    # 創建ZIP壓縮包
    zip_filename = f"{backup_dir}.zip"
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, _, files in os.walk(backup_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, backup_dir)
                zipf.write(file_path, arcname)
    
    print(f"📦 已創建壓縮包: {zip_filename}")
    
    # 創建恢復腳本
    restore_script = f"""#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
import zipfile

def restore_system():
    \"\"\"恢復系統到備份狀態\"\"\"
    
    backup_zip = "{zip_filename}"
    
    if not os.path.exists(backup_zip):
        print(f"❌ 備份文件不存在: {{backup_zip}}")
        return False
    
    print("🔄 開始恢復系統...")
    
    # 解壓備份文件
    with zipfile.ZipFile(backup_zip, 'r') as zipf:
        zipf.extractall("temp_restore")
    
    # 恢復文件
    restore_items = [
        # 主要應用文件
        'app.py',
        'start.py',
        'open_browser.py',

        # 核心模組
        'device_manager.py',
        'kafka_monitor.py',
        'kafka_config_manager.py',
        'rabbitmq_monitor.py',
        'rabbitmq_config_manager.py',
        'mes_api.py',
        'mes_forwarder.py',
        'config.py',

        # 數據獲取模組
        'getdata.py',
        'getdatafromrabbitmq.py',
        'MESnewtdc.py',

        # 測試文件
        'test_data_simulator.py',
        'test_forwarding_stats.py',
        'test_mes_forwarding_states.py',
        'test_multi_products.py',
        'rengong.py',

        # 遷移腳本
        'migrate_devices.py',
        'migrate_forwarding_stats.py',
        'migrate_period_logs.py',

        # 打包腳本
        'build_exe.py',
        'build_english_exe.py',
        'create_zip.py',
        'create_english_zip.py',
        'create_docker_package.py',

        # 前端資源
        'static/',
        'templates/',

        # 配置文件
        'devices.json',
        'kafka_config.json',
        'rabbitmq_config.json',
        'requirements.txt',

        # 規格文件
        'mes_system.spec',
        'mes_system_english.spec',
        'mes_upload_manager.spec',
        'open_browser_english.spec',
        'Open_Browser.spec',

        # 資源文件
        'logo.png',

        # 文檔文件
        'README.md',
        'README_RabbitMQ.md',
        'COMPLETE_FEATURE_SUMMARY.md',
        'FEATURE_UPDATE.md',
        'FINAL_FEATURE_SUMMARY.md',
        'UPDATE_SUMMARY.md'
    ]
    
    for item in restore_items:
        src = os.path.join("temp_restore", item)
        if os.path.exists(src):
            if os.path.isfile(src):
                shutil.copy2(src, item)
                print(f"✅ 已恢復文件: {{item}}")
            elif os.path.isdir(src):
                if os.path.exists(item):
                    shutil.rmtree(item)
                shutil.copytree(src, item)
                print(f"✅ 已恢復目錄: {{item}}")
    
    # 清理臨時文件
    shutil.rmtree("temp_restore")
    
    print("🎉 系統恢復完成！")
    print("請重新啟動應用: python app.py")
    
    return True

if __name__ == "__main__":
    restore_system()
"""
    
    with open(f"restore_from_{backup_dir}.py", 'w', encoding='utf-8') as f:
        f.write(restore_script)
    
    print(f"📝 已創建恢復腳本: restore_from_{backup_dir}.py")
    
    # 清理臨時目錄
    shutil.rmtree(backup_dir)
    
    print(f"""
🎉 備份完成！

📦 備份文件: {zip_filename}
📝 恢復腳本: restore_from_{backup_dir}.py

如需恢復系統，請運行:
python restore_from_{backup_dir}.py
""")
    
    return zip_filename

if __name__ == "__main__":
    backup_current_system()
