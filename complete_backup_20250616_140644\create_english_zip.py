#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
創建英文版部署包壓縮文件
"""

import zipfile
import os
from pathlib import Path

def create_english_deployment_zip():
    """創建英文版部署包壓縮文件"""
    deploy_dir = Path('MES_System_English')
    zip_name = 'MES_Data_Forwarding_System_v1.0.zip'
    
    if not deploy_dir.exists():
        print("❌ English deployment directory not found")
        return False
    
    print("📦 Creating English deployment package...")
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in deploy_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(deploy_dir.parent)
                zipf.write(file_path, arcname)
                print(f"  ✅ Added: {arcname}")
    
    # 檢查壓縮包大小
    zip_size = os.path.getsize(zip_name) / (1024 * 1024)  # MB
    print(f"\n🎉 English deployment package created!")
    print(f"📁 Filename: {zip_name}")
    print(f"📏 Size: {zip_size:.1f} MB")
    print(f"🌍 Language: English (compatible with all Windows systems)")
    
    return True

if __name__ == '__main__':
    create_english_deployment_zip()
