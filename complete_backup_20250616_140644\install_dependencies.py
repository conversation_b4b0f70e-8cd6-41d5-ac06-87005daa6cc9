#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
項目安裝腳本
自動安裝項目依賴
"""

import subprocess
import sys
import os

def install_dependencies():
    """安裝項目依賴"""
    print("📦 開始安裝項目依賴...")

    # 檢查requirements.txt
    if os.path.exists('requirements.txt'):
        print("📋 從 requirements.txt 安裝依賴...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
            print("✅ 依賴安裝完成！")
        except subprocess.CalledProcessError as e:
            print(f"❌ 安裝失敗: {e}")
            return False
    else:
        print("⚠️ 未找到 requirements.txt 文件")

        # 嘗試從pip_freeze.txt安裝
        if os.path.exists('pip_freeze.txt'):
            print("📋 從 pip_freeze.txt 安裝依賴...")
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'pip_freeze.txt'])
                print("✅ 依賴安裝完成！")
            except subprocess.CalledProcessError as e:
                print(f"❌ 安裝失敗: {e}")
                return False
        else:
            print("❌ 未找到依賴文件")
            return False

    return True

if __name__ == "__main__":
    if install_dependencies():
        print("🎉 項目安裝完成！")
        print("💡 現在可以運行: python app.py")
    else:
        print("❌ 項目安裝失敗")
