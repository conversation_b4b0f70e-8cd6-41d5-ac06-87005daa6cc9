#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整項目備份工具 - 增強版
包括源代碼、配置文件、依賴庫、環境信息等所有相關文件
"""

import os
import shutil
import zipfile
import json
from datetime import datetime
import subprocess
import sys

def backup_complete_project():
    """創建完整的項目備份，包含所有必要文件和環境信息"""

    # 創建備份目錄名稱（包含時間戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"complete_backup_{timestamp}"

    print(f"🚀 開始創建完整項目備份到: {backup_dir}")

    try:
        # 創建備份目錄
        os.makedirs(backup_dir, exist_ok=True)

        # 1. 複製所有源代碼文件
        print("📁 複製源代碼文件...")
        source_files = [
            'app.py', 'config.py', 'device_manager.py', 'kafka_monitor.py',
            'kafka_config_manager.py', 'rabbitmq_monitor.py', 'rabbitmq_config_manager.py',
            'mes_api.py', 'mes_forwarder.py', 'fake_data_generator.py',
            'test_config.py', 'test_data_generator.py', 'getdata.py',
            'getdatafromrabbitmq.py', 'open_browser.py', 'start.py',
            'MESnewtdc.py', 'rengong.py', 'test_data_simulator.py',
            'migrate_devices.py', 'migrate_forwarding_stats.py', 'migrate_period_logs.py'
        ]

        for file in source_files:
            if os.path.exists(file):
                shutil.copy2(file, backup_dir)
                print(f"  ✅ {file}")

        # 2. 複製配置文件
        print("⚙️ 複製配置文件...")
        config_files = [
            'devices.json', 'kafka_config.json', 'rabbitmq_config.json',
            'requirements.txt'
        ]

        for file in config_files:
            if os.path.exists(file):
                shutil.copy2(file, backup_dir)
                print(f"  ✅ {file}")

        # 3. 複製靜態文件和模板
        print("🎨 複製靜態文件和模板...")
        if os.path.exists('static'):
            shutil.copytree('static', os.path.join(backup_dir, 'static'))
            print("  ✅ static/")

        if os.path.exists('templates'):
            shutil.copytree('templates', os.path.join(backup_dir, 'templates'))
            print("  ✅ templates/")

        # 4. 複製測試設備文件
        print("🧪 複製測試文件...")
        test_files = [
            'test_devices_40.json', 'test_devices_50.json', 'test_devices_60.json',
            'test_devices_70.json', 'test_devices_100.json'
        ]

        for file in test_files:
            if os.path.exists(file):
                shutil.copy2(file, backup_dir)
                print(f"  ✅ {file}")

        # 5. 複製構建和部署文件
        print("🔧 複製構建和部署文件...")
        build_files = [
            'build_exe.py', 'build_english_exe.py', 'create_zip.py',
            'create_english_zip.py', 'create_docker_package.py',
            'mes_system.spec', 'mes_system_english.spec', 'mes_upload_manager.spec',
            'open_browser_english.spec', 'Open_Browser.spec'
        ]

        for file in build_files:
            if os.path.exists(file):
                shutil.copy2(file, backup_dir)
                print(f"  ✅ {file}")

        # 6. 複製文檔文件
        print("📚 複製文檔文件...")
        doc_files = [
            'README.md', 'README_RabbitMQ.md', 'AI編程項目大綱範例.md',
            'BACKUP_SUMMARY.md', 'COMPLETE_FEATURE_SUMMARY.md',
            'FEATURE_UPDATE.md', 'FINAL_FEATURE_SUMMARY.md', 'UPDATE_SUMMARY.md'
        ]

        for file in doc_files:
            if os.path.exists(file):
                shutil.copy2(file, backup_dir)
                print(f"  ✅ {file}")

        # 7. 複製已編譯的可執行文件（如果存在）
        print("💾 複製可執行文件...")
        if os.path.exists('dist'):
            shutil.copytree('dist', os.path.join(backup_dir, 'dist'))
            print("  ✅ dist/")

        # 8. 複製已打包的版本（如果存在）
        print("📦 複製已打包版本...")
        if os.path.exists('MES_Upload_Manager_v0.0.1-beta-1'):
            shutil.copytree('MES_Upload_Manager_v0.0.1-beta-1',
                          os.path.join(backup_dir, 'MES_Upload_Manager_v0.0.1-beta-1'))
            print("  ✅ MES_Upload_Manager_v0.0.1-beta-1/")

        # 9. 創建依賴庫備份
        print("📚 備份Python依賴庫...")
        try:
            # 創建虛擬環境信息
            pip_freeze_output = subprocess.check_output([sys.executable, '-m', 'pip', 'freeze'],
                                                      text=True, encoding='utf-8')

            with open(os.path.join(backup_dir, 'pip_freeze.txt'), 'w', encoding='utf-8') as f:
                f.write(pip_freeze_output)
            print("  ✅ pip_freeze.txt (完整依賴列表)")

            # 獲取Python版本信息
            python_version = sys.version
            with open(os.path.join(backup_dir, 'python_version.txt'), 'w', encoding='utf-8') as f:
                f.write(f"Python版本: {python_version}\n")
                f.write(f"Python路徑: {sys.executable}\n")
                f.write(f"Python版本號: {sys.version_info}\n")
            print("  ✅ python_version.txt")

        except Exception as e:
            print(f"  ⚠️ 無法獲取依賴信息: {e}")

        # 10. 創建環境信息文件
        print("🌍 創建環境信息...")
        try:
            env_info = {
                'os_name': os.name,
                'platform': sys.platform,
                'python_version': sys.version,
                'python_executable': sys.executable,
                'working_directory': os.getcwd(),
                'environment_variables': {
                    'PATH': os.environ.get('PATH', ''),
                    'PYTHONPATH': os.environ.get('PYTHONPATH', ''),
                    'VIRTUAL_ENV': os.environ.get('VIRTUAL_ENV', '')
                }
            }

            with open(os.path.join(backup_dir, 'environment_info.json'), 'w', encoding='utf-8') as f:
                json.dump(env_info, f, ensure_ascii=False, indent=2)
            print("  ✅ environment_info.json")

        except Exception as e:
            print(f"  ⚠️ 無法獲取環境信息: {e}")

        # 11. 創建備份信息文件
        print("📋 創建備份信息...")
        backup_info = {
            'backup_time': datetime.now().isoformat(),
            'backup_type': 'complete_project',
            'python_version': sys.version,
            'working_directory': os.getcwd(),
            'included_files': [],
            'included_directories': []
        }

        # 統計備份的文件和目錄
        for root, dirs, files in os.walk(backup_dir):
            for file in files:
                rel_path = os.path.relpath(os.path.join(root, file), backup_dir)
                backup_info['included_files'].append(rel_path)
            for dir in dirs:
                rel_path = os.path.relpath(os.path.join(root, dir), backup_dir)
                backup_info['included_directories'].append(rel_path)

        with open(os.path.join(backup_dir, 'backup_info.json'), 'w', encoding='utf-8') as f:
            json.dump(backup_info, f, ensure_ascii=False, indent=2)
        print("  ✅ backup_info.json")

        # 12. 創建恢復腳本
        print("🔄 創建恢復腳本...")
        restore_script = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
項目恢復腳本
從備份 {backup_dir} 恢復項目
"""

import os
import shutil
import json
from datetime import datetime

def restore_project():
    """從備份恢復項目"""
    backup_dir = "{backup_dir}"

    print(f"🔄 開始從 {{backup_dir}} 恢復項目...")

    # 讀取備份信息
    backup_info_file = os.path.join(backup_dir, "backup_info.json")
    if os.path.exists(backup_info_file):
        with open(backup_info_file, 'r', encoding='utf-8') as f:
            backup_info = json.load(f)
        print(f"📋 備份時間: {{backup_info.get('backup_time')}}")
        print(f"🐍 Python版本: {{backup_info.get('python_version')}}")

    # 恢復文件
    for item in os.listdir(backup_dir):
        if item in ["backup_info.json", "environment_info.json", "pip_freeze.txt", "python_version.txt"]:
            continue

        src_path = os.path.join(backup_dir, item)
        dst_path = item

        if os.path.isfile(src_path):
            shutil.copy2(src_path, dst_path)
            print(f"  ✅ {{item}}")
        elif os.path.isdir(src_path):
            if os.path.exists(dst_path):
                shutil.rmtree(dst_path)
            shutil.copytree(src_path, dst_path)
            print(f"  ✅ {{item}}/")

    print("✅ 項目恢復完成！")
    print("💡 請檢查 requirements.txt 並安裝依賴: pip install -r requirements.txt")
    print("💡 檢查 pip_freeze.txt 查看完整的依賴列表")
    print("💡 檢查 environment_info.json 查看原始環境信息")

if __name__ == "__main__":
    restore_project()
'''

        with open(os.path.join(backup_dir, f'restore_from_backup_{timestamp}.py'), 'w', encoding='utf-8') as f:
            f.write(restore_script)
        print(f"  ✅ restore_from_backup_{timestamp}.py")

        # 13. 創建安裝腳本
        print("📦 創建安裝腳本...")
        install_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
項目安裝腳本
自動安裝項目依賴
"""

import subprocess
import sys
import os

def install_dependencies():
    """安裝項目依賴"""
    print("📦 開始安裝項目依賴...")

    # 檢查requirements.txt
    if os.path.exists('requirements.txt'):
        print("📋 從 requirements.txt 安裝依賴...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
            print("✅ 依賴安裝完成！")
        except subprocess.CalledProcessError as e:
            print(f"❌ 安裝失敗: {e}")
            return False
    else:
        print("⚠️ 未找到 requirements.txt 文件")

        # 嘗試從pip_freeze.txt安裝
        if os.path.exists('pip_freeze.txt'):
            print("📋 從 pip_freeze.txt 安裝依賴...")
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'pip_freeze.txt'])
                print("✅ 依賴安裝完成！")
            except subprocess.CalledProcessError as e:
                print(f"❌ 安裝失敗: {e}")
                return False
        else:
            print("❌ 未找到依賴文件")
            return False

    return True

if __name__ == "__main__":
    if install_dependencies():
        print("🎉 項目安裝完成！")
        print("💡 現在可以運行: python app.py")
    else:
        print("❌ 項目安裝失敗")
'''

        with open(os.path.join(backup_dir, 'install_dependencies.py'), 'w', encoding='utf-8') as f:
            f.write(install_script)
        print("  ✅ install_dependencies.py")

        # 14. 創建ZIP壓縮包
        print("🗜️ 創建ZIP壓縮包...")
        zip_filename = f"project_backup_{timestamp}.zip"

        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(backup_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, '.')
                    zipf.write(file_path, arc_name)

        print(f"  ✅ {zip_filename}")

        # 統計信息
        total_files = len(backup_info['included_files'])
        total_dirs = len(backup_info['included_directories'])
        zip_size = os.path.getsize(zip_filename) / (1024 * 1024)  # MB

        print(f"\n🎉 備份完成！")
        print(f"📁 備份目錄: {backup_dir}")
        print(f"📦 壓縮包: {zip_filename} ({zip_size:.1f} MB)")
        print(f"📊 統計: {total_files} 個文件, {total_dirs} 個目錄")
        print(f"⏰ 備份時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        print(f"\n📋 備份內容包括:")
        print(f"  • 所有源代碼文件")
        print(f"  • 配置文件和設備數據")
        print(f"  • 靜態文件和模板")
        print(f"  • 測試文件和數據")
        print(f"  • 構建和部署腳本")
        print(f"  • 文檔和說明")
        print(f"  • 已編譯的可執行文件")
        print(f"  • Python依賴信息")
        print(f"  • 環境配置信息")
        print(f"  • 恢復和安裝腳本")

        return backup_dir, zip_filename

    except Exception as e:
        print(f"❌ 備份過程中出錯: {e}")
        return None, None

if __name__ == "__main__":
    backup_complete_project()
