"""
設備數據遷移腳本
為現有設備添加新字段
"""

import json
import os
from datetime import datetime

def migrate_devices():
    """遷移設備數據，添加新字段"""
    data_file = 'devices.json'
    
    if not os.path.exists(data_file):
        print("設備數據文件不存在")
        return
    
    # 讀取現有數據
    with open(data_file, 'r', encoding='utf-8') as f:
        devices = json.load(f)

    print(f"讀取到 {len(devices)} 個設備")

    # 為每個設備添加新字段
    for device_id, device in devices.items():
        print(f"處理設備: {device_id}")
        print(f"原始字段: {list(device.keys())}")

        # 添加新字段（如果不存在）
        if 'isMonitoring' not in device:
            device['isMonitoring'] = False
            print("  添加 isMonitoring")
        if 'productCount' not in device:
            device['productCount'] = 0
            print("  添加 productCount")
        if 'lastLog' not in device:
            device['lastLog'] = '暫無數據'
            print("  添加 lastLog")
        if 'lastUpdateTime' not in device:
            device['lastUpdateTime'] = ''
            print("  添加 lastUpdateTime")

        # 更新時間戳
        device['updatedAt'] = datetime.now().isoformat()

        print(f"  更新後字段: {list(device.keys())}")
        print(f"✅ 已遷移設備: {device_id}")

    # 保存更新後的數據
    print("正在保存數據...")
    with open(data_file, 'w', encoding='utf-8') as f:
        json.dump(devices, f, ensure_ascii=False, indent=2)
    print("數據保存完成")
    
    print(f"\n🎉 設備數據遷移完成，共處理 {len(devices)} 個設備")

if __name__ == '__main__':
    migrate_devices()
