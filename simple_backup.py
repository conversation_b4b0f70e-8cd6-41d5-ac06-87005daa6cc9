#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import zipfile
from datetime import datetime

def create_simple_backup():
    """創建簡單的項目備份"""
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    zip_filename = f"project_backup_{timestamp}.zip"
    
    print(f"🔄 開始創建項目備份: {zip_filename}")
    
    # 排除的文件模式
    exclude_patterns = [
        '__pycache__',
        '.pyc',
        '.pyo',
        '.pyd',
        'backup_',
        'complete_backup_',
        'project_backup_',
        'restore_from_',
        '.git',
        '.vscode',
        '.idea',
        'node_modules',
        'venv',
        'env'
    ]
    
    def should_exclude(file_path):
        """檢查文件是否應該被排除"""
        for pattern in exclude_patterns:
            if pattern in file_path:
                return True
        return False
    
    # 創建ZIP文件
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk('.'):
            # 過濾目錄
            dirs[:] = [d for d in dirs if not should_exclude(os.path.join(root, d))]
            
            for file in files:
                file_path = os.path.join(root, file)
                
                # 跳過排除的文件
                if should_exclude(file_path):
                    continue
                
                # 計算相對路徑
                arcname = os.path.relpath(file_path, '.')
                
                try:
                    zipf.write(file_path, arcname)
                    print(f"✅ 已添加: {arcname}")
                except Exception as e:
                    print(f"⚠️  跳過文件 {arcname}: {e}")
    
    # 獲取文件大小
    file_size = os.path.getsize(zip_filename)
    size_mb = file_size / (1024 * 1024)
    
    print(f"""
🎉 項目備份完成！

📦 備份文件: {zip_filename}
📏 文件大小: {size_mb:.2f} MB

備份包含所有源碼文件，可用於：
- 項目歸檔保存
- 版本控制
- 環境遷移
- 災難恢復

恢復方法：
1. 解壓備份文件到目標目錄
2. 安裝依賴: pip install -r requirements.txt
3. 運行應用: python app.py
""")
    
    return zip_filename

if __name__ == "__main__":
    create_simple_backup()
