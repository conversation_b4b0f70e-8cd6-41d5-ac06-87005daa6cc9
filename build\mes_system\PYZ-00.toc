('E:\\vscode\\tomesmo\\build\\mes_system\\PYZ-00.pyz',
 [('__future__', 'E:\\python\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle', 'E:\\python\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'E:\\python\\lib\\_compression.py', 'PYMODULE'),
  ('_dummy_thread', 'E:\\python\\lib\\_dummy_thread.py', 'PYMODULE'),
  ('_py_abc', 'E:\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'E:\\python\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'E:\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'E:\\python\\lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'E:\\python\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'E:\\python\\lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'E:\\python\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'E:\\python\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'E:\\python\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'E:\\python\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks', 'E:\\python\\lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.constants', 'E:\\python\\lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.coroutines', 'E:\\python\\lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.events', 'E:\\python\\lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'E:\\python\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'E:\\python\\lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'E:\\python\\lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'E:\\python\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'E:\\python\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'E:\\python\\lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.queues', 'E:\\python\\lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'E:\\python\\lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'E:\\python\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'E:\\python\\lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.streams', 'E:\\python\\lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess', 'E:\\python\\lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.tasks', 'E:\\python\\lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.transports', 'E:\\python\\lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'E:\\python\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'E:\\python\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'E:\\python\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'E:\\python\\lib\\base64.py', 'PYMODULE'),
  ('bisect', 'E:\\python\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'E:\\python\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'E:\\python\\lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('code', 'E:\\python\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'E:\\python\\lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent', 'E:\\python\\lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'E:\\python\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'E:\\python\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'E:\\python\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'E:\\python\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('config', 'E:\\vscode\\tomesmo\\config.py', 'PYMODULE'),
  ('contextlib', 'E:\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'E:\\python\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'E:\\python\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'E:\\python\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'E:\\python\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'E:\\python\\lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'E:\\python\\lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'E:\\python\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'E:\\python\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'E:\\python\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'E:\\python\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'E:\\python\\lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'E:\\python\\lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('dataclasses', 'E:\\python\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'E:\\python\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'E:\\python\\lib\\decimal.py', 'PYMODULE'),
  ('device_manager', 'E:\\vscode\\tomesmo\\device_manager.py', 'PYMODULE'),
  ('difflib', 'E:\\python\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'E:\\python\\lib\\dis.py', 'PYMODULE'),
  ('dummy_threading', 'E:\\python\\lib\\dummy_threading.py', 'PYMODULE'),
  ('email', 'E:\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'E:\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'E:\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'E:\\python\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'E:\\python\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'E:\\python\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'E:\\python\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'E:\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'E:\\python\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'E:\\python\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'E:\\python\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'E:\\python\\lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'E:\\python\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'E:\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'E:\\python\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'E:\\python\\lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'E:\\python\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'E:\\python\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'E:\\python\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'E:\\python\\lib\\email\\utils.py', 'PYMODULE'),
  ('flask',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.scaffold',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.wrappers',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('ftplib', 'E:\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'E:\\python\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'E:\\python\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'E:\\python\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'E:\\python\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'E:\\python\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'E:\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'E:\\python\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'E:\\python\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'E:\\python\\lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'E:\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'E:\\python\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'E:\\python\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'E:\\python\\lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'E:\\python\\lib\\http\\server.py', 'PYMODULE'),
  ('idna',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'E:\\python\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'E:\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'E:\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'E:\\python\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'E:\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.resources',
   'E:\\python\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util', 'E:\\python\\lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib_metadata',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._py39compat',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('inspect', 'E:\\python\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'E:\\python\\lib\\ipaddress.py', 'PYMODULE'),
  ('itsdangerous',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jinja2',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'E:\\python\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'E:\\python\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'E:\\python\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'E:\\python\\lib\\json\\scanner.py', 'PYMODULE'),
  ('kafka',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\__init__.py',
   'PYMODULE'),
  ('kafka.admin',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\admin\\__init__.py',
   'PYMODULE'),
  ('kafka.admin.acl_resource',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\admin\\acl_resource.py',
   'PYMODULE'),
  ('kafka.admin.client',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\admin\\client.py',
   'PYMODULE'),
  ('kafka.admin.config_resource',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\admin\\config_resource.py',
   'PYMODULE'),
  ('kafka.admin.new_partitions',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\admin\\new_partitions.py',
   'PYMODULE'),
  ('kafka.admin.new_topic',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\admin\\new_topic.py',
   'PYMODULE'),
  ('kafka.client_async',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\client_async.py',
   'PYMODULE'),
  ('kafka.cluster',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\cluster.py',
   'PYMODULE'),
  ('kafka.codec',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\codec.py',
   'PYMODULE'),
  ('kafka.conn',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\conn.py',
   'PYMODULE'),
  ('kafka.consumer',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\consumer\\__init__.py',
   'PYMODULE'),
  ('kafka.consumer.fetcher',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\consumer\\fetcher.py',
   'PYMODULE'),
  ('kafka.consumer.group',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\consumer\\group.py',
   'PYMODULE'),
  ('kafka.consumer.subscription_state',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\consumer\\subscription_state.py',
   'PYMODULE'),
  ('kafka.coordinator',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\coordinator\\__init__.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\coordinator\\assignors\\__init__.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors.abstract',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\coordinator\\assignors\\abstract.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors.range',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\coordinator\\assignors\\range.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors.roundrobin',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\coordinator\\assignors\\roundrobin.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors.sticky',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\coordinator\\assignors\\sticky\\__init__.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors.sticky.partition_movements',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\coordinator\\assignors\\sticky\\partition_movements.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors.sticky.sorted_set',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\coordinator\\assignors\\sticky\\sorted_set.py',
   'PYMODULE'),
  ('kafka.coordinator.assignors.sticky.sticky_assignor',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\coordinator\\assignors\\sticky\\sticky_assignor.py',
   'PYMODULE'),
  ('kafka.coordinator.base',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\coordinator\\base.py',
   'PYMODULE'),
  ('kafka.coordinator.consumer',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\coordinator\\consumer.py',
   'PYMODULE'),
  ('kafka.coordinator.heartbeat',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\coordinator\\heartbeat.py',
   'PYMODULE'),
  ('kafka.coordinator.protocol',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\coordinator\\protocol.py',
   'PYMODULE'),
  ('kafka.errors',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\errors.py',
   'PYMODULE'),
  ('kafka.future',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\future.py',
   'PYMODULE'),
  ('kafka.metrics',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\__init__.py',
   'PYMODULE'),
  ('kafka.metrics.compound_stat',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\compound_stat.py',
   'PYMODULE'),
  ('kafka.metrics.dict_reporter',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\dict_reporter.py',
   'PYMODULE'),
  ('kafka.metrics.kafka_metric',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\kafka_metric.py',
   'PYMODULE'),
  ('kafka.metrics.measurable',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\measurable.py',
   'PYMODULE'),
  ('kafka.metrics.measurable_stat',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\measurable_stat.py',
   'PYMODULE'),
  ('kafka.metrics.metric_config',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\metric_config.py',
   'PYMODULE'),
  ('kafka.metrics.metric_name',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\metric_name.py',
   'PYMODULE'),
  ('kafka.metrics.metrics',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\metrics.py',
   'PYMODULE'),
  ('kafka.metrics.metrics_reporter',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\metrics_reporter.py',
   'PYMODULE'),
  ('kafka.metrics.quota',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\quota.py',
   'PYMODULE'),
  ('kafka.metrics.stat',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\stat.py',
   'PYMODULE'),
  ('kafka.metrics.stats',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\stats\\__init__.py',
   'PYMODULE'),
  ('kafka.metrics.stats.avg',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\stats\\avg.py',
   'PYMODULE'),
  ('kafka.metrics.stats.count',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\stats\\count.py',
   'PYMODULE'),
  ('kafka.metrics.stats.histogram',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\stats\\histogram.py',
   'PYMODULE'),
  ('kafka.metrics.stats.max_stat',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\stats\\max_stat.py',
   'PYMODULE'),
  ('kafka.metrics.stats.min_stat',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\stats\\min_stat.py',
   'PYMODULE'),
  ('kafka.metrics.stats.percentile',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\stats\\percentile.py',
   'PYMODULE'),
  ('kafka.metrics.stats.percentiles',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\stats\\percentiles.py',
   'PYMODULE'),
  ('kafka.metrics.stats.rate',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\stats\\rate.py',
   'PYMODULE'),
  ('kafka.metrics.stats.sampled_stat',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\stats\\sampled_stat.py',
   'PYMODULE'),
  ('kafka.metrics.stats.sensor',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\stats\\sensor.py',
   'PYMODULE'),
  ('kafka.metrics.stats.total',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\metrics\\stats\\total.py',
   'PYMODULE'),
  ('kafka.oauth',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\oauth\\__init__.py',
   'PYMODULE'),
  ('kafka.oauth.abstract',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\oauth\\abstract.py',
   'PYMODULE'),
  ('kafka.partitioner',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\partitioner\\__init__.py',
   'PYMODULE'),
  ('kafka.partitioner.default',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\partitioner\\default.py',
   'PYMODULE'),
  ('kafka.producer',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\producer\\__init__.py',
   'PYMODULE'),
  ('kafka.producer.buffer',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\producer\\buffer.py',
   'PYMODULE'),
  ('kafka.producer.future',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\producer\\future.py',
   'PYMODULE'),
  ('kafka.producer.kafka',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\producer\\kafka.py',
   'PYMODULE'),
  ('kafka.producer.record_accumulator',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\producer\\record_accumulator.py',
   'PYMODULE'),
  ('kafka.producer.sender',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\producer\\sender.py',
   'PYMODULE'),
  ('kafka.protocol',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\protocol\\__init__.py',
   'PYMODULE'),
  ('kafka.protocol.abstract',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\protocol\\abstract.py',
   'PYMODULE'),
  ('kafka.protocol.admin',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\protocol\\admin.py',
   'PYMODULE'),
  ('kafka.protocol.api',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\protocol\\api.py',
   'PYMODULE'),
  ('kafka.protocol.commit',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\protocol\\commit.py',
   'PYMODULE'),
  ('kafka.protocol.fetch',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\protocol\\fetch.py',
   'PYMODULE'),
  ('kafka.protocol.frame',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\protocol\\frame.py',
   'PYMODULE'),
  ('kafka.protocol.group',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\protocol\\group.py',
   'PYMODULE'),
  ('kafka.protocol.metadata',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\protocol\\metadata.py',
   'PYMODULE'),
  ('kafka.protocol.offset',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\protocol\\offset.py',
   'PYMODULE'),
  ('kafka.protocol.parser',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\protocol\\parser.py',
   'PYMODULE'),
  ('kafka.protocol.produce',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\protocol\\produce.py',
   'PYMODULE'),
  ('kafka.protocol.struct',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\protocol\\struct.py',
   'PYMODULE'),
  ('kafka.protocol.types',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\protocol\\types.py',
   'PYMODULE'),
  ('kafka.record',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\record\\__init__.py',
   'PYMODULE'),
  ('kafka.record._crc32c',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\record\\_crc32c.py',
   'PYMODULE'),
  ('kafka.record.abc',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\record\\abc.py',
   'PYMODULE'),
  ('kafka.record.default_records',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\record\\default_records.py',
   'PYMODULE'),
  ('kafka.record.legacy_records',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\record\\legacy_records.py',
   'PYMODULE'),
  ('kafka.record.memory_records',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\record\\memory_records.py',
   'PYMODULE'),
  ('kafka.record.util',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\record\\util.py',
   'PYMODULE'),
  ('kafka.scram',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\scram.py',
   'PYMODULE'),
  ('kafka.serializer',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\serializer\\__init__.py',
   'PYMODULE'),
  ('kafka.serializer.abstract',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\serializer\\abstract.py',
   'PYMODULE'),
  ('kafka.structs',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\structs.py',
   'PYMODULE'),
  ('kafka.util',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\util.py',
   'PYMODULE'),
  ('kafka.vendor',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\vendor\\__init__.py',
   'PYMODULE'),
  ('kafka.vendor.enum34',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\vendor\\enum34.py',
   'PYMODULE'),
  ('kafka.vendor.selectors34',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\vendor\\selectors34.py',
   'PYMODULE'),
  ('kafka.vendor.six',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\vendor\\six.py',
   'PYMODULE'),
  ('kafka.vendor.socketpair',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\vendor\\socketpair.py',
   'PYMODULE'),
  ('kafka.version',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\kafka\\version.py',
   'PYMODULE'),
  ('kafka_config_manager',
   'E:\\vscode\\tomesmo\\kafka_config_manager.py',
   'PYMODULE'),
  ('kafka_monitor', 'E:\\vscode\\tomesmo\\kafka_monitor.py', 'PYMODULE'),
  ('logging', 'E:\\python\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'E:\\python\\lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mes_api', 'E:\\vscode\\tomesmo\\mes_api.py', 'PYMODULE'),
  ('mes_forwarder', 'E:\\vscode\\tomesmo\\mes_forwarder.py', 'PYMODULE'),
  ('mimetypes', 'E:\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'E:\\python\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'E:\\python\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'E:\\python\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'E:\\python\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'E:\\python\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'E:\\python\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'E:\\python\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'E:\\python\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'E:\\python\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'E:\\python\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'E:\\python\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'E:\\python\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'E:\\python\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'E:\\python\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'E:\\python\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'E:\\python\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'E:\\python\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.semaphore_tracker',
   'E:\\python\\lib\\multiprocessing\\semaphore_tracker.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'E:\\python\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'E:\\python\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'E:\\python\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'E:\\python\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'E:\\python\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'E:\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'E:\\python\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'E:\\python\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'E:\\python\\lib\\optparse.py', 'PYMODULE'),
  ('pickle', 'E:\\python\\lib\\pickle.py', 'PYMODULE'),
  ('pika',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\__init__.py',
   'PYMODULE'),
  ('pika.adapters',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\adapters\\__init__.py',
   'PYMODULE'),
  ('pika.adapters.base_connection',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\adapters\\base_connection.py',
   'PYMODULE'),
  ('pika.adapters.blocking_connection',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\adapters\\blocking_connection.py',
   'PYMODULE'),
  ('pika.adapters.select_connection',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\adapters\\select_connection.py',
   'PYMODULE'),
  ('pika.adapters.utils',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\adapters\\utils\\__init__.py',
   'PYMODULE'),
  ('pika.adapters.utils.connection_workflow',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\adapters\\utils\\connection_workflow.py',
   'PYMODULE'),
  ('pika.adapters.utils.io_services_utils',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\adapters\\utils\\io_services_utils.py',
   'PYMODULE'),
  ('pika.adapters.utils.nbio_interface',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\adapters\\utils\\nbio_interface.py',
   'PYMODULE'),
  ('pika.adapters.utils.selector_ioloop_adapter',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\adapters\\utils\\selector_ioloop_adapter.py',
   'PYMODULE'),
  ('pika.amqp_object',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\amqp_object.py',
   'PYMODULE'),
  ('pika.callback',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\callback.py',
   'PYMODULE'),
  ('pika.channel',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\channel.py',
   'PYMODULE'),
  ('pika.compat',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\compat.py',
   'PYMODULE'),
  ('pika.connection',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\connection.py',
   'PYMODULE'),
  ('pika.credentials',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\credentials.py',
   'PYMODULE'),
  ('pika.data',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\data.py',
   'PYMODULE'),
  ('pika.delivery_mode',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\delivery_mode.py',
   'PYMODULE'),
  ('pika.diagnostic_utils',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\diagnostic_utils.py',
   'PYMODULE'),
  ('pika.exceptions',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\exceptions.py',
   'PYMODULE'),
  ('pika.exchange_type',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\exchange_type.py',
   'PYMODULE'),
  ('pika.frame',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\frame.py',
   'PYMODULE'),
  ('pika.heartbeat',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\heartbeat.py',
   'PYMODULE'),
  ('pika.spec',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\spec.py',
   'PYMODULE'),
  ('pika.tcp_socket_opts',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\tcp_socket_opts.py',
   'PYMODULE'),
  ('pika.validators',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\pika\\validators.py',
   'PYMODULE'),
  ('pkgutil', 'E:\\python\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'E:\\python\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'E:\\python\\lib\\pprint.py', 'PYMODULE'),
  ('pqm_api', 'E:\\vscode\\tomesmo\\pqm_api.py', 'PYMODULE'),
  ('py_compile', 'E:\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'E:\\python\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'E:\\python\\lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics', 'E:\\python\\lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('queue', 'E:\\python\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'E:\\python\\lib\\quopri.py', 'PYMODULE'),
  ('rabbitmq_config_manager',
   'E:\\vscode\\tomesmo\\rabbitmq_config_manager.py',
   'PYMODULE'),
  ('rabbitmq_monitor', 'E:\\vscode\\tomesmo\\rabbitmq_monitor.py', 'PYMODULE'),
  ('random', 'E:\\python\\lib\\random.py', 'PYMODULE'),
  ('requests',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter', 'E:\\python\\lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'E:\\python\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'E:\\python\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'E:\\python\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'E:\\python\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'E:\\python\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'E:\\python\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'E:\\python\\lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'E:\\python\\lib\\socketserver.py', 'PYMODULE'),
  ('ssl', 'E:\\python\\lib\\ssl.py', 'PYMODULE'),
  ('string', 'E:\\python\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'E:\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'E:\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'E:\\python\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'E:\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'E:\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'E:\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'E:\\python\\lib\\threading.py', 'PYMODULE'),
  ('tracemalloc', 'E:\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'E:\\python\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'E:\\python\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib3',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu', 'E:\\python\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'E:\\python\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'E:\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('werkzeug',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\datastructures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('xml', 'E:\\python\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers', 'E:\\python\\lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat', 'E:\\python\\lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.sax', 'E:\\python\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'E:\\python\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'E:\\python\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'E:\\python\\lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'E:\\python\\lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader', 'E:\\python\\lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('xmlrpc', 'E:\\python\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'E:\\python\\lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'E:\\python\\lib\\zipfile.py', 'PYMODULE'),
  ('zipp',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.py310compat',
   'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\zipp\\py310compat.py',
   'PYMODULE')])
