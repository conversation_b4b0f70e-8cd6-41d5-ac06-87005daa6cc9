# MES設備數據轉發管理系統 - 備份總結

## 備份完成時間
**2025年6月13日 15:36**

## 備份文件列表

### 1. 源碼備份 (推薦使用)
- **文件名**: `backup_20250613_153048.zip`
- **大小**: 147 KB
- **類型**: 精選源碼備份
- **包含內容**: 所有核心源碼文件、配置文件、文檔
- **恢復腳本**: `restore_from_backup_20250613_153048.py`

### 2. 完整項目備份
- **文件名**: `project_backup_20250613_153643.zip`
- **大小**: 153 KB
- **類型**: 完整項目備份
- **包含內容**: 所有項目文件（排除緩存和臨時文件）

## 備份內容詳細說明

### 核心應用文件
- `app.py` - 主要Flask應用
- `start.py` - 應用啟動腳本
- `open_browser.py` - 瀏覽器自動開啟

### 設備管理模組
- `device_manager.py` - 設備管理核心
- `devices.json` - 設備配置數據

### 數據源監控
- `kafka_monitor.py` - Kafka數據監控
- `kafka_config_manager.py` - Kafka配置管理
- `rabbitmq_monitor.py` - RabbitMQ數據監控
- `rabbitmq_config_manager.py` - RabbitMQ配置管理
- `getdata.py` - Kafka數據獲取
- `getdatafromrabbitmq.py` - RabbitMQ數據獲取

### MES集成模組
- `mes_api.py` - MES API接口
- `mes_forwarder.py` - MES數據轉發
- `MESnewtdc.py` - MES參考實現

### 前端界面
- `static/` - 靜態資源目錄
  - `style.css` - 樣式文件
  - `script.js` - JavaScript腳本
  - `logo.png` - 系統Logo
- `templates/` - HTML模板目錄
  - `index.html` - 主頁面模板

### 測試和工具
- `test_data_simulator.py` - 測試數據模擬器
- `test_forwarding_stats.py` - 轉發統計測試
- `test_mes_forwarding_states.py` - MES轉發狀態測試
- `test_multi_products.py` - 多產品測試
- `rengong.py` - 人工測試工具

### 數據遷移腳本
- `migrate_devices.py` - 設備數據遷移
- `migrate_forwarding_stats.py` - 轉發統計遷移
- `migrate_period_logs.py` - 週期日誌遷移

### 打包和部署
- `build_exe.py` - 中文版可執行文件打包
- `build_english_exe.py` - 英文版可執行文件打包
- `create_zip.py` - 中文版ZIP包創建
- `create_english_zip.py` - 英文版ZIP包創建
- `create_docker_package.py` - Docker包創建
- `*.spec` - PyInstaller規格文件

### 配置文件
- `config.py` - 應用配置
- `kafka_config.json` - Kafka配置
- `rabbitmq_config.json` - RabbitMQ配置
- `requirements.txt` - Python依賴列表

### 文檔文件
- `README.md` - 項目說明文檔
- `README_RabbitMQ.md` - RabbitMQ使用說明
- `COMPLETE_FEATURE_SUMMARY.md` - 完整功能總結
- `FEATURE_UPDATE.md` - 功能更新記錄
- `FINAL_FEATURE_SUMMARY.md` - 最終功能總結
- `UPDATE_SUMMARY.md` - 更新總結

## 恢復指南

### 方法1: 使用恢復腳本 (推薦)
```bash
python restore_from_backup_20250613_153048.py
```

### 方法2: 手動恢復
1. 解壓備份文件到目標目錄
2. 安裝Python依賴:
   ```bash
   pip install -r requirements.txt
   ```
3. 運行應用:
   ```bash
   python app.py
   ```

## 系統特性

### 主要功能
- 設備數據實時監控
- Kafka/RabbitMQ雙數據源支持
- MES系統集成
- 工單管理和進度追蹤
- 自動數據轉發
- Web界面管理

### 技術架構
- **後端**: Python Flask
- **前端**: HTML + CSS + JavaScript
- **數據源**: Kafka, RabbitMQ
- **集成**: MES API
- **部署**: 支持Docker和可執行文件

## 備份腳本

### 可用的備份腳本
1. `backup_current_system.py` - 精選源碼備份
2. `simple_backup.py` - 簡單完整備份
3. `backup_complete_project.py` - 完整項目備份

### 使用方法
```bash
# 創建源碼備份
python backup_current_system.py

# 創建簡單備份
python simple_backup.py
```

## 注意事項

1. **環境要求**: Python 3.7+
2. **依賴安裝**: 確保安裝requirements.txt中的所有依賴
3. **配置調整**: 根據實際環境調整配置文件
4. **權限設置**: 確保應用有適當的文件讀寫權限
5. **網絡配置**: 確保能夠訪問Kafka/RabbitMQ和MES系統

## 聯繫信息
- 開發部門: T2 SI系統整合技術部
- 項目版本: 0.0.1-beta-1
- 備份日期: 2025-06-13

---
*此文檔由自動備份系統生成*
