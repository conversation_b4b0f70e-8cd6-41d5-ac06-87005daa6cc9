#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試默認配置設置
驗證EAP設置和SIE設置的默認消息類型是否為WorkCompleted
"""

import requests
import json

def test_kafka_default_config():
    """測試Kafka默認配置"""
    print("=== 測試Kafka默認配置 ===")
    
    try:
        # 獲取Kafka配置
        response = requests.get('http://127.0.0.1:5000/api/kafka/config')
        config = response.json()
        
        print(f"✅ 成功獲取Kafka配置")
        print(f"📋 配置內容:")
        for key, value in config.items():
            print(f"   {key}: {value}")
        
        # 檢查默認消息名稱
        message_name = config.get('message_name')
        expected_message_name = 'CFX.Production.WorkCompleted'
        
        if message_name == expected_message_name:
            print(f"✅ Kafka消息名稱默認值正確: {message_name}")
        else:
            print(f"❌ Kafka消息名稱默認值錯誤: 期望 {expected_message_name}, 實際 {message_name}")
        
        return message_name == expected_message_name
        
    except Exception as e:
        print(f"❌ 測試Kafka配置失敗: {e}")
        return False

def test_rabbitmq_default_config():
    """測試RabbitMQ默認配置"""
    print("\n=== 測試RabbitMQ默認配置 ===")
    
    try:
        # 獲取RabbitMQ配置
        response = requests.get('http://127.0.0.1:5000/api/rabbitmq/config')
        config = response.json()
        
        print(f"✅ 成功獲取RabbitMQ配置")
        print(f"📋 配置內容:")
        for key, value in config.items():
            print(f"   {key}: {value}")
        
        # 檢查默認Routing Key模式
        routing_key_pattern = config.get('routing_key_pattern')
        expected_pattern = 'edadata.cfx.#.{device_id}.CFX.Production.WorkCompleted'
        
        if routing_key_pattern == expected_pattern:
            print(f"✅ RabbitMQ Routing Key模式默認值正確: {routing_key_pattern}")
        else:
            print(f"❌ RabbitMQ Routing Key模式默認值錯誤:")
            print(f"   期望: {expected_pattern}")
            print(f"   實際: {routing_key_pattern}")
        
        return routing_key_pattern == expected_pattern
        
    except Exception as e:
        print(f"❌ 測試RabbitMQ配置失敗: {e}")
        return False

def test_config_persistence():
    """測試配置持久化"""
    print("\n=== 測試配置持久化 ===")
    
    try:
        # 測試修改Kafka配置為UnitsDeparted
        print("🧪 測試1: 修改Kafka配置為UnitsDeparted")
        
        kafka_config = {
            'bootstrap_servers': '10.148.208.112:9092',
            'group_id': 'mes_forwarder_group',
            'factory': 'CZ',
            'mfg_plant_code': 'MAG',
            'message_type': 'DEVICE_CFX',
            'message_name': 'CFX.Production.UnitsDeparted'
        }
        
        response = requests.post('http://127.0.0.1:5000/api/kafka/config', 
                               json=kafka_config,
                               headers={'Content-Type': 'application/json'})
        result = response.json()
        
        if result.get('success'):
            print(f"✅ 成功修改Kafka配置為UnitsDeparted")
        else:
            print(f"❌ 修改Kafka配置失敗: {result.get('error')}")
            return False
        
        # 重新獲取配置確認修改
        response = requests.get('http://127.0.0.1:5000/api/kafka/config')
        config = response.json()
        
        if config.get('message_name') == 'CFX.Production.UnitsDeparted':
            print(f"✅ 配置修改已保存: {config.get('message_name')}")
        else:
            print(f"❌ 配置修改未保存: {config.get('message_name')}")
            return False
        
        # 測試修改RabbitMQ配置為UnitsDeparted
        print("\n🧪 測試2: 修改RabbitMQ配置為UnitsDeparted")
        
        rabbitmq_config = {
            'host': '************',
            'port': 30025,
            'username': 'guest',
            'password': 'guest',
            'exchange_name': 'message-bus',
            'exchange_type': 'topic',
            'routing_key_pattern': 'edadata.cfx.#.{device_id}.CFX.Production.UnitsDeparted'
        }
        
        response = requests.post('http://127.0.0.1:5000/api/rabbitmq/config', 
                               json=rabbitmq_config,
                               headers={'Content-Type': 'application/json'})
        result = response.json()
        
        if result.get('success'):
            print(f"✅ 成功修改RabbitMQ配置為UnitsDeparted")
        else:
            print(f"❌ 修改RabbitMQ配置失敗: {result.get('error')}")
            return False
        
        # 重新獲取配置確認修改
        response = requests.get('http://127.0.0.1:5000/api/rabbitmq/config')
        config = response.json()
        
        expected_pattern = 'edadata.cfx.#.{device_id}.CFX.Production.UnitsDeparted'
        if config.get('routing_key_pattern') == expected_pattern:
            print(f"✅ 配置修改已保存: {config.get('routing_key_pattern')}")
        else:
            print(f"❌ 配置修改未保存: {config.get('routing_key_pattern')}")
            return False
        
        # 恢復默認配置為WorkCompleted
        print("\n🧪 測試3: 恢復默認配置為WorkCompleted")
        
        # 恢復Kafka配置
        kafka_config['message_name'] = 'CFX.Production.WorkCompleted'
        response = requests.post('http://127.0.0.1:5000/api/kafka/config', 
                               json=kafka_config,
                               headers={'Content-Type': 'application/json'})
        
        # 恢復RabbitMQ配置
        rabbitmq_config['routing_key_pattern'] = 'edadata.cfx.#.{device_id}.CFX.Production.WorkCompleted'
        response = requests.post('http://127.0.0.1:5000/api/rabbitmq/config', 
                               json=rabbitmq_config,
                               headers={'Content-Type': 'application/json'})
        
        print(f"✅ 已恢復默認配置為WorkCompleted")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試配置持久化失敗: {e}")
        return False

def test_frontend_defaults():
    """測試前端默認選項"""
    print("\n=== 測試前端默認選項 ===")
    
    try:
        # 獲取主頁面HTML
        response = requests.get('http://127.0.0.1:5000/')
        html_content = response.text
        
        print(f"✅ 成功獲取主頁面HTML")
        
        # 檢查Kafka消息名稱下拉框的默認選項
        if 'value="CFX.Production.WorkCompleted" selected' in html_content:
            print(f"✅ Kafka消息名稱下拉框默認選中WorkCompleted")
            kafka_default_ok = True
        else:
            print(f"❌ Kafka消息名稱下拉框默認選項不正確")
            kafka_default_ok = False
        
        # 檢查RabbitMQ Routing Key模式下拉框的默認選項
        if 'value="edadata.cfx.#.{device_id}.CFX.Production.WorkCompleted" selected' in html_content:
            print(f"✅ RabbitMQ Routing Key模式下拉框默認選中WorkCompleted")
            rabbitmq_default_ok = True
        else:
            print(f"❌ RabbitMQ Routing Key模式下拉框默認選項不正確")
            rabbitmq_default_ok = False
        
        return kafka_default_ok and rabbitmq_default_ok
        
    except Exception as e:
        print(f"❌ 測試前端默認選項失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("開始測試默認配置設置...\n")
    
    test_results = []
    
    # 測試Kafka默認配置
    test_results.append(test_kafka_default_config())
    
    # 測試RabbitMQ默認配置
    test_results.append(test_rabbitmq_default_config())
    
    # 測試配置持久化
    test_results.append(test_config_persistence())
    
    # 測試前端默認選項
    test_results.append(test_frontend_defaults())
    
    # 總結測試結果
    print("\n=== 測試結果總結 ===")
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"✅ 通過測試: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有測試通過！默認配置設置正確。")
        print("\n📋 功能確認:")
        print("   ✅ EAP設置默認使用WorkCompleted消息類型")
        print("   ✅ SIE設置默認使用WorkCompleted Routing Key模式")
        print("   ✅ 用戶可以修改並保存其他消息類型")
        print("   ✅ 重新啟動後會加載保存的配置")
        print("   ✅ 前端界面默認選中WorkCompleted選項")
    else:
        print("❌ 部分測試失敗，請檢查配置設置。")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
