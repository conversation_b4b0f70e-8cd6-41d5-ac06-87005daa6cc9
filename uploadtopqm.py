'''
Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
Date: 2023-12-15 16:00:12
LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
LastEditTime: 2024-11-28 16:31:53
FilePath: \系统程序\开发自用模块\MES\PQM数据交互pqm.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import requests
import json

# pqm_http变量
url_pqm = "http://*************:8090/sensordata"
headers_pqm = {
    # 'Content-Type': "application/json"
    }
params_pqm = {
    'sensorId': "UploadMachineData"
    }
data_pqm = {
    'interfaceID': '',
    'status': 0,
    'statusCode': '',
    'passQty': 0,
    'failQty': 8,
    'errorCnt': 0,
    'errorTimes': 0,
    'cycleTime': 0,
    'runningTime': 0,
    'waitingTime': 0,
    'selfCheck': 1,
    'inputQty': 8,
    'barcode': 'NFA',
    'model': '',
    'paramList': [{'paramCode': 'CT_M', 'paramValue': '0'}, {'paramCode': 'CT_Q', 'paramValue': '0'}]
    }



#1. 测试数据上传接口
def pqm_http(url_pqm,data_pqm,headers_pqm,params_pqm):
    #发送数据并接收返回数据
    receive = requests.request("POST", url_pqm, data=str(data_pqm).encode('utf-8'), headers=headers_pqm, params=params_pqm)
    # receive = requests.request("POST", url_pqm, data=str(data_pqm).encode('utf-8'), headers=headers_pqm, params=params_pqm)
    print(receive)
    # try:
    #     receive = json.loads(receive.text)
    # except:
    # receive = eval(receive.text)
    # print(receive)
    #str转换为字典列表
    # receive = eval(receive.text)
    # return receive

# 1.1 Demo
data_pqm['interfaceID'] = 'S123456'
data_pqm['status'] = 0
data_pqm['statusCode'] = ''
data_pqm['passQty'] = 1
data_pqm['failQty'] = 0
data_pqm['errorCnt'] = 0
data_pqm['errorTimes'] = 0.0
data_pqm['cycleTime'] = 0.0
data_pqm['runningTime'] = 0.0
data_pqm['waitingTime'] = 1.2
data_pqm['selfCheck'] = 1
data_pqm['inputQty'] = 1
data_pqm['barcode'] = 'test123test123test123'
data_pqm['model'] = 'WT1018AI'
data_pqm['paramList'] = [
    {'paramCode': 'CT_M', 'paramValue': '0'},
    {'paramCode': 'CT_Q', 'paramValue': '0'}
]
testdata = []
testdata.append(data_pqm)
print(data_pqm)
print(url_pqm)
print(testdata)
print(headers_pqm)
print(params_pqm)
pqm_http = pqm_http(url_pqm, testdata, headers_pqm, params_pqm)
# print(pqm_http)



