#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模擬數據生成器
用於測試模式下生成模擬的生產數據和MES轉發
"""

import threading
import time
import random
import uuid
from datetime import datetime
import json

class FakeDataGenerator:
    def __init__(self, device_manager, mes_api=None):
        self.device_manager = device_manager
        self.mes_api = mes_api
        self.is_running = False
        self.generation_thread = None
        self.generation_interval = 3  # 每3秒生成一次數據
        self.active_devices = []
        
    def start_generation(self, interval=3):
        """開始生成模擬數據"""
        if self.is_running:
            print("⚠️ 模擬數據生成器已在運行中")
            return False
            
        self.generation_interval = interval
        self.is_running = True
        
        # 獲取所有活躍設備
        self.update_active_devices()
        
        # 啟動生成線程
        self.generation_thread = threading.Thread(target=self._generation_loop, daemon=True)
        self.generation_thread.start()
        
        print(f"🎭 模擬數據生成器已啟動，間隔: {interval}秒")
        print(f"📊 活躍設備數量: {len(self.active_devices)}")
        return True
    
    def stop_generation(self):
        """停止生成模擬數據"""
        if not self.is_running:
            return False
            
        self.is_running = False
        if self.generation_thread:
            self.generation_thread.join(timeout=2)
            
        print("⏹️ 模擬數據生成器已停止")
        return True
    
    def update_active_devices(self):
        """更新活躍設備列表"""
        devices = self.device_manager.get_all_devices()
        self.active_devices = []
        
        for device_id, device in devices.items():
            # 只為有工單且啟用MES轉發的設備生成數據
            if device.get('isMonitoring', False) and device.get('workOrders', []):
                self.active_devices.append(device_id)
                
        print(f"🔄 更新活躍設備列表: {len(self.active_devices)} 個設備")
    
    def _generation_loop(self):
        """數據生成主循環"""
        while self.is_running:
            try:
                # 每次循環更新活躍設備列表
                self.update_active_devices()
                
                if not self.active_devices:
                    print("⚠️ 沒有活躍設備，跳過本次生成")
                    time.sleep(self.generation_interval)
                    continue
                
                # 隨機選擇1-3個設備生成數據
                num_devices = min(random.randint(1, 3), len(self.active_devices))
                selected_devices = random.sample(self.active_devices, num_devices)
                
                for device_id in selected_devices:
                    self._generate_device_data(device_id)
                
                # 等待下一次生成
                time.sleep(self.generation_interval)
                
            except Exception as e:
                print(f"❌ 模擬數據生成錯誤: {e}")
                time.sleep(1)
    
    def _generate_device_data(self, device_id):
        """為指定設備生成模擬數據"""
        try:
            # 生成模擬產品條碼
            barcode = f"FAKE-{uuid.uuid4().hex[:8].upper()}"
            
            print(f"🎭 生成模擬數據: 設備={device_id}, 條碼={barcode}")
            
            # 模擬處理生產數據
            self._process_fake_production_data(device_id, barcode)
            
        except Exception as e:
            print(f"❌ 設備 {device_id} 生成數據失敗: {e}")
    
    def _process_fake_production_data(self, device_id, barcode):
        """處理模擬生產數據"""
        try:
            # 獲取設備信息
            device = self.device_manager.get_device(device_id)
            if not device:
                print(f"⚠️ 設備 {device_id} 不存在")
                return
            
            # 獲取當前工單
            current_work_order = self.device_manager.get_current_work_order(device_id)
            if not current_work_order:
                print(f"⚠️ 設備 {device_id} 沒有當前工單")
                return
            
            # 獲取模穴數
            cavity_count = current_work_order.get('cavityCount', 1)
            
            print(f"📦 模擬收到設備 {device_id} 的生產數據")
            print(f"🕒 時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"📋 產品條碼: {barcode}")
            print(f"🔧 模穴數: {cavity_count}")
            
            # 模擬MES轉發
            if self.mes_api:
                success, response = self._simulate_mes_forwarding(device_id, barcode)
                
                # 更新設備統計
                self.device_manager.update_device_production(
                    device_id=device_id,
                    unit_count=1,
                    log_message=barcode,
                    is_forwarded=success
                )
                
                # 更新工單進度
                if success:
                    self.device_manager.update_work_order_progress(device_id, cavity_count)
            else:
                # 沒有MES API時，只更新統計（標記為未轉發）
                self.device_manager.update_device_production(
                    device_id=device_id,
                    unit_count=1,
                    log_message=barcode,
                    is_forwarded=False
                )
                print(f"⚠️ 沒有MES API，數據未轉發")
                
        except Exception as e:
            print(f"❌ 處理設備 {device_id} 模擬數據失敗: {e}")
    
    def _simulate_mes_forwarding(self, device_id, barcode):
        """模擬MES轉發"""
        try:
            # 90%成功率
            success = random.random() < 0.9
            
            if success:
                # 模擬成功響應
                response = {
                    'result': 'OK',
                    'description': random.choice(['SUCCESS', 'MO_HAS_FULL', 'DATA_RECEIVED'])
                }
                print(f"✅ 模擬MES轉發成功: 設備={device_id}, 條碼={barcode}")
            else:
                # 模擬失敗響應
                response = {
                    'result': 'FAIL',
                    'description': random.choice(['STATION_NOT_EXIST', 'NETWORK_ERROR', 'INVALID_DATA'])
                }
                print(f"❌ 模擬MES轉發失敗: 設備={device_id}, 條碼={barcode}")
                
                # 記錄MES錯誤
                self.device_manager.add_mes_error(device_id, {
                    'timestamp': datetime.now().isoformat(),
                    'message': f"❌ 失敗: {response['description']}",
                    'full_response': f"HTTP 200 | 模擬響應: {json.dumps(response, ensure_ascii=False)}",
                    'card_response': f"HTTP 200 | {response['description']}",
                    'request_data': {
                        'url': 'http://fake.mes.api/test',
                        'method': 'POST',
                        'device_id': device_id,
                        'barcode': barcode
                    }
                })
            
            return success, response
            
        except Exception as e:
            print(f"❌ 模擬MES轉發異常: {e}")
            return False, {'result': 'ERROR', 'description': str(e)}
    
    def get_status(self):
        """獲取生成器狀態"""
        return {
            'is_running': self.is_running,
            'generation_interval': self.generation_interval,
            'active_devices_count': len(self.active_devices),
            'active_devices': self.active_devices
        }
    
    def set_interval(self, interval):
        """設置生成間隔"""
        if interval < 1:
            interval = 1
        elif interval > 60:
            interval = 60
            
        self.generation_interval = interval
        print(f"🔧 模擬數據生成間隔已設置為: {interval}秒")

# 全局模擬數據生成器實例
fake_data_generator = None

def get_fake_data_generator():
    """獲取全局模擬數據生成器實例"""
    global fake_data_generator
    return fake_data_generator

def init_fake_data_generator(device_manager, mes_api=None):
    """初始化全局模擬數據生成器"""
    global fake_data_generator
    fake_data_generator = FakeDataGenerator(device_manager, mes_api)
    return fake_data_generator
