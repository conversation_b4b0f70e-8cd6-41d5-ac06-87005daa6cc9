# RabbitMQ數據獲取程式使用說明

## 概述
這是一個獨立的Python程式，用於從RabbitMQ服務器持續獲取UnitsDeparted生產數據。

## 配置信息
- **服務器IP**: ************
- **端口**: 30025
- **用戶名**: guest
- **密碼**: guest
- **Exchange**: message-bus (Topic類型)
- **Routing Key**: edadata.cfx.#.OT01270001.CFX.Production.UnitsDeparted
- **設備ID**: OT01270001

## 安裝依賴
```bash
pip install pika==1.3.2
```

## 運行程式
```bash
python getdatafromrabbitmq.py
```

## 功能特點
1. **自動重連**: 支持連接失敗時自動重試（最多5次）
2. **日誌記錄**: 同時輸出到控制台和日誌文件 `rabbitmq_consumer.log`
3. **消息解析**: 自動識別JSON格式消息並提取關鍵信息
4. **錯誤處理**: 完善的異常處理機制
5. **優雅停止**: 支持Ctrl+C優雅停止程式

## 輸出示例
程式會顯示接收到的每條消息，包括：
- 時間戳
- Routing Key
- 產品序號
- 工站信息
- 完整的JSON數據

## 修改設備ID
如需監聽其他設備，請修改 `getdatafromrabbitmq.py` 文件中的 `device_id` 變量：
```python
self.device_id = 'YOUR_DEVICE_ID'  # 替換為您的設備ID
```

## 停止程式
按 `Ctrl+C` 停止程式運行。
