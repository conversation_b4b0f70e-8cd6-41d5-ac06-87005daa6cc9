## 📋 項目基本信息

### 項目名稱
設備數據轉發管理系統

### 我想要什麼
做一個網頁，可以管理工廠設備，監控設備的生產數據，並且自動把數據傳送到MES系統。

### 具體需求
- 我有很多設備，每個設備都有編號、在哪條線、在哪個站等信息
- 設備會產生生產數據（比如生產了什麼產品、數量等）
- 我想在網頁上看到每個設備的狀態和生產情況
- 當設備生產產品時，要自動把數據傳送到MES系統
- 需要管理工單（生產任務），知道每個工單完成了多少

## 🎯 主要功能

### 1. 設備管理
- 可以添加新設備，填寫設備編號、線名、站名等
- 可以修改設備信息
- 可以刪除不用的設備
- 可以看到所有設備的列表

### 2. 生產數據監控
- 能夠接收設備的生產數據（從Kafka或RabbitMQ）
- 在網頁上顯示每個設備最新的生產情況
- 統計每個設備生產了多少產品

### 3. MES數據轉發
- 當收到設備生產數據時，自動轉發到MES系統
- 如果轉發失敗，要記錄錯誤信息
- 統計轉發成功和失敗的數量

### 4. 工單管理
- 可以為設備設置工單（生產任務）
- 顯示工單進度（完成了多少/總共要生產多少）
- 工單完成後自動切換到下一個工單

### 5. 網頁界面
- 用卡片的方式顯示每個設備
- 實時更新數據，不用手動刷新
- 界面要簡潔好用

## 🔧 開發步驟

### 第一步：基礎架構和界面
**給AI的指令：**
"請幫我創建一個Python Flask網頁應用，包含以下功能：
1. 一個主頁面，可以顯示設備列表
2. 可以添加、編輯、刪除設備的功能
3. 設備信息包括：設備ID、線名、段名、組名、站名
4. 使用簡潔的網頁設計，藍色主題
5. 數據保存在JSON文件中
6. 請創建完整的HTML、CSS、JavaScript和Python代碼"

**期望結果：**
- 能夠運行的Flask應用
- 基本的設備管理功能
- 簡潔的網頁界面

### 第二步：測試模式和模擬數據
**給AI的指令：**
"請為這個系統添加測試模式，包含：
1. 模擬數據生成器，可以模擬設備生產數據
2. 模擬MES轉發功能，不真正調用MES API，只是打印日誌
3. 可以在網頁上啟動/停止模擬數據生成
4. 可以調整模擬數據的生成頻率
5. 在設備卡片上顯示模擬的生產數據和統計信息
6. 添加測試設備數據，方便測試"

**期望結果：**
- 完整的測試環境
- 可以看到模擬的生產數據流
- 驗證所有功能邏輯正確

### 第三步：真實API接口集成
**給AI的指令：**
"請為系統添加真實的API接口，我會提供具體的接口信息：

**Kafka接口：**
- 輸入：Kafka服務器地址、端口、主題名稱
- 輸出：設備生產數據（JSON格式，包含設備ID和產品信息）
- 請創建Kafka監控模塊的框架，我會填入具體的連接和數據解析邏輯

**MES API接口：**
- 輸入：設備ID、產品條碼、工單號等
- 輸出：成功/失敗狀態和響應信息
- 請創建MES API調用模塊的框架，我會填入具體的API地址和參數

**RabbitMQ接口（備用）：**
- 輸入：RabbitMQ服務器信息
- 輸出：設備生產數據
- 請創建RabbitMQ監控模塊的框架

請保留測試模式，讓我可以在測試模式和真實模式之間切換。"

**期望結果：**
- API接口的框架代碼
- 配置管理功能
- 我可以填入具體的API邏輯

## 💡 給AI的關鍵信息

### 界面要求
- 使用藍色主題（RGB: 0,135,220）
- 卡片式佈局顯示設備
- 緊湊的設計，不要太多空白
- 支持1080P屏幕顯示

### 數據結構
- 設備信息：設備ID、線名、段名、組名、站名、是否啟用、生產統計
- 生產數據：產品條碼、生產時間、是否轉發成功
- 工單信息：工單號、目標數量、已完成數量、機種名

### 技術要求
- 使用Python Flask
- 前端用HTML+CSS+JavaScript（不用複雜框架）
- 數據保存在JSON文件
- 支持多線程處理
- 要有錯誤處理和日誌記錄

### 重要功能
- 實時數據更新（前端定時刷新）
- 數據去重（避免重複處理同一個產品）
- 錯誤記錄（MES轉發失敗要記錄原因）
- 工單自動切換（完成後自動換下一個）

## � 成功標準

### 第一步完成標準
- 網頁能正常打開和使用
- 可以添加、修改、刪除設備
- 界面看起來整潔好用

### 第二步完成標準
- 模擬數據能正常生成和顯示
- 所有統計數據正確
- 工單管理功能正常

### 第三步完成標準
- 能連接真實的Kafka/RabbitMQ
- 能調用真實的MES API
- 系統穩定運行，錯誤處理完善

## 📝 注意事項

1. **安全考慮**：API接口部分我會自己調整，AI只需要提供框架
2. **簡單優先**：不需要複雜的技術，能用就行
3. **測試重要**：每一步按步驟逐個執行，都要能測試驗證，測試完我會再要求你進行下一步開發
4. **文檔清楚**：代碼要有註釋，我能看懂怎麼修改
5. **錯誤處理**：要考慮網絡斷線、API失敗等情況

這個大綱的優點：
- 語言簡單，容易理解
- 分步驟開發，風險小
- 重點突出，不會迷失方向
- 給AI的指令很具體，容易執行
- 保留了我自己調整的空間
