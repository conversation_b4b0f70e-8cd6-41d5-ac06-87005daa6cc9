#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試設備ID編輯功能
驗證編輯設備時可以修改設備ID，並檢查重複ID的驗證
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from device_manager import <PERSON>ce<PERSON>ana<PERSON>

def test_device_id_edit():
    """測試設備ID編輯功能"""
    print("=== 測試設備ID編輯功能 ===")
    
    device_manager = DeviceManager()
    
    # 清空現有設備（僅用於測試）
    original_devices = device_manager.devices.copy()
    device_manager.devices = {}
    
    try:
        # 添加測試設備
        test_devices = [
            {
                'deviceId': 'TEST_DEVICE_001',
                'lineName': 'LINE_A',
                'stationName': 'STATION_1',
                'sectionName': 'SECTION_A',
                'groupName': 'GROUP_1'
            },
            {
                'deviceId': 'TEST_DEVICE_002',
                'lineName': 'LINE_B',
                'stationName': 'STATION_2',
                'sectionName': 'SECTION_B',
                'groupName': 'GROUP_2'
            }
        ]
        
        for device in test_devices:
            device_manager.add_device(
                device_id=device['deviceId'],
                line_name=device['lineName'],
                station_name=device['stationName'],
                section_name=device['sectionName'],
                group_name=device['groupName']
            )
            print(f"✅ 已添加測試設備: {device['deviceId']}")
        
        print(f"\n📋 當前設備列表: {list(device_manager.devices.keys())}")
        
        # 測試1: 正常修改設備ID
        print("\n🧪 測試1: 正常修改設備ID")
        old_device_id = 'TEST_DEVICE_001'
        new_device_id = 'TEST_DEVICE_001_UPDATED'
        
        update_data = {
            'deviceId': new_device_id,
            'lineName': 'LINE_A_UPDATED',
            'stationName': 'STATION_1_UPDATED'
        }
        
        try:
            device_manager.update_device(old_device_id, update_data)
            print(f"✅ 成功修改設備ID: {old_device_id} → {new_device_id}")
            
            # 檢查舊ID是否已刪除
            if old_device_id not in device_manager.devices:
                print(f"✅ 舊設備ID已刪除: {old_device_id}")
            else:
                print(f"❌ 舊設備ID仍存在: {old_device_id}")
            
            # 檢查新ID是否存在
            if new_device_id in device_manager.devices:
                print(f"✅ 新設備ID已創建: {new_device_id}")
                updated_device = device_manager.devices[new_device_id]
                print(f"   設備信息: {updated_device['lineName']}, {updated_device['stationName']}")
            else:
                print(f"❌ 新設備ID不存在: {new_device_id}")
                
        except Exception as e:
            print(f"❌ 修改設備ID失敗: {e}")
        
        # 測試2: 嘗試修改為已存在的設備ID（應該失敗）
        print("\n🧪 測試2: 嘗試修改為已存在的設備ID")
        
        update_data_duplicate = {
            'deviceId': 'TEST_DEVICE_002',  # 這個ID已經存在
            'lineName': 'LINE_CONFLICT'
        }
        
        try:
            device_manager.update_device(new_device_id, update_data_duplicate)
            print(f"❌ 不應該成功: 修改為重複的設備ID")
        except ValueError as e:
            print(f"✅ 正確阻止重複設備ID: {e}")
        except Exception as e:
            print(f"❌ 意外錯誤: {e}")
        
        # 測試3: 修改設備ID為相同的ID（應該成功，不需要檢查重複）
        print("\n🧪 測試3: 修改設備ID為相同的ID")
        
        update_data_same = {
            'deviceId': new_device_id,  # 相同的ID
            'lineName': 'LINE_A_SAME_ID_UPDATE'
        }
        
        try:
            device_manager.update_device(new_device_id, update_data_same)
            print(f"✅ 成功更新設備（相同ID）: {new_device_id}")
            
            # 檢查更新是否生效
            updated_device = device_manager.devices[new_device_id]
            if updated_device['lineName'] == 'LINE_A_SAME_ID_UPDATE':
                print(f"✅ 設備信息已更新: {updated_device['lineName']}")
            else:
                print(f"❌ 設備信息未更新: {updated_device['lineName']}")
                
        except Exception as e:
            print(f"❌ 更新設備失敗: {e}")
        
        # 測試4: 不修改設備ID，只修改其他字段
        print("\n🧪 測試4: 不修改設備ID，只修改其他字段")
        
        update_data_no_id = {
            'lineName': 'LINE_A_NO_ID_CHANGE',
            'stationName': 'STATION_NO_ID_CHANGE'
        }
        
        try:
            device_manager.update_device(new_device_id, update_data_no_id)
            print(f"✅ 成功更新設備（不修改ID）: {new_device_id}")
            
            # 檢查更新是否生效
            updated_device = device_manager.devices[new_device_id]
            if updated_device['lineName'] == 'LINE_A_NO_ID_CHANGE':
                print(f"✅ 設備信息已更新: {updated_device['lineName']}")
            else:
                print(f"❌ 設備信息未更新: {updated_device['lineName']}")
                
        except Exception as e:
            print(f"❌ 更新設備失敗: {e}")
        
        print(f"\n📋 最終設備列表: {list(device_manager.devices.keys())}")
        
        # 測試5: 測試工單數據是否正確遷移
        print("\n🧪 測試5: 測試工單數據遷移")
        
        # 為設備添加工單
        device_manager.add_work_order(
            device_id=new_device_id,
            work_order_number='WO_TEST_001',
            target_quantity=100,
            model_name='TEST_MODEL',
            description='測試工單'
        )
        
        print(f"✅ 已為設備 {new_device_id} 添加工單")
        
        # 修改設備ID
        final_device_id = 'TEST_DEVICE_FINAL'
        update_data_with_workorder = {
            'deviceId': final_device_id,
            'lineName': 'LINE_FINAL'
        }
        
        try:
            device_manager.update_device(new_device_id, update_data_with_workorder)
            print(f"✅ 成功修改帶工單的設備ID: {new_device_id} → {final_device_id}")
            
            # 檢查工單是否正確遷移
            work_orders = device_manager.get_work_orders(final_device_id)
            if work_orders and len(work_orders) > 0:
                print(f"✅ 工單數據已正確遷移: {len(work_orders)}個工單")
                print(f"   工單號: {work_orders[0]['workOrderNumber']}")
            else:
                print(f"❌ 工單數據遷移失敗")
                
        except Exception as e:
            print(f"❌ 修改帶工單的設備ID失敗: {e}")
        
        print("\n✅ 設備ID編輯功能測試完成")
        
    finally:
        # 恢復原始設備數據
        device_manager.devices = original_devices
        device_manager._save_devices()
        print("\n🔄 已恢復原始設備數據")

def test_frontend_validation():
    """測試前端驗證邏輯"""
    print("\n=== 前端驗證邏輯測試 ===")
    
    # 模擬前端設備列表
    mock_devices = {
        'DEVICE_A': {'deviceId': 'DEVICE_A', 'lineName': 'LINE_1'},
        'DEVICE_B': {'deviceId': 'DEVICE_B', 'lineName': 'LINE_2'},
        'DEVICE_C': {'deviceId': 'DEVICE_C', 'lineName': 'LINE_3'}
    }
    
    # 測試重複檢查邏輯
    test_cases = [
        {
            'name': '添加新設備 - 不重複',
            'new_device_id': 'DEVICE_D',
            'is_editing': False,
            'original_device_id': None,
            'should_pass': True
        },
        {
            'name': '添加新設備 - 重複',
            'new_device_id': 'DEVICE_A',
            'is_editing': False,
            'original_device_id': None,
            'should_pass': False
        },
        {
            'name': '編輯設備 - ID不變',
            'new_device_id': 'DEVICE_A',
            'is_editing': True,
            'original_device_id': 'DEVICE_A',
            'should_pass': True
        },
        {
            'name': '編輯設備 - 修改為不重複ID',
            'new_device_id': 'DEVICE_A_NEW',
            'is_editing': True,
            'original_device_id': 'DEVICE_A',
            'should_pass': True
        },
        {
            'name': '編輯設備 - 修改為重複ID',
            'new_device_id': 'DEVICE_B',
            'is_editing': True,
            'original_device_id': 'DEVICE_A',
            'should_pass': False
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 測試: {test_case['name']}")
        
        new_device_id = test_case['new_device_id']
        is_editing = test_case['is_editing']
        original_device_id = test_case['original_device_id']
        
        # 模擬前端驗證邏輯
        if is_editing and new_device_id == original_device_id:
            # 設備ID沒有改變，不需要檢查重複
            is_duplicate = False
        else:
            # 檢查是否與其他設備重複
            is_duplicate = any(
                device['deviceId'] == new_device_id and device['deviceId'] != original_device_id
                for device in mock_devices.values()
            )
        
        should_pass = test_case['should_pass']
        actual_pass = not is_duplicate
        
        if actual_pass == should_pass:
            print(f"   ✅ 驗證正確: {'通過' if actual_pass else '阻止'}")
        else:
            print(f"   ❌ 驗證錯誤: 預期{'通過' if should_pass else '阻止'}，實際{'通過' if actual_pass else '阻止'}")

if __name__ == "__main__":
    print("開始測試設備ID編輯功能...\n")
    
    try:
        test_device_id_edit()
        test_frontend_validation()
        
        print("\n✅ 所有測試完成！")
        
        print("\n=== 手動驗證步驟 ===")
        print("請在瀏覽器中打開 http://127.0.0.1:5000 並執行以下測試:")
        print()
        print("🧪 測試1: 編輯設備ID")
        print("   1. 點擊任意設備卡片上的「編輯」按鈕")
        print("   2. 檢查「設備ID」輸入框是否可以輸入")
        print("   3. 修改設備ID為一個新的值")
        print("   4. 點擊保存，確認修改成功")
        print()
        print("🧪 測試2: 重複ID驗證")
        print("   1. 編輯一個設備，將設備ID修改為另一個已存在的設備ID")
        print("   2. 點擊保存，應該顯示錯誤信息阻止保存")
        print()
        print("🧪 測試3: 添加設備ID驗證")
        print("   1. 點擊「添加設備」按鈕")
        print("   2. 輸入一個已存在的設備ID")
        print("   3. 點擊保存，應該顯示錯誤信息阻止保存")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
