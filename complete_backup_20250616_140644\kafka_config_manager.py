import json
import os
from config import DEFAULT_KAFKA_CONFIG, KAFKA_CONFIG_FILE

class KafkaConfigManager:
    def __init__(self):
        self.config_file = KAFKA_CONFIG_FILE
        self.config = self.load_config()
    
    def load_config(self):
        """從文件加載Kafka配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 確保所有必需的字段都存在
                    for key, value in DEFAULT_KAFKA_CONFIG.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                print(f"加載Kafka配置失敗: {e}")
                return DEFAULT_KAFKA_CONFIG.copy()
        else:
            return DEFAULT_KAFKA_CONFIG.copy()
    
    def save_config(self, config):
        """保存Kafka配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            self.config = config
            return True
        except Exception as e:
            print(f"保存Kafka配置失敗: {e}")
            return False
    
    def get_config(self):
        """獲取當前配置"""
        return self.config.copy()
    
    def update_config(self, new_config):
        """更新配置"""
        # 驗證配置
        if self.validate_config(new_config):
            return self.save_config(new_config)
        return False
    
    def validate_config(self, config):
        """驗證配置的有效性"""
        required_fields = [
            'bootstrap_servers', 'group_id', 'factory', 
            'mfg_plant_code', 'message_type', 'message_name'
        ]
        
        for field in required_fields:
            if field not in config or not config[field]:
                return False
        
        # 驗證服務器地址格式
        servers = config['bootstrap_servers']
        if not isinstance(servers, str) or ':' not in servers:
            return False
        
        return True
    
    def generate_topic_patterns(self, lines):
        """根據線別生成主題模式列表"""
        topics = []
        for line in lines:
            topic = f"EAP.{self.config['factory']}.{self.config['mfg_plant_code']}.{line}.{self.config['message_type']}.{self.config['message_name']}"
            topics.append(topic)
        return topics
    
    def get_consumer_config(self):
        """獲取Kafka消費者配置"""
        return {
            'bootstrap_servers': [self.config['bootstrap_servers']],
            'group_id': self.config['group_id'],
            'auto_offset_reset': self.config.get('auto_offset_reset', 'latest'),
            'enable_auto_commit': self.config.get('enable_auto_commit', True),
            'value_deserializer': lambda x: json.loads(x.decode('utf-8'))
        }

# 全局配置管理器實例
kafka_config_manager = KafkaConfigManager()
