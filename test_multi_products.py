"""
測試多產品條碼顯示
"""

import json
import time
from datetime import datetime
from device_manager import <PERSON>ceManager
from kafka_monitor import KafkaMonitor
from config import TEST_DATA

class MultiProductTest:
    def __init__(self):
        self.device_manager = DeviceManager()
        self.kafka_monitor = KafkaMonitor(self.device_manager)
    
    def create_multi_product_message(self, products):
        """創建包含多個產品的Kafka消息"""
        test_message = TEST_DATA['sample_kafka_message'].copy()
        
        # 更新時間戳
        current_time = datetime.now().isoformat() + '+00:00'
        test_message['MessageTime'] = current_time
        test_message['Data']['ProcessedTime'] = current_time
        test_message['Data']['RawData']['TimeStamp'] = current_time
        
        # 更新產品數據
        units = []
        for i, (unit_id, status) in enumerate(products, 1):
            unit = {
                'UnitIdentifier': unit_id,
                'PositionNumber': i,
                'PositionName': f'{unit_id}_{i}',
                'X': 0.0,
                'Y': 0.0,
                'Rotation': 0.0,
                'FlipX': False,
                'FlipY': False,
                'Status': status
            }
            units.append(unit)
        
        test_message['Data']['RawData']['MessageBody']['Units'] = units
        test_message['Data']['RawData']['MessageBody']['UnitCount'] = len(units)
        
        return test_message
    
    def test_multi_products(self):
        """測試多產品處理"""
        print("🧪 測試多產品條碼顯示")
        print("="*60)
        
        # 測試案例：3個產品，2個Pass，1個Fail
        products = [
            ('A001', 'Pass'),
            ('A002', 'Fail'),
            ('A003', 'Pass')
        ]
        
        print(f"測試產品: {products}")
        
        # 創建多產品消息
        test_message = self.create_multi_product_message(products)
        
        # 模擬Kafka消息對象
        class MockMessage:
            def __init__(self, value, timestamp):
                self.value = value
                self.timestamp = timestamp * 1000
                self.topic = 'EAP.CZ.MAG.MAG-H71.DEVICE_CFX.CFX.Production.UnitsDeparted'
                self.partition = 0
                self.offset = 99999
        
        mock_message = MockMessage(test_message, time.time())
        
        # 處理消息
        self.kafka_monitor._process_message(mock_message)
        
        print("\n✅ 多產品測試完成")

if __name__ == '__main__':
    test = MultiProductTest()
    test.test_multi_products()
