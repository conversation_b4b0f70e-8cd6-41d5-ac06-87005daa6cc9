﻿# -*- coding: utf-8 -*-
"""
Created on 2022/11/23 10:34:10
@author: Administrator
"""

import matplotlib as mpl

mpl.use('Agg')
import tkinter as tk
from tkinter import *
import tkinter.messagebox
from PIL import Image, ImageTk
from copy import deepcopy
import pandas as pd
import matplotlib.pyplot as plt
import datetime
from matplotlib.font_manager import FontProperties
import os
from tkinter import ttk
import multiprocessing as mp
import requests
import configparser
import json
import hashlib
import threading
import time
import socket
import logging
import binascii
import serial
import traceback
import random
from paho.mqtt import client as mqtt_client
from pymongo import MongoClient

# 获取当前工作目录路径
if os.popen('uname -a').read().find('raspberrypi') != -1:
    work_address = '/home/<USER>'
elif os.popen('uname -a').read().find('simotech') != -1:
    work_address = '/home/<USER>'
else:
    work_address = '/home/<USER>'

# 检查是否存在log文件夹，不能存在则自动创建一个
if not os.path.exists(work_address + '/history/'):
    os.mkdir(work_address + '/history/')

# 检查是否存在log文件夹，不能存在则自动创建一个
if not os.path.exists(work_address + '/log/'):
    os.mkdir(work_address + '/log/')

# 初始化日志记录模块
logger = logging.getLogger(__name__)
logger.setLevel(level=logging.INFO)
handler = logging.FileHandler(work_address + '/log/log_' + str(datetime.datetime.now())[:10] + '.txt')
handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.info('PDA软件已开启!')

changmoflag = False

# 工号厂区登陆界面
class LoginWindow:
    def __init__(self, window):
        logger.info('进入工号厂区登录界面!')
        self.window = window
        self.window.attributes('-fullscreen', True)
        self.window.configure(background='white')
        # 读取历史设置数据
        self.config = configparser.ConfigParser()
        try:
            self.config.read(work_address + '/set.ini', encoding='utf-8-sig')
        except:
            self.config.read(work_address + '/set_backup.ini', encoding='utf-8-sig')
        self.usr_factory = self.config.get('mes_config', 'usr_factory')  # 用户厂区
        self.usr_id = self.config.get('mes_config', 'usr_id')  # 用户工号
        self.line = self.config.get('mes_config', 'line')  # 用户工号
        self.line_login = self.config.get('else_config', 'line_login')
        if self.line_login != '':
            self.line = self.line_login
        if self.line == '':
            self.line = 'ALL'
        self.auto_open = self.config.get('mode_config', 'auto_open')  # 是否开启无人模式，直接进入主界面连接PMM Table使用
        self.factory_list = self.config.get('mes_config', 'factory_list').replace(' ','').split(',')  # 所有厂区清单
        self.language = self.config.get('else_config', 'language')  # 语言
        self.screen_type = self.config.get('else_config', 'screen_type')  # 分辨率
        self.only_pqm = self.config.get('pqm_config', 'only_pqm')

    # 运行程序
    def run(self):
        # 显示界面
        self.show()
        # 若已用历史设置数据，则自动设置好
        if self.line != '':
            self.str_line.set(self.line)
        if self.usr_factory != '' and self.usr_id != '':
            try:
                index_factory = self.factory_list.index(self.usr_factory)
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
                pass
            else:
                self.cbx_factory.current(index_factory)
                self.str_id.set(self.usr_id)
                # 如果设置了程序无人模式，则直接进入下一个界面
                if not changmoflag:
                    if self.auto_open == '1' or self.only_pqm == '1':
                        self.usr_login()

    # 显示界面
    def show(self):
        # 加载背景图
        self.img_loginbackground = Image.open(work_address + '/login_' + self.language + '.png')
        self.img_loginbackground = ImageTk.PhotoImage(self.img_loginbackground)
        self.label_loginbackground = Label(self.window)
        self.label_loginbackground['image'] = self.img_loginbackground
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_loginbackground.place(x=770, y=374)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_loginbackground.place(x=420 - 100, y=320 - 100)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_loginbackground.place(x=323, y=222)
        else:
            self.label_loginbackground.place(x=420, y=320)

        # 定义下拉列表风格
        combostyle = tkinter.ttk.Style()
        combostyle.theme_create('combostyle', parent='alt', settings={'TCombobox':
            {'configure':
                {
                    'selectbackground': 'white',  # 选择后的背景颜色
                    'selectforeground': 'black',  # 选择后的背景颜色
                    'fieldbackground': 'white',  # 背景颜色
                    'background': 'white',  # 下拉框颜色
                }}})
        combostyle.theme_use('combostyle')

        # 加载厂区输入框
        self.cbx_factory = ttk.Combobox(self.window, font=('', 17), width=15, background='#FFFFFF')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.cbx_factory.place(x=537 + 350, y=413 + 54)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.cbx_factory.place(x=537 - 100, y=413 - 100)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.cbx_factory.place(x=537 - 98, y=413 - 98)
        else:
            self.cbx_factory.place(x=537, y=413)
        self.cbx_factory.configure(state='readonly')
        self.cbx_factory['values'] = self.factory_list

        # 加载工号输入框
        self.str_id = StringVar()
        self.str_id.set('')
        self.text_id = Entry(textvariable=self.str_id, font=('', 17), width=16, background='#FFFFFF')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.text_id.place(x=536 + 350, y=468 + 54)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.text_id.place(x=536 - 100, y=468 - 100)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.text_id.place(x=536 - 98, y=468 - 98)
        else:
            self.text_id.place(x=536, y=468)
        self.text_id.bind('<Return>', self.listen)  # 绑定回车，相当于点击登录

        # 加载线别输入框
        self.str_line = StringVar()
        self.str_line.set('')
        self.text_line = Entry(textvariable=self.str_line, font=('', 17), width=16, background='#FFFFFF')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.text_line.place(x=536 + 350, y=526 + 54)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.text_line.place(x=536 - 100, y=526 - 100)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.text_line.place(x=536 - 98, y=526 - 98)
        else:
            self.text_line.place(x=536, y=526)
        self.text_line.bind('<Return>', self.listen)  # 绑定回车，相当于点击登录

        # 加载确定按钮
        self.img_login = Image.open(work_address + '/next_' + self.language + '.png')
        self.img_login = ImageTk.PhotoImage(self.img_login)
        self.bt_login = tk.Button(self.window, relief='flat', width=87, height=41, bg='#FFFFFF',
                                  command=self.usr_login)
        self.bt_login['image'] = self.img_login
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.bt_login.place(x=641 + 350, y=588 + 54)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.bt_login.place(x=641 - 100, y=588 - 100)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.bt_login.place(x=641 - 98, y=588 - 98)
        else:
            self.bt_login.place(x=641, y=588)
        # 加载退出按钮
        self.img_quit = Image.open(work_address + '/close_' + self.language + '.png')
        self.img_quit = ImageTk.PhotoImage(self.img_quit)
        self.bt_quit = tk.Button(self.window, relief='flat', width=87, height=41, bg='#FFFFFF',
                                 command=self.usr_quit)
        self.bt_quit['image'] = self.img_quit
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.bt_quit.place(x=513 + 350, y=588 + 54)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.bt_quit.place(x=513 - 100, y=588 - 100)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.bt_quit.place(x=513 - 98, y=588 - 98)
        else:
            self.bt_quit.place(x=513, y=588)

        # 加载错误提示框
        self.str_err = StringVar()
        self.text_err = tk.Label(self.window, textvariable=self.str_err, bg='white', fg='red', font=('Arial', 10))
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.text_err.place(x=500 + 350, y=388 + 54)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.text_err.place(x=500 - 100, y=388 - 100)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.text_err.place(x=500 - 98, y=388 - 98)
        else:
            self.text_err.place(x=500, y=388)

        # 加载呼出键盘按钮
        self.img_keyboard = Image.open(work_address + '/keyboard.png')
        self.img_keyboard = ImageTk.PhotoImage(self.img_keyboard)
        self.bt_keyboard = tk.Button(self.window, relief='flat', width=20, height=20, bg='#FFFFFF',
                                     command=self.openkeyboard)
        self.bt_keyboard['image'] = self.img_keyboard
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.bt_keyboard.place(x=1920 - 20 - 5, y=0)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.bt_keyboard.place(x=1280 - 20 - 5 - 100, y=0)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.bt_keyboard.place(x=1280 - 20 - 5 - 98, y=0)
        else:
            # self.bt_keyboard.place(x=420, y=627)
            self.bt_keyboard.place(x=1280 - 20 - 5, y=0)

    # 虚拟键盘线程
    def threading_openkeyboard(self):
        os.system('onboard')

    # 开启虚拟键盘
    def openkeyboard(self):
        mythread_openkeyboard = threading.Thread(target=self.threading_openkeyboard, name='mainui', daemon=True)
        mythread_openkeyboard.start()

    # 自动关闭错误提示图
    def check_if_running(self, window):
        window.destroy()

    # 用于绑定回车按键
    def listen(self, event):
        self.usr_login()

    # 登录函数
    def usr_login(self):
        # 获取用户已输入的登录数据
        self.var_usr_factory = self.cbx_factory.get()
        self.var_usr_id = self.text_id.get()
        self.var_line = self.text_line.get()
        # 对各数据作逻辑分析，防止输入错误
        if self.var_usr_factory == '':
            self.text_err.place(x=535 - 35, y=373 + 15)
            # '厂区不能为空!'
            # Factory is empty!
            if self.language == 'en':
                self.str_err.set('Factory is empty!')
            else:
                self.str_err.set('厂区不能为空!')
        elif self.var_usr_id == '':
            self.text_err.place(x=535 - 35, y=373 + 15)
            # 工号不能为空!
            # ID is empty!
            if self.language == 'en':
                self.str_err.set('ID is empty!')
            else:
                self.str_err.set('工号不能为空!')
        elif self.var_line == '':
            self.text_err.place(x=535 - 35, y=373 + 15)
            # 线别不能为空!
            # Line is empty!
            if self.language == 'en':
                self.str_err.set('Line is empty!')
            else:
                self.str_err.set('线别不能为空!')
        else:
            # 保存登录数据到本地
            self.usr_factory = self.var_usr_factory
            self.usr_id = self.var_usr_id
            self.line = self.var_line
            self.config.set('mes_config', 'usr_factory', self.usr_factory)
            self.config.set('mes_config', 'usr_id', self.usr_id)
            if self.line != 'all' or self.line != 'ALL':
                self.config.set('mes_config', 'line', self.line)
            self.config.set('else_config', 'line_login', self.line)
            self.config.write(open(work_address + '/set.ini', 'w', encoding='utf-8-sig'))
            self.config.write(open(work_address + '/set_backup.ini', 'w', encoding='utf-8-sig'))
            # 关闭窗口，进入选取工单界面
            self.window.destroy()
            window = tk.Tk()
            modelclass = ModelWindow(window)
            modelclass.run()
            window.mainloop()

    # 退出的函数
    def usr_quit(self):
        self.window.destroy()

# 选取工单界面
class ModelWindow:
    def __init__(self, window):
        logger.info('进入选取工单界面!')
        self.window = window
        self.window.attributes('-fullscreen', True)
        # self.window.geometry('611x464+661+275')
        self.window.configure(background='white')

        # 读取设置数据
        self.config = configparser.ConfigParser()
        try:
            self.config.read(work_address + '/set.ini', encoding='utf-8-sig')
        except:
            self.config.read(work_address + '/set_backup.ini', encoding='utf-8-sig')
        self.usr_factory = self.config.get('mes_config', 'usr_factory')  # 用户厂区
        self.usr_id = self.config.get('mes_config', 'usr_id')  # 用户工号
        self.mo = self.config.get('mes_config', 'mo')  # 工单
        self.model = self.config.get('mes_config', 'model')  # 机种
        self.line = self.config.get('mes_config', 'line')  # 线别
        self.line_login = self.config.get('else_config', 'line_login')
        self.line_model = self.config.get('else_config', 'line_model')
        if self.line_model != '':
            self.line = self.line_model
        if self.line == '':
            self.line = 'all'
        self.section = self.config.get('mes_config', 'section')  # 段别
        self.group = self.config.get('mes_config', 'group')  # 组别
        self.station = self.config.get('mes_config', 'station')  # 站别
        self.youtiaoma = self.config.get('mes_config', 'youtiaoma')  # 是否为有条码
        self.feishouzhan = self.config.get('mes_config', 'feishouzhan')  # 是否为非首站
        self.url_mes = self.config.get('mes_config', 'url_mes')  # MES服务器

        self.pingbi_station = self.config.get('mes_config', 'pingbi_station').replace(' ', '').split(',')  # 屏蔽的站点

        self.chanliang = self.config.get('mode_config', 'chanliang')  # 单次产量，用于多胞胎设备
        if self.chanliang == '':
            self.chanliang = '1'
        else:
            self.chanliang = str(int(float(self.chanliang)))  # 单次触发产品数量，限制最多4胞胎，防止用户误设置成非常多导致程序紊乱
        self.snlength = self.config.get('mes_config', 'snlength')  # SN码长度
        self.snguding = self.config.get('mes_config', 'snguding')  # SN码固定码
        self.auto_open = self.config.get('mode_config', 'auto_open')  # 是否开启首次直接进入主界面
        self.language = self.config.get('else_config', 'language')  # 语言
        self.screen_type = self.config.get('else_config', 'screen_type')  # 分辨率
        self.only_pqm = self.config.get('pqm_config', 'only_pqm')

        # 定义MES通用变量
        self.secretkey = '894A0F0DF84A4799E0530CCA940AC604'
        self.tokenID = '894A0F0DF8494799E0530CCA940AC604'
        self.headers = {
            'tokenID': '894A0F0DF8494799E0530CCA940AC604',
            'Content-Type': 'application/json'
        }

        # 查询工单清单数据所需的变量
        self.url_worksheet = 'http://' + self.url_mes + ':10101/QueryData/MOList'
        self.params_worksheet = {
            'FACTORY': '',
            'GETDATA_TYPE': '',
            'MO_TYPE': '',
            'EMP_NO': '',
            'LINE_NAME': '',
            'sign': ''
        }
        self.data_worksheet = ''
        # 查询线段组站数据所需的变量
        self.url_lineinfo = 'http://' + self.url_mes + ':10101/QueryData/LineInfo'
        self.params_lineinfo = {
            'FACTORY': '',
            'LINE_NAME': '',
            'EMP_NO': '',
            'sign': ''
        }
        self.data_lineinfo = ''

        # 定义程序中途变量
        self.mo_list = []  # 存放工单清单
        self.model_list = []  # 存放机种清单
        self.line_list = []  # 存放线别清单
        self.lsgs_list = []  # 存放线段组站清单
        self.section_list = []  # 存放段别清单
        self.group_list = []  # 存放组别清单
        self.station_list = []  # 存放站别清单
        self.all_line_list = []
        self.all_section_list = []
        self.all_group_list = []
        self.all_station_list = []
        if self.language == 'en':
            self.feishouzhan_list = ['Yes', 'No']  # 存放首站\非首站选取列表清单
        else:
            self.feishouzhan_list = ['首站投入', '非首站投入']
        self.chanliang_list = [str(i + 1) for i in range(100)]  # 存放单次产量选取列表清单
        if self.language == 'en':
            self.youtiaoma_list = ['No', 'Yes']  # 存放有\无条码选取列表清单
        else:
            self.youtiaoma_list = ['无条码模式', '有条码模式']

    # 显示界面
    def show(self):
        # 否则只检索上次使用的线别上的工单
        logger.info('尝试获取' + str(self.line) + '工单数据!')

        # 加载背景图
        self.img_modelbackground = Image.open(work_address + '/model_update_' + self.language + '.png')
        self.img_modelbackground = ImageTk.PhotoImage(self.img_modelbackground)
        self.label_modelbackground = Label(self.window)
        self.label_modelbackground['image'] = self.img_modelbackground
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_modelbackground.place(x=662, y=185)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_modelbackground.place(x=341 - 100, y=185 - 155)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_modelbackground.place(x=214, y=31)
        else:
            self.label_modelbackground.place(x=341, y=150)

        # 用户工单检索输入框
        self.str_mocheck = StringVar()
        self.str_mocheck.set('')
        self.mocheck_model = Entry(self.window, textvariable=self.str_mocheck, font=('Arial', 15), width=30,
                                   fg='#000000', bg='#FFFFFF')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.mocheck_model.place(x=452 + 321, y=248 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.mocheck_model.place(x=452 - 100, y=248 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.mocheck_model.place(x=452 - 127, y=248 - 119)
        else:
            self.mocheck_model.place(x=452, y=248)
        self.mocheck_model.focus()
        self.mocheck_model.bind('<Return>', self.check_mo_enter)

        # 点击搜索工单按钮
        if self.language == 'en':
            self.bt_allmo = tk.Button(self.window, text='Click', bg='#FFFFFF', relief='flat', font=('', 11), width=12,
                                      height=1, command=self.check_mo)
        else:
            self.bt_allmo = tk.Button(self.window, text='搜索', bg='#FFFFFF', relief='flat', font=('', 11), width=12,
                                      height=1, command=self.check_mo)
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.bt_allmo.place(x=783 + 321, y=247 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.bt_allmo.place(x=783 - 100, y=247 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.bt_allmo.place(x=783 - 127, y=247 - 119)
        else:
            self.bt_allmo.place(x=783, y=247)
        # 定义所有下拉列表的风格
        self.combostyle = tkinter.ttk.Style()
        self.combostyle.theme_create('combostyle', parent='alt', settings={'TCombobox':
            {
                'configure':
                    {
                        # 'foreground': 'black',
                        'selectbackground': 'white',  # 选择后的背景颜色
                        'selectforeground': 'black',  # 选择后的背景颜色
                        'fieldbackground': 'white',  # 背景颜色
                        'background': 'white',  # 下拉框颜色
                        # 'font': 10,  # 字体大小
                        # 'font-weight': 'bold'
                    }}})
        self.combostyle.theme_use('combostyle')

        # 工单清单列表
        self.cbx_mo = ttk.Combobox(self.window, font=('', 12), width=15, height=10)
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.cbx_mo.place(x=430 + 321, y=299 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.cbx_mo.place(x=430 - 100, y=299 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.cbx_mo.place(x=430 - 127, y=299 - 119)
        else:
            self.cbx_mo.place(x=430, y=299)
        self.cbx_mo.configure(state='readonly')
        self.cbx_mo['values'] = self.mo_list
        self.cbx_mo.bind('<<ComboboxSelected>>', self.select_mo)

        # 机种清单列表
        self.cbx_model = ttk.Combobox(self.window, font=('', 12), width=15, height=10)
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.cbx_model.place(x=740 + 321, y=299 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.cbx_model.place(x=740 - 100, y=299 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.cbx_model.place(x=740 - 127, y=299 - 119)
        else:
            self.cbx_model.place(x=740, y=299)
        self.cbx_model.configure(state='disable')
        self.cbx_model['values'] = []

        # 线别清单列表
        self.cbx_line = ttk.Combobox(self.window, font=('', 12), width=15, height=10)
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.cbx_line.place(x=430 + 321, y=339 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.cbx_line.place(x=430 - 100, y=339 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.cbx_line.place(x=430 - 127, y=339 - 119)
        else:
            self.cbx_line.place(x=430, y=339)
        self.cbx_line.configure(state='readonly')
        self.cbx_line['values'] = []
        self.cbx_line.bind('<<ComboboxSelected>>', self.select_line)

        # 段别清单列表
        self.cbx_section = ttk.Combobox(self.window, font=('', 12), width=15, height=10)
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.cbx_section.place(x=740 + 321, y=339 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.cbx_section.place(x=740 - 100, y=339 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.cbx_section.place(x=740 - 127, y=339 - 119)
        else:
            self.cbx_section.place(x=740, y=339)
        self.cbx_section.configure(state='readonly')
        self.cbx_section['values'] = self.section_list
        self.cbx_section.bind('<<ComboboxSelected>>', self.select_section)

        # 组别清单列表
        self.cbx_group = ttk.Combobox(self.window, font=('', 12), width=15, height=10)
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.cbx_group.place(x=430 + 321, y=379 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.cbx_group.place(x=430 - 100, y=379 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.cbx_group.place(x=430 - 127, y=379 - 119)
        else:
            self.cbx_group.place(x=430, y=379)
        self.cbx_group.configure(state='readonly')
        self.cbx_group['values'] = self.group_list
        self.cbx_group.bind('<<ComboboxSelected>>', self.select_group)

        # 站别清单列表
        self.cbx_station = ttk.Combobox(self.window, font=('', 12), width=15, height=10)
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.cbx_station.place(x=740 + 321, y=379 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.cbx_station.place(x=740 - 100, y=379 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.cbx_station.place(x=740 - 127, y=379 - 119)
        else:
            self.cbx_station.place(x=740, y=379)
        self.cbx_station.configure(state='readonly')
        self.cbx_station['values'] = self.station_list

        # 有条码/无条码清单列表
        if self.language == 'en':
            self.cbx_youtiaoma = ttk.Combobox(self.window, font=('', 12), width=12 - 4, height=10)
        else:
            self.cbx_youtiaoma = ttk.Combobox(self.window, font=('', 12), width=12 - 2, height=10)
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.cbx_youtiaoma.place(x=451 + 321, y=459 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.cbx_youtiaoma.place(x=451 - 100, y=459 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.cbx_youtiaoma.place(x=451 - 127, y=459 - 119)
        else:
            self.cbx_youtiaoma.place(x=451, y=459)
        self.cbx_youtiaoma.configure(state='readonly')
        self.cbx_youtiaoma['values'] = self.youtiaoma_list
        self.cbx_youtiaoma.bind('<<ComboboxSelected>>', self.select_youtiaoma)

        # SN条码长度输入框
        self.str_snlength = StringVar()
        self.str_snlength.set(str(self.snlength))
        self.text_snlength = Entry(self.window, textvariable=self.str_snlength, font=('', 12), width=6 - 2,
                                   fg='#000000', bg='#FFFFFF')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.text_snlength.place(x=639 + 321, y=459 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.text_snlength.place(x=639 - 100, y=459 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.text_snlength.place(x=639 - 127, y=459 - 119)
        else:
            self.text_snlength.place(x=639, y=459)
        # SN条码固定码输入框
        self.str_snguding = StringVar()
        self.str_snguding.set(str(self.snguding))
        self.text_snguding = Entry(self.window, textvariable=self.str_snguding, font=('', 12), width=12, fg='#000000',
                                   bg='#FFFFFF')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.text_snguding.place(x=775 + 321, y=459 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.text_snguding.place(x=775 - 100, y=459 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.text_snguding.place(x=775 - 127, y=459 - 119)
        else:
            self.text_snguding.place(x=775, y=459)
        # 首站/非首站清单列表
        if self.language == 'en':
            self.cbx_feishouzhan = ttk.Combobox(self.window, font=('', 12), width=12 - 4, height=10)
        else:
            self.cbx_feishouzhan = ttk.Combobox(self.window, font=('', 12), width=12 - 2, height=10)
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.cbx_feishouzhan.place(x=451 + 321, y=499 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.cbx_feishouzhan.place(x=451 - 100, y=499 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.cbx_feishouzhan.place(x=451 - 127, y=499 - 119)
        else:
            self.cbx_feishouzhan.place(x=451, y=499)
        self.cbx_feishouzhan.configure(state='readonly')
        self.cbx_feishouzhan['values'] = self.feishouzhan_list
        self.cbx_feishouzhan.bind('<<ComboboxSelected>>', self.select_feishouzhan)

        # 单次触发产品数量输入框
        self.cbx_chanliang = ttk.Combobox(self.window, font=('', 12), width=12 - 8, height=10)
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.cbx_chanliang.place(x=639 + 321, y=499 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.cbx_chanliang.place(x=639 - 100, y=499 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.cbx_chanliang.place(x=639 - 127, y=499 - 119)
        else:
            self.cbx_chanliang.place(x=639, y=499)
        self.cbx_chanliang.configure(state='readonly')
        self.cbx_chanliang['values'] = self.chanliang_list

        # 以线别搜索排程工单数据的输入框
        self.str_linetomo = StringVar()
        self.str_linetomo.set('')
        self.text_linetomo = Entry(self.window, textvariable=self.str_linetomo, font=('', 14), width=10, fg='#000000',
                                   bg='#FFFFFF')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.text_linetomo.place(x=541 + 321, y=787 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.text_linetomo.place(x=541 - 100, y=787 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.text_linetomo.place(x=541 - 127, y=787 - 119)
        else:
            self.text_linetomo.place(x=541, y=787)
        # 刷新工单数据按钮
        if self.language == 'en':
            self.bt_lintomo = tk.Button(self.window, relief='flat', bg='#FFFFFF', text='Click', font=('', 10), width=9,
                                        height=1, command=self.flash_mo_line)
        else:
            self.bt_lintomo = tk.Button(self.window, relief='flat', bg='#FFFFFF', text='刷新', font=('', 10), width=8,
                                        height=1, command=self.flash_mo_line)
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.bt_lintomo.place(x=636 + 321, y=786 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.bt_lintomo.place(x=636 - 100, y=786 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.bt_lintomo.place(x=636 - 127, y=786 - 119)
        else:
            self.bt_lintomo.place(x=636, y=786)
        # 搜索全部默认工单数据的输入框
        self.str_allmo = StringVar()
        self.str_allmo.set('ALL')
        self.text_allmo = Entry(self.window, textvariable=self.str_allmo, font=('', 14), width=10, fg='#000000',
                                bg='#FFFFFF', state='readonly')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.text_allmo.place(x=541 + 321, y=820 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.text_allmo.place(x=541 - 100, y=820 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.text_allmo.place(x=541 - 127, y=820 - 119)
        else:
            self.text_allmo.place(x=541, y=820)
        # 刷新工单数据按钮
        if self.language == 'en':
            self.bt_allmo = tk.Button(self.window, relief='flat', bg='#FFFFFF', text='Click', font=('', 10), width=9,
                                      height=1, command=self.flash_mo_all)
        else:
            self.bt_allmo = tk.Button(self.window, relief='flat', bg='#FFFFFF', text='刷新', font=('', 10), width=8,
                                      height=1, command=self.flash_mo_all)
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.bt_allmo.place(x=636 + 321, y=819 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.bt_allmo.place(x=636 - 100, y=819 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.bt_allmo.place(x=636 - 127, y=819 - 119)
        else:
            self.bt_allmo.place(x=636, y=819)
        # 返回按钮
        self.img_back = Image.open(work_address + '/back_' + self.language + '.png')
        self.img_back = ImageTk.PhotoImage(self.img_back)
        self.bt_back = tk.Button(self.window, relief='flat', width=81, height=41, bg='#FFFFFF', command=self.goback)
        self.bt_back['image'] = self.img_back
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.bt_back.place(x=740 + 321, y=793 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.bt_back.place(x=740 - 100, y=793 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.bt_back.place(x=740 - 127, y=793 - 119)
        else:
            self.bt_back.place(x=740, y=793)
        # 确定按钮
        self.img_next = Image.open(work_address + '/next_' + self.language + '.png')
        self.img_next = ImageTk.PhotoImage(self.img_next)
        self.bt_next = tk.Button(self.window, relief='flat', width=81, height=41, bg='#FFFFFF', command=self.get_done)
        self.bt_next['image'] = self.img_next
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.bt_next.place(x=830 + 321, y=793 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.bt_next.place(x=830 - 100, y=793 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.bt_next.place(x=830 - 127, y=793 - 119)
        else:
            self.bt_next.place(x=830, y=793)
        # 自动更新相关设定
        self.var_auto = tk.IntVar()
        self.var_just = tk.IntVar()
        self.chb_auto = tk.Checkbutton(self.window, text='是否开启自动更新', onvalue=1, offvalue=0,
                                       variable=self.var_auto, bg='#FFFFFF', foreground='#000000', state='disabled')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.chb_auto.place(x=585 + 321, y=598 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.chb_auto.place(x=585 - 100, y=598 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.chb_auto.place(x=585 - 127, y=598 - 119)
        else:
            self.chb_auto.place(x=585, y=598)
        self.chb_just = tk.Checkbutton(self.window, text='仅更新检查码清单，不更新程序', onvalue=1,
                                       offvalue=0, variable=self.var_just, bg='#FFFFFF', foreground='#000000',
                                       state='disabled')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.chb_just.place(x=585 + 321, y=630 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.chb_just.place(x=585 - 100, y=630 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.chb_just.place(x=585 - 127, y=630 - 119)
        else:
            self.chb_just.place(x=585, y=630)
        self.var_io = tk.IntVar()
        self.rdo_self = tk.Radiobutton(self.window, text='模组内部IO采集模式', variable=self.var_io, value=1,
                                       bg='#FFFFFF', foreground='#000000', state='disabled')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.rdo_self.place(x=405 + 321, y=598 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.rdo_self.place(x=405 - 100, y=598 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.rdo_self.place(x=405 - 127, y=598 - 119)
        else:
            self.rdo_self.place(x=405, y=598)
        self.rdo_usb = tk.Radiobutton(self.window, text='USB-IO采集模式', variable=self.var_io, value=2, bg='#FFFFFF',
                                      foreground='#000000', state='disabled')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.rdo_usb.place(x=405 + 321, y=630 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.rdo_usb.place(x=405 - 100, y=630 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.rdo_usb.place(x=405 - 127, y=630 - 119)
        else:
            self.rdo_usb.place(x=405, y=630)
        # 显示MES文件更新服务器当前连接状态
        self.str_update_statu = StringVar()
        self.str_update_statu.set('')
        self.label_update_statu = Label(self.window, textvariable=self.str_update_statu, font=('', 11), width=12,
                                        fg='#000000',
                                        bg='#FFFFFF',
                                        anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_update_statu.place(x=473 + 321, y=681 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_update_statu.place(x=473 - 100, y=681 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_update_statu.place(x=473 - 127, y=681 - 119)
        else:
            self.label_update_statu.place(x=473, y=681)
        # 显示本地版本号
        self.str_update_nowlvl = StringVar()
        self.str_update_nowlvl.set('')
        self.label_update_nowlvl = Label(self.window, textvariable=self.str_update_nowlvl, font=('', 11), width=14,
                                         fg='#000000',
                                         bg='#FFFFFF',
                                         anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_update_nowlvl.place(x=640 + 321, y=681 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_update_nowlvl.place(x=640 - 100, y=681 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_update_nowlvl.place(x=640 - 127, y=681 - 119)
        else:
            self.label_update_nowlvl.place(x=640, y=681)
        # 显示云端版本号
        self.str_update_newlvl = StringVar()
        self.str_update_newlvl.set('')
        self.label_update_newlvl = Label(self.window, textvariable=self.str_update_newlvl, font=('', 11), width=14,
                                         fg='#000000',
                                         bg='#FFFFFF',
                                         anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_update_newlvl.place(x=820 + 321, y=681 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_update_newlvl.place(x=820 - 100, y=681 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_update_newlvl.place(x=820 - 127, y=681 - 119)
        else:
            self.label_update_newlvl.place(x=820, y=681)
        # 显示上次更新时间
        self.str_update_time = StringVar()
        self.str_update_time.set('')
        self.label_update_time = Label(self.window, textvariable=self.str_update_time, font=('', 11), width=13,
                                       fg='#000000',
                                       bg='#FFFFFF',
                                       anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_update_time.place(x=473 + 321, y=711 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_update_time.place(x=473 - 100, y=711 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_update_time.place(x=473 - 127, y=711 - 119)
        else:
            self.label_update_time.place(x=473, y=711)
        # 显示更新耗时
        self.str_update_usetime = StringVar()
        self.str_update_usetime.set('')
        self.label_update_usetime = Label(self.window, textvariable=self.str_update_usetime, font=('', 11), width=12,
                                          fg='#000000',
                                          bg='#FFFFFF',
                                          anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_update_usetime.place(x=635 + 321, y=711 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_update_usetime.place(x=635 - 100, y=711 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_update_usetime.place(x=635 - 127, y=711 - 119)
        else:
            self.label_update_usetime.place(x=635, y=711)
        # 手动更新按钮
        self.bt_update = tk.Button(self.window, relief='flat', bg='#FFFFFF', text='手动刷新', font=('', 10), width=9,
                                   height=1,
                                   command=self.update_ver, state='disabled')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.bt_update.place(x=718 + 321, y=593 + 35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.bt_update.place(x=718 - 100, y=593 - 155 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.bt_update.place(x=718 - 127, y=593 - 119)
        else:
            self.bt_update.place(x=718, y=593)

        # 加载呼出键盘按钮
        self.img_keyboard = Image.open(work_address + '/keyboard.png')
        self.img_keyboard = ImageTk.PhotoImage(self.img_keyboard)
        self.bt_keyboard = tk.Button(self.window, relief='flat', width=20, height=20, bg='#FFFFFF',
                                     command=self.openkeyboard)
        self.bt_keyboard['image'] = self.img_keyboard
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.bt_keyboard.place(x=1920 - 20 - 5, y=0)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.bt_keyboard.place(x=1280 - 20 - 5 - 100, y=0 + 35)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.bt_keyboard.place(x=1280 - 20 - 5 - 127, y=0)
        else:
            # self.bt_keyboard.place(x=420, y=627)
            self.bt_keyboard.place(x=1280 - 20 - 5, y=0)

    def threading_openkeyboard(self):
        os.system('onboard')

    def openkeyboard(self):
        mythread_openkeyboard = threading.Thread(target=self.threading_openkeyboard, name='mainui', daemon=True)
        mythread_openkeyboard.start()

    # 运行程序
    def run(self):
        if not changmoflag:
            if self.auto_open == '1' or self.only_pqm == '1':
                logger.info('自启动,线段组站已设置,自动进入主界面!')
                if self.mo == '' or self.model == '':
                    self.mo = '******'
                    self.model = '******'
                self.config.set('mes_config', 'mo', self.mo)
                self.config.set('mes_config', 'model', self.model)
                self.config.write(open(work_address + '/set.ini', 'w', encoding='utf-8-sig'))
                self.config.write(open(work_address + '/set_backup.ini', 'w', encoding='utf-8-sig'))
                self.window.destroy()
                window = tk.Tk()
                mainclass = MainWindow(window)
                mainclass.run()
                window.mainloop()
        # 显示界面
        self.show()
        # 开始运行
        print('尝试获取' + str(self.line) + '工单数据!')
        self.mo_list, self.model_list, self.line_list = self.getmo(self.usr_id)
        self.all_line_list, self.all_section_list, self.all_group_list, self.all_station_list = self.get_all_line(
            self.usr_id)

        # 自动帮忙选定历史工单
        # 假如工单清单为空, 则报错
        if self.mo_list == [] or self.model_list == [] or self.line_list == [] or self.all_line_list == [] or self.all_section_list == [] or self.all_group_list == [] or self.all_station_list == []:
            logger.error('获取MES工单失败!')
            print('获取MES工单失败!')
            try:
                self.messagewindow = tk.Toplevel(self.window)
                self.messagewindow.overrideredirect(True)
                self.messagewindow.geometry('+450+474')
                if self.language == 'en':
                    msg = tk.Message(self.messagewindow, text='Has not any MO in MES!',
                                     font=('Arial', 25), fg='white',
                                     bg='red',
                                     width=400)
                else:
                    msg = tk.Message(self.messagewindow, text='获取MES工单失败!',
                                     font=('Arial', 25), fg='white',
                                     bg='red',
                                     width=400)
                msg.pack()
                self.messagewindow.after(3000, self.check_if_running, self.messagewindow)
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
        else:
            self.cbx_mo['values'] = self.mo_list
            self.cbx_model['values'] = self.model_list
            self.cbx_line['values'] = sorted(list(set(self.all_line_list)))
            self.cbx_section['values'] = sorted(list(set(self.all_section_list)))
            self.cbx_group['values'] = sorted(list(set(self.all_group_list)))
            self.cbx_station['values'] = sorted(list(set(self.all_station_list)))
            if self.mo in self.cbx_mo['values'] and self.model in self.cbx_model['values']:
                self.cbx_mo.set(self.mo)
                self.cbx_model.set(self.model)
            if self.line in self.cbx_line['values'] and self.section in self.cbx_section['values'] and self.group in \
                    self.cbx_group['values'] and self.station in self.cbx_station['values']:
                self.cbx_line.set(self.line)
                self.cbx_section.set(self.section)
                self.cbx_group.set(self.group)
                self.cbx_station.set(self.station)
            self.cbx_feishouzhan.current(int(self.feishouzhan))
            self.cbx_youtiaoma.current(int(self.youtiaoma))

            self.cbx_chanliang.current(int(self.chanliang) - 1)

    # md5加密，MES交互使用
    def keymd5(self, src):
        str_md5 = hashlib.md5(src.encode('utf-8')).hexdigest()
        str_md5 = str_md5.upper()
        return str_md5

    # MES接口：提供厂区工号线别，获取该用户工单/机种/线别信息
    def worksheet_check(self):
        src = self.secretkey + 'EMP_NO' + self.params_worksheet['EMP_NO'] + 'FACTORY' + self.params_worksheet[
            'FACTORY'] + 'GETDATA_TYPE' + self.params_worksheet['GETDATA_TYPE'] + 'LINE_NAME' + self.params_worksheet[
                  'LINE_NAME'] + 'MO_TYPE' + self.params_worksheet['MO_TYPE']
        md5 = self.keymd5(src)
        self.params_worksheet['sign'] = md5
        print(self.url_worksheet)
        print(self.data_worksheet)
        print(self.headers)
        print(self.params_worksheet)
        logger.info('url_routing=%s\ndata_worksheet=%s\nheaders=%s\nparams_worksheet=%s' % (
            self.url_worksheet, self.data_worksheet, self.headers, self.params_worksheet))
        for i in range(5):
            try:
                receive = requests.request('GET', self.url_worksheet, data=self.data_worksheet, headers=self.headers,
                                           params=self.params_worksheet, timeout=20)
                try:
                    receive = json.loads(receive.text)
                except:
                    receive = eval(receive.text)
                print(receive)
                # print(receive['Result'])
                # print(receive['Message'])
                return receive
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
                time.sleep(2)
        return [{'result': 'FAIL', 'Message': 'MES发送错误!'}]

    # MES接口：提供厂区、线别，获取该线别所有段/组/站信息
    def checkline(self):
        src = self.secretkey + 'EMP_NO' + self.params_lineinfo['EMP_NO'] + 'FACTORY' + self.params_lineinfo[
            'FACTORY'] + 'LINE_NAME' + self.params_lineinfo['LINE_NAME']
        md5 = self.keymd5(src)
        self.params_lineinfo['sign'] = md5
        for i in range(5):
            try:
                print(self.url_lineinfo)
                print(self.data_lineinfo)
                print(self.headers)
                print(self.params_lineinfo)
                logger.info('url_lineinfo=%s\ndata_lineinfo=%s\nheaders=%s\nparams_lineinfo=%s' % (
                    self.url_lineinfo, self.data_lineinfo, self.headers, self.params_lineinfo))
                receive = requests.request('GET', self.url_lineinfo, data=self.data_lineinfo, headers=self.headers,
                                           params=self.params_lineinfo, timeout=20)
                try:
                    receive = json.loads(receive.text)
                except:
                    receive = eval(receive.text)
                print(receive)
                # print(receive['Result'])
                # print(receive['Message'])
                return receive
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
                time.sleep(2)
        return [{'result': 'FAIL', 'Message': 'MES发送错误!'}]

    def get_all_line(self, my_id):
        self.params_lineinfo['FACTORY'] = self.usr_factory
        self.params_lineinfo['EMP_NO'] = self.usr_id
        self.params_lineinfo['LINE_NAME'] = 'ALL'
        lineinfo_post = self.checkline()
        result = lineinfo_post['Result']
        lineinfo = lineinfo_post['Message']
        if result == 'OK':
            if len(lineinfo) == 0:
                print(self.usr_id + '查询ALL段组站为空!')
                return [], [], [], [0]
            else:
                self.all_line_list = []
                self.all_section_list = []
                self.all_group_list = []
                self.all_station_list = []
                for i in lineinfo:
                    self.all_line_list.append(i['LINE_NAME'])
                    self.all_section_list.append(i['SECTION_NAME'])
                    self.all_group_list.append(i['GROUP_NAME'])
                    self.all_station_list.append(i['STATION_NAME'])
                return self.all_line_list, self.all_section_list, self.all_group_list, self.all_station_list
        else:
            print(self.usr_id + '查询ALL段组站失败!')
            return [], [], [], [0]
        # return lineinfo

    # 根据用户填写的工号厂区，获取该用户工单/机种/线别信息
    def getmo(self, my_id):
        self.params_worksheet['FACTORY'] = self.usr_factory
        if self.line_login == 'all' or self.line_login == 'ALL' or self.line_login == '':
            self.params_worksheet['GETDATA_TYPE'] = '0'
            self.params_worksheet['EMP_NO'] = my_id
        else:
            self.params_worksheet['GETDATA_TYPE'] = '1'
            self.params_worksheet['EMP_NO'] = my_id + str(random.randint(1, 254))
        self.params_worksheet['LINE_NAME'] = self.line_login
        self.params_worksheet['MO_TYPE'] = '0'
        print('##########################')
        print(self.line + ',等待MES回传工单信息中。。。。。')
        print('##########################')
        worksheet_post = self.worksheet_check()
        result = worksheet_post['Result']
        worksheet = worksheet_post['Message']
        if result == 'OK':
            if len(worksheet) == 0:
                print(self.line + '查询工单为空!')
                return [], [], []
            else:
                print(str(worksheet))
                self.mo_list = []
                self.model_list = []
                self.line_list = []
                for i in worksheet:
                    self.mo_list.append(i['MO_NUMBER'])
                    self.model_list.append(i['MODEL_NAME'])
                    self.line_list.append(i['LINE_NAME'])
                return self.mo_list, self.model_list, self.line_list
        else:
            print(self.line + '查询工单失败!')
            return [], [], []

    # 用于绑定用户在工单列表内选定某个工单后的触发事件
    def select_mo(self, *args):
        self.mo = self.cbx_mo.get()
        num_mo = self.mo_list.index(self.mo)
        self.model = self.model_list[num_mo]
        self.cbx_model.current(self.cbx_model['values'].index(self.model))
        self.line = self.line_list[num_mo]
        if self.line_model != '':
            self.cbx_line.current(self.cbx_line['values'].index(self.line_model))
        else:
            self.cbx_line.current(self.cbx_line['values'].index(self.line))
        self.select_line()

    def select_line(self, *args):
        self.line = self.cbx_line.get()
        self.cbx_section.set('')
        self.cbx_group.set('')
        self.cbx_station.set('')
        self.section_list = []
        self.group_list = []
        self.station_list = []
        for i in range(len(self.all_line_list)):
            if self.all_line_list[i] == self.line:
                self.section_list.append(deepcopy(self.all_section_list[i]))
                self.group_list.append(deepcopy(self.all_group_list[i]))
                self.station_list.append(deepcopy(self.all_station_list[i]))
        self.cbx_section['values'] = sorted(list(set(self.section_list)))
        self.cbx_group['values'] = sorted(list(set(self.group_list)))
        self.cbx_station['values'] = sorted(list(set(self.station_list)))
        if self.section in self.cbx_section['values'] and self.group in self.cbx_group['values'] and self.station in \
                self.cbx_station['values']:
            self.cbx_section.set(self.section)
            self.cbx_group.set(self.group)
            self.cbx_station.set(self.station)
        self.select_section()

    def select_section(self, *args):
        self.section = self.cbx_section.get()
        self.cbx_group.set('')
        self.cbx_station.set('')
        self.group_list = []
        self.station_list = []
        for i in range(len(self.all_line_list)):
            if self.all_line_list[i] == self.line and self.all_section_list[i] == self.section:
                self.group_list.append(deepcopy(self.all_group_list[i]))
                self.station_list.append(deepcopy(self.all_station_list[i]))
        self.cbx_group['values'] = sorted(list(set(self.group_list)))
        self.cbx_station['values'] = sorted(list(set(self.station_list)))
        if self.group in self.cbx_group['values'] and self.station in self.cbx_station['values']:
            self.cbx_group.set(self.group)
            self.cbx_station.set(self.station)
        self.select_group()

    def select_group(self, *args):
        self.group = self.cbx_group.get()
        self.cbx_station.set('')
        self.station_list = []
        for i in range(len(self.all_line_list)):
            if self.all_line_list[i] == self.line and self.all_section_list[i] == self.section and self.all_group_list[
                i] == self.group:
                self.station_list.append(deepcopy(self.all_station_list[i]))
        self.cbx_station['values'] = sorted(list(set(self.station_list)))
        if self.station in self.cbx_station['values']:
            self.cbx_station.set(self.station)

    # 用于绑定用户在首站/非首站列表内选择后的触发事件
    def select_feishouzhan(self, *args):  # 处理事件，*args表示可变参数
        if self.cbx_feishouzhan.get() == 'Yes' or self.cbx_feishouzhan.get() == '首站投入':
            self.cbx_youtiaoma.current(1)
            self.cbx_youtiaoma.configure(state='disable')
            self.text_snlength.configure(state='normal')
            self.text_snguding.configure(state='normal')
            self.cbx_chanliang.current(0)
            self.cbx_chanliang.configure(state='disable')
        else:
            self.cbx_youtiaoma.configure(state='readonly')
            self.cbx_chanliang.configure(state='normal')
            now_youtiaoma = self.cbx_youtiaoma.get()
            if now_youtiaoma != '无条码模式':
                self.cbx_chanliang.current(0)
                self.cbx_chanliang.configure(state='disable')
                self.text_snguding.configure(state='normal')
                self.text_snlength.configure(state='normal')

    # 用于绑定用户在有条码/无条码列表内选择后的触发事件
    def select_youtiaoma(self, *args):  # 处理事件，*args表示可变参数
        if self.cbx_youtiaoma.get() == 'Yes' or self.cbx_youtiaoma.get() == '有条码模式':
            self.text_snlength.configure(state='normal')
            self.text_snguding.configure(state='normal')
            self.cbx_chanliang.current(0)
            self.cbx_chanliang.configure(state='disable')
            self.cbx_feishouzhan.configure(state='readonly')
        else:
            self.cbx_chanliang.configure(state='normal')
            self.cbx_chanliang.current(int(self.chanliang) - 1)
            self.cbx_feishouzhan.current(1)
            self.cbx_feishouzhan.configure(state='disable')

    # 用于用户点击“完成”按钮后的触发事件
    def get_done(self):
        self.mo = self.cbx_mo.get()
        self.model = self.cbx_model.get()
        self.line = self.cbx_line.get()
        self.section = self.cbx_section.get()
        self.group = self.cbx_group.get()
        self.station = self.cbx_station.get()
        if self.cbx_feishouzhan.get() == 'Yes' or self.cbx_feishouzhan.get() == '首站投入':
            self.feishouzhan = '0'
        else:
            self.feishouzhan = '1'
        if self.cbx_youtiaoma.get() == 'No' or self.cbx_youtiaoma.get() == '无条码模式':
            self.youtiaoma = '0'
        else:
            self.youtiaoma = '1'
        self.chanliang = self.cbx_chanliang.get()
        self.snlength = self.text_snlength.get()
        if self.snlength == '':
            self.snlength = '0'
        self.snguding = self.text_snguding.get()

        if self.mo == '' or self.model == '' or self.line == '' or self.section == '' or self.group == '' or self.station == '':
            tk.messagebox.showerror(message='工单信息不能为空')
        elif self.youtiaoma == '0' and self.feishouzhan == '0':
            tk.messagebox.showerror(message='无条码模式不允许作为首站投入')
        elif self.youtiaoma == '1' and not self.snlength.isdigit():
            tk.messagebox.showerror(message='有条码模式必须填写条码长度且长度大于0')
        elif self.youtiaoma == '1' and self.snlength.isdigit() and int(self.snlength) == 0:
            tk.messagebox.showerror(message='有条码模式必须填写条码长度且长度大于0')
        elif self.station in self.pingbi_station:
            tk.messagebox.showerror(message='该程式已被禁止选择此站点，请修改站点或联系厂区负责人!')
        else:
            # 修改配置文件的内容
            self.config.set('mes_config', 'mo', self.mo)
            self.config.set('mes_config', 'model', self.model)
            self.config.set('mes_config', 'line', self.line)
            self.config.set('mes_config', 'section', self.section)
            self.config.set('mes_config', 'group', self.group)
            self.config.set('mes_config', 'station', self.station)
            self.config.set('mes_config', 'feishouzhan', self.feishouzhan)
            self.config.set('mes_config', 'youtiaoma', self.youtiaoma)
            self.config.set('mode_config', 'chanliang', self.chanliang)
            self.config.set('mes_config', 'snlength', self.snlength)
            self.config.set('mes_config', 'snguding', self.snguding)
            self.config.set('else_config', 'line_model', self.line)
            self.config.write(open(work_address + '/set.ini', 'w', encoding='utf-8-sig'))
            self.config.write(open(work_address + '/set_backup.ini', 'w', encoding='utf-8-sig'))
            self.window.destroy()
            window = tk.Tk()
            mainclass = MainWindow(window)
            mainclass.run()
            window.mainloop()

    # 关闭错误提示图
    def check_if_running(self, window2):
        window2.destroy()
        self.window.destroy()
        window = tk.Tk()
        loginclass = LoginWindow(window)
        loginclass.run()
        window.mainloop()

    # 用于用户在工单列表内搜索工单
    def check_mo(self):
        checkmo = self.mocheck_model.get()
        if checkmo in self.cbx_mo['value']:
            self.mocheck_model.select_range(0, END)
            self.mocheck_model.icursor(0)
            self.cbx_mo.set(checkmo)
            self.select_mo()
        else:
            self.str_mocheck.set('清单内不存在该工单号！')
            self.mocheck_model.select_range(0, END)
            self.mocheck_model.icursor(0)

    # 与check_mo一样的功能，用于绑定回车按钮
    def check_mo_enter(self, event):
        checkmo = self.mocheck_model.get()
        if checkmo in self.cbx_mo['value']:
            self.mocheck_model.select_range(0, END)
            self.mocheck_model.icursor(0)
            self.cbx_mo.set(checkmo)
            self.select_mo()
        else:
            self.str_mocheck.set('清单内不存在该工单号！')
            self.mocheck_model.select_range(0, END)
            self.mocheck_model.icursor(0)

    # 用于用户点击“获取该用户所有工单信息”按钮
    def flash_mo_all(self):
        self.line = 'all'
        self.config.set('mes_config', 'line', self.line)
        self.config.set('else_config', 'line_login', self.line)
        self.config.write(open(work_address + '/set.ini', 'w', encoding='utf-8-sig'))
        self.config.write(open(work_address + '/set_backup.ini', 'w', encoding='utf-8-sig'))
        self.window.destroy()
        window = tk.Tk()
        modelclass = ModelWindow(window)
        modelclass.run()
        window.mainloop()

    # 用于用户点击“以线别获取排程上的工单信息”按钮
    def flash_mo_line(self):
        self.line = self.text_linetomo.get()
        self.config.set('mes_config', 'line', self.line)
        self.config.set('else_config', 'line_login', self.line)
        self.config.write(open(work_address + '/set.ini', 'w', encoding='utf-8-sig'))
        self.config.write(open(work_address + '/set_backup.ini', 'w', encoding='utf-8-sig'))
        self.window.destroy()
        window = tk.Tk()
        modelclass = ModelWindow(window)
        modelclass.run()
        window.mainloop()

    # 用于用户点击“返回”按钮
    def goback(self):
        self.window.destroy()
        window = tk.Tk()
        loginclass = LoginWindow(window)
        loginclass.run()
        window.mainloop()

    # 手动更新按钮，暂未完成该功能
    def update_ver(self):
        pass

# 主程序界面
class MainWindow:
    def __init__(self, window):
        logger.info('进入主界面!')
        self.window = window

        self.setini_lock = threading.Lock()

        # 载入设置信息
        self.config = configparser.ConfigParser()
        try:
            self.config.read(work_address + '/set.ini', encoding='utf-8-sig')
        except:
            self.config.read(work_address + '/set_backup.ini', encoding='utf-8-sig')
        self.usr_factory = self.config.get('mes_config', 'usr_factory')  # 用户厂区
        self.usr_id = self.config.get('mes_config', 'usr_id')  # 用户工号
        self.mo = self.config.get('mes_config', 'mo')  # 工单
        self.model = self.config.get('mes_config', 'model')  # 机种
        self.line = self.config.get('mes_config', 'line')  # 线别
        self.section = self.config.get('mes_config', 'section')  # 段别
        self.group = self.config.get('mes_config', 'group')  # 组别
        self.station = self.config.get('mes_config', 'station')  # 站别
        self.youtiaoma = self.config.get('mes_config', 'youtiaoma')  # 是否为有条码
        self.feishouzhan = self.config.get('mes_config', 'feishouzhan')  # 是否为非首站
        self.url_mes = self.config.get('mes_config', 'url_mes')  # MES服务器
        if self.config.get('mode_config', 'chanliang') == '':
            self.chanliang = '1'
        else:
            self.chanliang = str(int(float(self.config.get('mode_config', 'chanliang'))))  # 单次触发产品数量
        self.snlength = self.config.get('mes_config', 'snlength')  # SN码长度
        self.snguding = self.config.get('mes_config', 'snguding')  # SN码固定码
        self.auto_reboot = self.config.get('else_config', 'auto_reboot')
        self.reboot_time = self.config.get('else_config', 'reboot_time')
        self.title_name = self.config.get('else_config', 'title_name')
        self.not_upload = self.config.get('else_config', 'not_upload')
        self.language = self.config.get('else_config', 'language')  # 语言
        self.io_in_type = self.config.get('else_config', 'io_in_type')  # 触发方式
        self.io_out_type = self.config.get('else_config', 'io_out_type')  # 输出信号方式
        self.gpio_in_com_old = self.config.get('else_config', 'gpio_in_com_old')  # 输入GPIO的端口号
        self.screen_type = self.config.get('else_config', 'screen_type')  # 分辨率
        self.url_pqm = self.config.get('pqm_config', 'url_pqm')
        self.shebei_bianma = self.config.get('pqm_config', 'shebei_bianma').replace(' ', '').split(',')
        self.only_pqm = self.config.get('pqm_config', 'only_pqm')
        self.hold_pcs_time = self.config.get('pqm_config', 'hold_pcs_time')
        self.hold_pcs_time = 0 if self.hold_pcs_time == '' else int(self.hold_pcs_time)
        self.hold_max_time = self.config.get('pqm_config', 'hold_max_time')
        self.hold_max_time = 0 if self.hold_max_time == '' else int(self.hold_max_time)

        self.cttimeset = self.config.get('pqm_config', 'cttimeset')
        if self.cttimeset == '':
            self.cttimeset = '5'

        self.cttime_same_type = self.config.get('pqm_config', 'cttime_same_type')
        self.cttimestart_com = self.config.get('pqm_config', 'cttimestart_com')
        self.cttimeend_com = self.config.get('pqm_config', 'cttimeend_com')

        # self.today_restart_time = self.config.get('pqm_config', 'today_restart_time')
        self.today_restart_time = self.config.get('pqm_config', 'today_restart_time').replace(' ', '').split(',')

        self.status = self.config.get('pqm_data', 'status').replace(' ','').split(',')
        self.statusCode = self.config.get('pqm_data', 'statusCode').replace(' ','').split(',')
        self.passQty = self.config.get('pqm_data', 'passQty').replace(' ','').split(',')
        self.failQty = self.config.get('pqm_data', 'failQty').replace(' ','').split(',')
        self.errorCnt = self.config.get('pqm_data', 'errorCnt').replace(' ','').split(',')
        self.errorTimes = self.config.get('pqm_data', 'errorTimes').replace(' ','').split(',')
        self.cycleTime = self.config.get('pqm_data', 'cycleTime').replace(' ','').split(',')
        self.runningTime = 0 if self.config.get('pqm_data', 'runningTime') == '' else float(
            self.config.get('pqm_data', 'runningTime'))
        self.waitingTime = self.config.get('pqm_data', 'waitingTime').replace(' ','').split(',')
        self.inputQty = self.config.get('pqm_data', 'inputQty').replace(' ','').split(',')


        self.gpio_in_com_new = self.config.get('else_config', 'gpio_in_com_new').replace(' ','').split(',')
        self.gpio_in_com_new = [] if self.gpio_in_com_new == [''] else [int(self.gpio_in_com_new[i]) for i in range(len(self.gpio_in_com_new))]

        for i in range(len(self.gpio_in_com_new)):
            if len(self.shebei_bianma) < self.gpio_in_com_new[i] + 1:
                logger.warning('检测到设备ID数量(%s)小于gpio_in_com_new所指定的位置数(%s+1)' % (len(self.shebei_bianma), self.gpio_in_com_new[i]))
                self.shebei_bianma = deepcopy([self.shebei_bianma[0] for x in range(self.gpio_in_com_new[i])])
                logger.info('将self.shebei_bianma修改为%s'%self.shebei_bianma)

        self.container_open = self.config.get('container_config', 'container_open')
        # self.mixLink = self.config.get('container_config', 'mixLink')
        self.side_name = self.config.get('container_config', 'side_name').replace(' ','').split(',')
        self.side_gpio = self.config.get('container_config', 'side_gpio').replace(' ','').split(',')
        self.side_gpio = [] if self.side_gpio == [''] else [int(self.side_gpio[i]) for i in range(len(self.side_gpio))]
        self.side_number = self.config.get('container_config', 'side_number').replace(' ','').split(',')
        self.side_number = [] if self.side_number == [''] else [int(self.side_number[i]) for i in range(len(self.side_number))]
        self.side_now = self.config.get('container_config', 'side_now')

        self.only_sensor = self.config.get('container_config', 'only_sensor')
        self.EQP_CODE = self.config.get('container_config', 'EQP_CODE').replace(' ','').split(',')
        self.EQP_DESC = self.config.get('container_config', 'EQP_DESC').replace(' ','').split(',')
        self.EQP_PORT = self.config.get('container_config', 'EQP_PORT').replace(' ','').split(',')

        # self.container_NO = self.config.get('container_config', 'container_NO')


        self.light_open = self.config.get('light_config', 'light_open')
        self.gpio_green = int(self.config.get('light_config', 'gpio_green'))
        self.gpio_yellow = int(self.config.get('light_config', 'gpio_yellow'))
        self.gpio_red = int(self.config.get('light_config', 'gpio_red'))
        self.code_green = self.config.get('light_config', 'code_green')
        self.code_yellow = self.config.get('light_config', 'code_yellow')
        self.code_red = self.config.get('light_config', 'code_red')
        self.hold_green_time = self.config.get('light_config', 'hold_green_time')
        self.status_time_list = self.config.get('light_config', 'status_time_list').replace(' ', '').split(',')
        self.status_time_list = [0, 0, 0] if self.status_time_list == [''] else [int(self.status_time_list[i]) for i in range(len(self.status_time_list))]


        self.ctct_factory = self.usr_factory
        self.ctct_ip = self.config.get('mqtt_config', 'ctct_ip')  # mqtt代理服务器地址
        self.ctct_port = int(self.config.get('mqtt_config', 'ctct_port'))
        self.ctct_produce = self.config.get('mqtt_config', 'ctct_produce')
        if self.ctct_produce == '':
            self.ctct_produce = 'None'
        self.ctct_line = self.line
        self.ctct_deviceid = self.line + '}' + self.section + '}' + self.group + '}' + self.station
        self.ctct_station = self.station
        self.ctct_pcname = self.config.get('mqtt_config', 'ctct_pcname')
        self.ctct_clientid = 'PMM/' + self.usr_factory + '/' + self.ctct_produce + '/' + self.line + '/' + self.station + '(' + self.ctct_deviceid + ')'

        self.ctct_software = self.config.get('mqtt_config', 'ctct_software')
        self.ctct_version = self.config.get('mqtt_config', 'ctct_version')
        self.ctct_CPIUID = self.config.get('mqtt_config', 'ctct_CPIUID')
        self.ctct_selfcmd = 'Sys/Cmd/' + self.ctct_clientid
        self.ctct_selfreturn = 'Sys/Return/' + self.ctct_clientid
        self.ctct_linecmd = 'Sys/Cmd/CCS/' + self.ctct_factory + '/' + self.ctct_produce
        self.ctct_kanbancmd = 'Sys/Cmd/KanBan/' + self.ctct_factory + '/' + self.ctct_produce
        self.ctct_selflog = 'Sys/Log/' + self.ctct_clientid
        self.ctct_keepalive = 60  # 与代理通信之间允许的最长时间段（以秒为单位）

        self.db_ip = self.config.get('cfx_config', 'db_ip')
        self.db_port = int(self.config.get('cfx_config', 'db_port'))
        self.db_database_name = self.config.get('cfx_config', 'db_database_name')
        self.eqpType = self.config.get('cfx_config', 'eqpType')

        if len(self.status) != len(self.shebei_bianma):
            self.status = [0 for x in range(len(self.shebei_bianma))]
        if len(self.statusCode) != len(self.shebei_bianma):
            self.statusCode = ['' for x in range(len(self.shebei_bianma))]
        if len(self.passQty) != len(self.shebei_bianma):
            self.passQty = [0 for x in range(len(self.shebei_bianma))]
        if len(self.failQty) != len(self.shebei_bianma):
            self.failQty = [0 for x in range(len(self.shebei_bianma))]
        if len(self.errorCnt) != len(self.shebei_bianma):
            self.errorCnt = [0 for x in range(len(self.shebei_bianma))]
        if len(self.errorTimes) != len(self.shebei_bianma):
            self.errorTimes = [0 for x in range(len(self.shebei_bianma))]
        if len(self.waitingTime) != len(self.shebei_bianma):
            self.waitingTime = [0 for x in range(len(self.shebei_bianma))]
        if len(self.cycleTime) != len(self.shebei_bianma):
            self.cycleTime = [0 for x in range(len(self.shebei_bianma))]
        if len(self.inputQty) != len(self.shebei_bianma):
            self.inputQty = [0 for x in range(len(self.shebei_bianma))]

        for i in range(len(self.passQty)):
            self.passQty[i] = 0 if self.passQty[i] == '' else int(self.passQty[i])
        for i in range(len(self.failQty)):
            self.failQty[i] = 0 if self.failQty[i] == '' else int(self.failQty[i])
        for i in range(len(self.errorCnt)):
            self.errorCnt[i] = 0 if self.errorCnt[i] == '' else int(self.errorCnt[i])
        for i in range(len(self.errorTimes)):
            self.errorTimes[i] = 0 if self.errorTimes[i] == '' else float(self.errorTimes[i])
        for i in range(len(self.waitingTime)):
            self.waitingTime[i] = 0 if self.waitingTime[i] == '' else float(self.waitingTime[i])
        for i in range(len(self.cycleTime)):
            self.cycleTime[i] = 0 if self.cycleTime[i] == '' else float(self.cycleTime[i])
        for i in range(len(self.inputQty)):
            self.inputQty[i] = 0 if self.inputQty[i] == '' else int(self.inputQty[i])

        # MES通用变量
        self.secretkey = '894A0F0DF84A4799E0530CCA940AC604'
        self.tokenID = '894A0F0DF8494799E0530CCA940AC604'
        self.headers = {
            'tokenID': '894A0F0DF8494799E0530CCA940AC604',
            'Content-Type': 'application/json'
        }

        # routing变量
        self.testdata = []
        self.url_routing = 'http://' + self.url_mes + ':10101/TDC/DELTA_DEAL_TEST_DATA_I'
        self.params_routing = {
            'sign': ''
        }
        self.data_routing = {
            'factory': '',
            'testType': '',
            'routingData': '',
            'testData': []
        }
        # worksheet_check变量
        self.url_worksheet = 'http://' + self.url_mes + ':10101/QueryData/MOList'
        self.params_worksheet = {
            'FACTORY': '',
            'GETDATA_TYPE': '',
            'MO_TYPE': '',
            'EMP_NO': '',
            'LINE_NAME': '',
            'sign': ''
        }
        self.data_worksheet = ''

        # 定义需要的变量参数
        self.code = {
            'pro_code': '',
            'err_code': '',
            'err_code_mes': '',
            'err_meaning': '',
            'result': '',
            'updated': ''
        }
        self.result_xls = []
        self.history_xls = []
        self.history = {
            'time': '',
            'prodCode': '',
            'checkItem': '',
            'result': '',
            'Uploaded': '',
            'mo': '',
            'model': '',
            'line': '',
            'section': '',
            'group': '',
            'station': ''
        }
        self.image_name = []
        self.image_result = []
        self.image_number = []
        self.watercode = 0
        self.all_number = 0
        self.pass_number = 0
        self.err_number = 0
        self.procode = ''
        self.firstcode = ''
        self.secondcode = ''
        self.userIP = ''
        # 当前显示的图片，用于交替显示图片，防止闪屏
        self.now_pic = 0
        # 当前使用的USB采集器的COM端口号，用于识别出另一个是USB继电器
        self.now_usb = '0'
        # 当前USB采集器连接状态
        self.now_signal = ''
        self.pqm_starttime = time.time() - self.runningTime
        self.pqm_cttime_list = [0, 0, 0, 0]
        self.pqm_allnumber_list = self.inputQty
        self.pqm_hold_flag_list = [False, False, False, False]
        self.signal_list = []

        self.CheckState_returnCTCT_list = []
        self.ChangeLineResult_returnCTCT = {}
        self.ChangeModelSetResult_returnCTCT = {}
        self.lineData_list = []
        self.changeline_starttime = ''
        self.first_upload = False

        # 定义初始化装载箱状态
        self.side_status = [1 for i in range(len(self.side_gpio))]
        self.container_NO = ''


        # 定义报警灯状态
        self.light_status = 'green'
        # 当前显示的图片，用于交替显示图片，防止闪屏
        self.light_show_flag = 0

        # 定义连接使用的clientid_new
        self.clientid_new = deepcopy(self.ctct_clientid)

        # 读取模组当前IP地址
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(('*******', 80))
            self.userIP = s.getsockname()[0]
            s.close()
        except:
            message = traceback.format_exc()
            print(message)
            logger.error(message)
            print('系统开启时,初次获取自身ip失败,尝试直接使用set.ini里记录的ip!')

        # 载入检查项清单
        try:
            self.result_xls = pd.read_excel(work_address + '/result.xls')
            self.result_xls = self.result_xls.to_dict(orient='records')
        except:
            message = traceback.format_exc()
            print(message)
            # logger.error(message)
            logger.error('载入检查项文件result.xls失败，请检查文件是否存在或数据损坏!\n%s'%message)
            try:
                self.messagewindow = tk.Toplevel(self.window)
                self.messagewindow.overrideredirect(True)
                self.messagewindow.geometry('+479+504')
                # 载入检查项文件result.xls失败\n请检查文件是否存在或数据损坏
                # Fail to load result.xls, please check this file if error
                if self.language == 'en':
                    msg = tk.Message(self.messagewindow,
                                     text='Fail to load result.xls,\nplease check this file if error!',
                                     font=('Arial', 50),
                                     fg='white', bg='red', width=400)
                else:
                    msg = tk.Message(self.messagewindow,
                                     text='载入检查项文件result.xls失败\n请检查文件是否存在或数据损坏',
                                     font=('Arial', 50),
                                     fg='white', bg='red', width=400)
                msg.pack()
                self.messagewindow.after(3000, self.check_if_running, self.messagewindow)
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
        # 载入机种配置清单
        try:
            self.modelset_xls = pd.read_excel(work_address + '/model_set.xls')
            self.modelset_xls = self.modelset_xls.to_dict(orient='records')
        except:
            message = traceback.format_exc()
            print(message)
            # logger.error(message)
            logger.error('载入机种配置文件model_set.xls失败，请检查文件是否存在或数据损坏!\n%s'%message)
            try:
                self.messagewindow = tk.Toplevel(self.window)
                self.messagewindow.overrideredirect(True)
                self.messagewindow.geometry('+479+504')
                # 载入检查项文件model_set.xls失败\n请检查文件是否存在或数据损坏!
                # Fail to load model_set.xls, please check this file if error
                if self.language == 'en':
                    msg = tk.Message(self.messagewindow,
                                     text='Fail to load model_set.xls,\nplease check this file if error!',
                                     font=('Arial', 50),
                                     fg='white', bg='red', width=400)
                else:
                    msg = tk.Message(self.messagewindow,
                                     text='载入检查项文件model_set.xls失败\n请检查文件是否存在或数据损坏!',
                                     font=('Arial', 50),
                                     fg='white', bg='red', width=400)
                msg.pack()
                self.messagewindow.after(3000, self.check_if_running, self.messagewindow)
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
        # 载入历史数据，如果文件损坏或者不存在则新建
        filename = work_address + '/history/' + str(self.mo) + ' + ' + str(self.station) + '.csv'
        try:
            pd_history = pd.read_csv(filename)
            self.history_xls = pd_history.to_dict(orient='records')
            # 将history_xls里面所有数据都转换为str格式
            for i in range(len(self.history_xls)):
                self.history_xls[i] = dict([(x, str(y)) for x, y in self.history_xls[i].items()])

            # 随机码流水号self.watercode
            self.watercode = 0

            # 根据历史数据，查询当前最新的随机码流水号self.watercode，假如不存在从0开始
            for i in range(len(self.history_xls)):
                for item in self.result_xls:
                    if self.history_xls[i]['checkItem'] == str(item['Meaning']):
                        if self.history_xls[i]['checkItem'] not in self.image_name:
                            self.image_name.append(deepcopy(self.history_xls[i]['checkItem']))
                            self.image_result.append(deepcopy(self.history_xls[i]['result']))
                            if self.history_xls[i]['prodCode'].find('---') != -1:
                                self.watercode += int(self.history_xls[i]['prodCode'].split('---')[1])
                                self.image_number.append(int(self.history_xls[i]['prodCode'].split('---')[1]))
                            else:
                                self.watercode += 1
                                self.image_number.append(1)
                        else:
                            ind = self.image_name.index(self.history_xls[i]['checkItem'])
                            if self.history_xls[i]['prodCode'].find('---') != -1:
                                self.watercode += int(self.history_xls[i]['prodCode'].split('---')[1])
                                self.image_number[ind] += int(self.history_xls[i]['prodCode'].split('---')[1])
                            else:
                                self.watercode += 1
                                self.image_number[ind] += 1
        except:
            message = traceback.format_exc()
            print(message)
            # logger.error(message)
            logger.warning('载入历史数据文件损坏或者不存在,自动新建文件!\n%s'%message)
            data_list = {
                'time': '',
                'prodCode': '',
                'checkItem': '',
                'result': '',
                'Uploaded': '',
                'mo': '',
                'model': '',
                'line': '',
                'section': '',
                'group': '',
                'station': ''}
            pf = pd.DataFrame([data_list])
            pf.to_csv(filename, index=False, header=False)
            # 重新载入空白self.history_xls
            self.history_xls = pd.read_csv(filename)
            self.history_xls = self.history_xls.to_dict(orient='records')

            # 随机码流水号self.watercode
            self.watercode = 0

            # 根据历史数据，查询当前最新的随机码流水号self.watercode，假如不存在从0开始
            for i in range(len(self.history_xls)):
                for item in self.result_xls:
                    if self.history_xls[i]['checkItem'] == str(item['Meaning']):
                        if self.history_xls[i]['checkItem'] not in self.image_name:
                            self.image_name.append(deepcopy(self.history_xls[i]['checkItem']))
                            self.image_result.append(deepcopy(self.history_xls[i]['result']))
                            if self.history_xls[i]['prodCode'].find('---') != -1:
                                self.watercode += int(self.history_xls[i]['prodCode'].split('---')[1])
                                self.image_number.append(int(self.history_xls[i]['prodCode'].split('---')[1]))
                            else:
                                self.watercode += 1
                                self.image_number.append(1)
                        else:
                            ind = self.image_name.index(self.history_xls[i]['checkItem'])
                            if self.history_xls[i]['prodCode'].find('---') != -1:
                                self.watercode += int(self.history_xls[i]['prodCode'].split('---')[1])
                                self.image_number[ind] += int(self.history_xls[i]['prodCode'].split('---')[1])
                            else:
                                self.watercode += 1
                                self.image_number[ind] += 1



    # 在特定主题下并持续发布消息
    def sendmqtt(self, topic, key, data, Response, Return):
        msg = {}
        msg['Body'] = deepcopy(data)
        msg['ClientID'] = self.ctct_clientid
        msg['Software'] = self.ctct_software + '_' + self.ctct_version
        msg['DeviceID'] = self.ctct_deviceid
        msg['CPUID'] = self.ctct_CPIUID
        msg['Return'] = Return
        msg['MessID'] = str(datetime.datetime.now()).replace('-', '').replace(' ', '').replace('.', '').replace(':',
                                                                                                                '')[:17]
        msg['PublishTime'] = str(datetime.datetime.now())[:19]
        msg['MessQos'] = 2
        msg['Response'] = Response
        msg['Key'] = key
        msg['FeedBack'] = 0
        msg = json.dumps(msg)
        ress = {'result': '',
                'description': ''}
        try:
            self.ctct_sendlock.acquire()
            result = self.client.publish(topic, msg, 2)
            self.ctct_sendlock.release()
            status = result[0]
            if status == 0:
                print(f"Send `{msg}` to topic `{topic}`")
                logger.info(f"Send `{msg}` to topic `{topic}`")
                ress['result'] = 'OK'
                ress['description'] = f"Send `{msg}` to topic `{topic}`"
            else:
                print(f"Failed to send message to topic {topic}")
                logger.info(f"Failed to send message to topic {topic}")
                ress['result'] = 'OK'
                ress['description'] = f"Failed to send message to topic {topic}"
        except:
            print(f"Failed to Send `{msg}` to topic {topic}")
            logger.info(f"Failed to Send `{msg}` to topic {topic}")
            ress['result'] = 'FAIL'
            ress['description'] = f"Failed to Send `{msg}` to topic {topic}"
        return ress

    # 连接mqtt代理服务器
    def connect_mqtt(self):
        '''连接mqtt代理服务器'''

        def on_connect(client, userdata, flags, rc):
            '''连接回调函数'''
            # 响应状态码为0表示连接成功
            if rc == 0:
                print("Connected to MQTT OK!")
                logger.info("Connected to MQTT OK!\n MQTTclientID=%s" % self.clientid_new)
                try:
                    topic_list = [(self.ctct_selfreturn, 0), (self.ctct_linecmd, 0), (self.ctct_kanbancmd, 0)]
                    # 订阅指定消息主题
                    client.subscribe(topic_list)
                except:
                    logger.error('mqtt订阅失败，准备重新订阅!')
            else:
                print("Failed to connect, return code %d\n", rc)
                logger.error("Failed to connect")

        # 连接mqtt代理服务器，并获取连接引用

        nowtime = str(datetime.datetime.now())
        # random_code = '`' + self.mo + nowtime[18:26] + str(self.watercode).rjust(6, '0') + '---' + str(number)
        # self.ctct_clientid = self.ctct_clientid +
        self.clientid_new = self.ctct_clientid+'_'+nowtime[18:26]
        client = mqtt_client.Client(self.clientid_new)
        client.on_connect = on_connect

        msg = {}
        msg['Body'] = {
            "Factory": self.ctct_factory,
            "Product": self.ctct_produce,
            "Line": self.ctct_line,
            "Host": 'None',
            "IP": self.userIP,
            "State": "OffLineByWill"
        }
        msg['ClientID'] = self.ctct_clientid
        msg['Software'] = self.ctct_software + '_' + self.ctct_version
        msg['DeviceID'] = self.ctct_deviceid
        msg['CPUID'] = self.ctct_CPIUID
        msg['Return'] = ''
        msg['MessID'] = str(datetime.datetime.now()).replace('-', '').replace(' ', '').replace('.', '').replace(':',
                                                                                                                '')[:17]
        msg['PublishTime'] = str(datetime.datetime.now())[:19]
        msg['MessQos'] = 2
        msg['Response'] = ''
        msg['Key'] = 'ClientState'
        msg['FeedBack'] = 0
        msg = json.dumps(msg)

        client.will_set(self.ctct_selflog, msg, 0, False)

        client.connect(self.ctct_ip, self.ctct_port, self.ctct_keepalive)
        return client

    # 初始化mqtt模块
    def init_mqtt(self):
        '''运行发布者'''
        self.client = self.connect_mqtt()
        self.ctct_sendlock = threading.Lock()

    # 显示界面
    def show(self):
        # 设定界面尺寸
        self.window.title('MainUI')
        self.window.configure(background='white')
        self.window.attributes('-fullscreen', True)

        # 加载背景图
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.img_mainbackground = Image.open(work_address + '/mainui_' + self.language + '_720.png')
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.img_mainbackground = Image.open(work_address + '/mainui_' + self.language + '_720.png')
        else:
            self.img_mainbackground = Image.open(work_address + '/mainui_' + self.language + '.png')
        self.img_mainbackground = ImageTk.PhotoImage(self.img_mainbackground)
        self.label_mainbackground = Label(self.window)
        self.label_mainbackground['image'] = self.img_mainbackground
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_mainbackground.place(x=320, y=0)
        else:
            self.label_mainbackground.place(x=0, y=0)

        # 载入当前时间显示框
        self.str_nowtime = StringVar()
        self.str_nowtime.set('')  # FFC0CB
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_nowtime = Label(self.window, textvariable=self.str_nowtime, font=('Arial', 13),
                                       width=19, fg='#FFFFFF', bg='#0087DC', anchor='w')
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_nowtime = Label(self.window, textvariable=self.str_nowtime, font=('Arial', 13),
                                       width=19, fg='#FFFFFF', bg='#0087DC', anchor='w')
        else:
            self.label_nowtime = Label(self.window, textvariable=self.str_nowtime, font=('Arial', 15),
                                       width=19, fg='#FFFFFF', bg='#0087DC', anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_nowtime.place(x=1060 + 320, y=35)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_nowtime.place(x=834, y=27)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_nowtime.place(x=834, y=27)
        else:
            self.label_nowtime.place(x=1060, y=35)
        # 载入工单显示框
        self.str_mo = StringVar()
        self.str_mo.set(self.mo)
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_mo = Label(self.window, textvariable=self.str_mo, font=('Arial', 9),
                                  width=12, fg='#000000', bg='#FFFFFF', anchor='w')
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_mo = Label(self.window, textvariable=self.str_mo, font=('Arial', 9),
                                  width=12, fg='#000000', bg='#FFFFFF', anchor='w')
        else:
            self.label_mo = Label(self.window, textvariable=self.str_mo, font=('Arial', 11),
                                  width=13, fg='#000000', bg='#FFFFFF', anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_mo.place(x=55 + 2 - 1 + 320, y=89 + 2 + 1)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_mo.place(x=49, y=65 + 2)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_mo.place(x=49, y=65 + 2)
        else:
            self.label_mo.place(x=55 + 2 - 1, y=89 + 1 + 2)

        # 载入机种显示框
        self.str_model = StringVar()
        self.str_model.set(self.model)
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_model = Label(self.window, textvariable=self.str_model, font=('Arial', 11),
                                     width=12, fg='#000000', bg='#FFFFFF', anchor='w')
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_model = Label(self.window, textvariable=self.str_model, font=('Arial', 11),
                                     width=12, fg='#000000', bg='#FFFFFF', anchor='w')
        else:
            self.label_model = Label(self.window, textvariable=self.str_model, font=('Arial', 11),
                                     width=16, fg='#000000', bg='#FFFFFF', anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_model.place(x=245 + 2 - 1 + 320, y=89 + 2 + 1)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_model.place(x=202, y=65)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_model.place(x=202, y=65)
        else:
            self.label_model.place(x=245 + 2 - 1, y=89 + 1 + 2)

        # 载入线别显示框
        self.str_line = StringVar()
        self.str_line.set(self.line)
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_line = Label(self.window, textvariable=self.str_line, font=('Arial', 9),
                                    width=4, fg='#000000', bg='#FFFFFF', anchor='w')
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_line = Label(self.window, textvariable=self.str_line, font=('Arial', 9),
                                    width=4, fg='#000000', bg='#FFFFFF', anchor='w')
        else:
            self.label_line = Label(self.window, textvariable=self.str_line, font=('Arial', 10),
                                    width=6, fg='#000000', bg='#FFFFFF', anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_line.place(x=476 + 2 - 1 + 320, y=89 + 2 + 1)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_line.place(x=382, y=65 + 2)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_line.place(x=382, y=65 + 2)
        else:
            self.label_line.place(x=476 + 2 - 1, y=89 + 1 + 2)

        # 载入段别显示框
        self.str_section = StringVar()
        self.str_section.set(self.section)
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_section = Label(self.window, textvariable=self.str_section, font=('Arial', 9),
                                       width=11, fg='#000000', bg='#FFFFFF', anchor='w')
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_section = Label(self.window, textvariable=self.str_section, font=('Arial', 9),
                                       width=11, fg='#000000', bg='#FFFFFF', anchor='w')
        else:
            self.label_section = Label(self.window, textvariable=self.str_section, font=('Arial', 11),
                                       width=11, fg='#000000', bg='#FFFFFF', anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_section.place(x=597 + 2 - 1 + 320, y=89 + 2 + 1)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_section.place(x=480, y=65 + 2)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_section.place(x=480, y=65 + 2)
        else:
            self.label_section.place(x=597 + 2 - 1, y=89 + 1 + 2)

        # 载入组别显示框
        self.str_group = StringVar()
        self.str_group.set(self.group)
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_group = Label(self.window, textvariable=self.str_group, font=('Arial', 9),
                                     width=11, fg='#000000', bg='#FFFFFF', anchor='w')
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_group = Label(self.window, textvariable=self.str_group, font=('Arial', 9),
                                     width=11, fg='#000000', bg='#FFFFFF', anchor='w')
        else:
            self.label_group = Label(self.window, textvariable=self.str_group, font=('Arial', 11),
                                     width=15, fg='#000000', bg='#FFFFFF', anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_group.place(x=796 + 2 - 1 + 320, y=89 + 2 + 1)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_group.place(x=641, y=65 + 2)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_group.place(x=641, y=65 + 2)
        else:
            self.label_group.place(x=796 + 2 - 1, y=89 + 1 + 2)

        # 载入站别显示框
        self.str_station = StringVar()
        self.str_station.set(self.station)
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_station = Label(self.window, textvariable=self.str_station, font=('Arial', 9),
                                       width=11, fg='#000000', bg='#FFFFFF', anchor='w')
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_station = Label(self.window, textvariable=self.str_station, font=('Arial', 9),
                                       width=11, fg='#000000', bg='#FFFFFF', anchor='w')
        else:
            self.label_station = Label(self.window, textvariable=self.str_station, font=('Arial', 11),
                                       width=11, fg='#000000', bg='#FFFFFF', anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_station.place(x=1015 + 2 - 1 + 320, y=89 + 2 + 1)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_station.place(x=816, y=65 + 2)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_station.place(x=816, y=65 + 2)
        else:
            self.label_station.place(x=1015 + 2 - 1, y=89 + 1 + 2)

        # 载入工单达成率显示框
        self.str_finish = StringVar()
        self.str_finish.set('00.00%')
        self.label_finish = Label(self.window, textvariable=self.str_finish, font=('Arial', 13),
                                  width=8, fg='#FFFFFF', bg='#0095C5', anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_finish.place(x=95 + 320, y=178)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_finish.place(x=72, y=128)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_finish.place(x=72, y=128)
        else:
            self.label_finish.place(x=95, y=178)

        # 载入工单良品率显示框
        self.str_pass = StringVar()
        self.str_pass.set('00.00%')
        self.label_pass = Label(self.window, textvariable=self.str_pass, font=('Arial', 13),
                                width=8, fg='#FFFFFF', bg='#00C7A6', anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_pass.place(x=353 + 13 + 320, y=178)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_pass.place(x=283, y=128)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_pass.place(x=283, y=128)
        else:
            self.label_pass.place(x=353 + 13, y=178)

        # 载入运行时间显示框
        self.str_runtime = StringVar()
        self.str_runtime.set('')
        self.label_runtime = Label(self.window, textvariable=self.str_runtime, font=('Arial', 12),
                                   width=8, fg='#FFFFFF', bg='#00BCC7', anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_runtime.place(x=612 + 320, y=178)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_runtime.place(x=489, y=128)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_runtime.place(x=489, y=128)
        else:
            self.label_runtime.place(x=612, y=178)

        # 载入信号采集器状态显示框
        self.str_signal = StringVar()
        self.str_signal.set('')

        self.label_signal = Label(self.window, textvariable=self.str_signal, font=('Arial', 13),
                                  width=8, fg='#FFFFFF', bg='#00A6D4', anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_signal.place(x=872 + 320, y=178)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_signal.place(x=698, y=128)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_signal.place(x=698, y=128)
        else:
            self.label_signal.place(x=872, y=178)

        # 载入网络状态显示框
        self.str_network = StringVar()
        self.str_network.set('')
        self.label_network = Label(self.window, textvariable=self.str_network, font=('Arial', 13),
                                   width=8, fg='#FFFFFF', bg='#00A9C7', anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_network.place(x=1132 + 320, y=178)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_network.place(x=905, y=128)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_network.place(x=905, y=128)
        else:
            self.label_network.place(x=1132, y=178)

        # 载入检查码清单显示框
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.tree_checklist = ttk.Treeview(self.window, columns=['1', '2'], height=13, show='headings',
                                               selectmode='none')
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.tree_checklist = ttk.Treeview(self.window, columns=['1', '2'], height=13, show='headings',
                                               selectmode='none')
        else:
            self.tree_checklist = ttk.Treeview(self.window, columns=['1', '2'], height=17, show='headings',
                                               selectmode='none')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.tree_checklist.place(x=320, y=252 + 5)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.tree_checklist.place(x=-1, y=189)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.tree_checklist.place(x=-1, y=189)
        else:
            self.tree_checklist.place(x=-1, y=252 + 5)
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.tree_checklist.column('1', width=60, anchor='center')
            self.tree_checklist.column('2', width=110, anchor='w')
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.tree_checklist.column('1', width=60, anchor='center')
            self.tree_checklist.column('2', width=110, anchor='w')
        else:
            self.tree_checklist.column('1', width=68, anchor='center')
            self.tree_checklist.column('2', width=146, anchor='w')
        self.tree_checklist.heading('1', text='编号')
        self.tree_checklist.heading('2', text='检查项')
        for i in range(len(self.result_xls)):
            self.tree_checklist.insert('', i, values=[self.result_xls[i]['Code'], self.result_xls[i]['Meaning']])

        # 加载检查码清单标题图
        self.img_checklistname = Image.open(work_address + '/checklist_name_' + self.language + '.png')
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.img_checklistname = self.img_checklistname.resize((172, 23), Image.ANTIALIAS)
            self.img_checklistname = ImageTk.PhotoImage(self.img_checklistname)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.img_checklistname = self.img_checklistname.resize((172, 23), Image.ANTIALIAS)
            self.img_checklistname = ImageTk.PhotoImage(self.img_checklistname)
        else:
            self.img_checklistname = ImageTk.PhotoImage(self.img_checklistname)
        self.label_imgchecklistname = Label(self.window, borderwidth=0, highlightthickness=0)
        self.label_imgchecklistname['image'] = self.img_checklistname
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_imgchecklistname.place(x=0 + 320, y=252 + 8 - 4)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_imgchecklistname.place(x=0, y=188)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_imgchecklistname.place(x=0, y=188)
        else:
            self.label_imgchecklistname.place(x=0, y=252 + 8 - 4)

        # 载入具体信息清单显示框
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.tree_history = ttk.Treeview(self.window, columns=['1', '2', '3', '4', '5', '6', '7'],
                                             height=17, show='headings', selectmode='none')
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.tree_history = ttk.Treeview(self.window, columns=['1', '2', '3', '4', '5', '6', '7'],
                                             height=17, show='headings', selectmode='none')
        else:
            self.tree_history = ttk.Treeview(self.window, columns=['1', '2', '3', '4', '5', '6', '7'],
                                             height=14, show='headings', selectmode='none')
        self.style = ttk.Style()
        self.style.configure('Treeview', font=('Arial', 11))
        self.style.configure('Treeview.Heading', font=('Arial', 12))
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.tree_history.column('1', width=218, anchor='center')
            self.tree_history.column('2', width=196, anchor='center')
            self.tree_history.column('3', width=135, anchor='center')
            self.tree_history.column('4', width=96, anchor='center')
            self.tree_history.column('5', width=85, anchor='center')
            self.tree_history.column('6', width=81, anchor='center')
            self.tree_history.column('7', width=209, anchor='center')
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.tree_history.column('1', width=218, anchor='center')
            self.tree_history.column('2', width=196, anchor='center')
            self.tree_history.column('3', width=135, anchor='center')
            self.tree_history.column('4', width=96, anchor='center')
            self.tree_history.column('5', width=85, anchor='center')
            self.tree_history.column('6', width=81, anchor='center')
            self.tree_history.column('7', width=209, anchor='center')
        else:
            self.tree_history.column('1', width=274, anchor='center')
            self.tree_history.column('2', width=247, anchor='center')
            self.tree_history.column('3', width=167, anchor='center')
            self.tree_history.column('4', width=120, anchor='center')
            self.tree_history.column('5', width=106, anchor='center')
            self.tree_history.column('6', width=101, anchor='center')
            self.tree_history.column('7', width=263, anchor='center')
        self.tree_history.heading('1', text='产品条码')
        self.tree_history.heading('2', text='采集时间')
        self.tree_history.heading('3', text='检查项')
        self.tree_history.heading('4', text='判断结果')
        self.tree_history.heading('5', text='保存情况')
        self.tree_history.heading('6', text='上传情况')
        self.tree_history.heading('7', text='异常信息')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.tree_history.place(x=320, y=720 + 3)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.tree_history.place(x=-1, y=534)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.tree_history.place(x=-1, y=534)
        else:
            self.tree_history.place(x=-1, y=720 + 2)

        # 加载具体信息标题图
        self.img_listname = Image.open(work_address + '/list_name_' + self.language + '.png')
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.img_listname = self.img_listname.resize((1024, 24), Image.ANTIALIAS)
            self.img_listname = ImageTk.PhotoImage(self.img_listname)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.img_listname = self.img_listname.resize((1024, 24), Image.ANTIALIAS)
            self.img_listname = ImageTk.PhotoImage(self.img_listname)
        else:
            self.img_listname = ImageTk.PhotoImage(self.img_listname)
        self.label_imglistname = Label(self.window, borderwidth=0, highlightthickness=0)
        self.label_imglistname['image'] = self.img_listname
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_imglistname.place(x=0 + 320, y=720 - 2)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_imglistname.place(x=0, y=534)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_imglistname.place(x=0, y=534)
        else:
            self.label_imglistname.place(x=0, y=720 - 2)

        # 加载输入条码或数据的文本框
        self.str_keyin = StringVar()
        self.str_keyin.set('')
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.text_keyin = Entry(self.window, textvariable=self.str_keyin, width=45, font=('Arial', 12),
                                    fg='#000000', bg='#FFFFFF', borderwidth=0, highlightthickness=0)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.text_keyin = Entry(self.window, textvariable=self.str_keyin, width=45, font=('Arial', 12),
                                    fg='#000000', bg='#FFFFFF', borderwidth=0, highlightthickness=0)
        else:
            self.text_keyin = Entry(self.window, textvariable=self.str_keyin, width=43 - 2, font=('Arial', 15),
                                    fg='#000000', bg='#FFFFFF', borderwidth=0, highlightthickness=0)
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.text_keyin.place(x=103 + 320, y=642 + 1)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.text_keyin.place(x=83, y=480)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.text_keyin.place(x=83, y=480)
        else:
            self.text_keyin.place(x=103, y=642 + 1)
        self.text_keyin.focus()
        self.text_keyin.bind('<Return>', self.getbarcode)

        # 加载扫码提示文本框
        self.str_keyinmessage = StringVar()
        self.str_keyinmessage.set('')
        self.label_keyinmessage = Label(self.window,
                                        textvariable=self.str_keyinmessage, width=41, font=('Arial', 17),
                                        fg='#000000', bg='#FFFFFF', anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_keyinmessage.place(x=670 + 8 + 320, y=640)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_keyinmessage.place(x=542, y=473)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_keyinmessage.place(x=542, y=473)
        else:
            self.label_keyinmessage.place(x=670 + 8, y=640)

        # 加载圆饼图
        try:
            self.img_yuanbing = Image.open(work_address + '/yuanbingtu.jpg')
        except:
            message = traceback.format_exc()
            print(message)
            logger.error(message)
            self.img_yuanbing = Image.open(work_address + '/yuanbingtu_backup.jpg')
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.img_yuanbing = self.img_yuanbing.resize((419, 274), Image.ANTIALIAS)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.img_yuanbing = self.img_yuanbing.resize((419, 274), Image.ANTIALIAS)
        else:
            self.img_yuanbing = self.img_yuanbing.resize((525, 368), Image.ANTIALIAS)
        self.img_yuanbing = ImageTk.PhotoImage(self.img_yuanbing)
        self.label_yuanbing = tk.Label(self.window)
        self.label_yuanbing['image'] = self.img_yuanbing
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_yuanbing.place(x=755 + 320, y=253)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_yuanbing.place(x=604, y=189)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_yuanbing.place(x=604, y=189)
        else:
            self.label_yuanbing.place(x=755, y=253)

        # 加载直方图
        try:
            self.img_zhifang = Image.open(work_address + '/zhifangtu.jpg')
        except:
            message = traceback.format_exc()
            print(message)
            logger.error(message)
            self.img_zhifang = Image.open(work_address + '/zhifangtu_backup.jpg')
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.img_zhifang = self.img_zhifang.resize((419, 274), Image.ANTIALIAS)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.img_zhifang = self.img_zhifang.resize((419, 274), Image.ANTIALIAS)
        else:
            self.img_zhifang = self.img_zhifang.resize((525, 368), Image.ANTIALIAS)
        self.img_zhifang = ImageTk.PhotoImage(self.img_zhifang)
        self.label_zhifang = tk.Label(self.window)
        self.label_zhifang['image'] = self.img_zhifang
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_zhifang.place(x=222 + 320, y=253)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_zhifang.place(x=178, y=189)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_zhifang.place(x=178, y=189)
        else:
            self.label_zhifang.place(x=222, y=253)

        # 载入刷新图像倒计时显示框
        self.str_flash_clock = StringVar()
        self.str_flash_clock.set('5s')  # 0087DC
        self.label_flash_clock = Label(self.window,
                                       textvariable=self.str_flash_clock,
                                       font=('Arial', 12),
                                       width=2, fg='#0087DC',
                                       bg='#FFFFFF', anchor='w')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_flash_clock.place(x=1253 + 320, y=240)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_flash_clock.place(x=1253 + 320, y=240)
        else:
            self.label_flash_clock.place(x=1253, y=240)

        # 载入title显示框
        self.str_title = StringVar()
        self.str_title.set(self.title_name)  # FFC0CB
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            label_12 = Label(self.window, textvariable=self.str_title, font=('Arial', 28), width=26, fg='#FFFFFF',
                             bg='#0087DC')
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            label_12 = Label(self.window, textvariable=self.str_title, font=('Arial', 28), width=26, fg='#FFFFFF',
                             bg='#0087DC')
        else:
            label_12 = Label(self.window, textvariable=self.str_title, font=('Arial', 30), width=33, fg='#FFFFFF',
                             bg='#0087DC')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            label_12.place(x=200 + 320, y=25 - 12)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            label_12.place(x=161, y=5)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            label_12.place(x=161, y=5)
        else:
            label_12.place(x=200, y=25 - 12)

        # 更换工单按钮
        self.img_change = Image.open(work_address + '/reset_' + self.language + '.png')
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.img_change = self.img_change.resize((63, 25), Image.ANTIALIAS)
            self.img_change = ImageTk.PhotoImage(self.img_change)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.img_change = self.img_change.resize((63, 25), Image.ANTIALIAS)
            self.img_change = ImageTk.PhotoImage(self.img_change)
        else:
            self.img_change = ImageTk.PhotoImage(self.img_change)
        self.bt_change = tk.Button(self.window, relief='flat', width=66, height=28, bg='#FFFFFF',
                                   command=self.changemo)
        self.bt_change['image'] = self.img_change
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.bt_change.place(x=1192 + 320, y=81)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.bt_change.place(x=956, y=61)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.bt_change.place(x=956, y=61)
        else:
            self.bt_change.place(x=1192, y=81)





        # 加载呼出键盘按钮
        self.img_keyboard = Image.open(work_address + '/keyboard.png')
        self.img_keyboard = ImageTk.PhotoImage(self.img_keyboard)
        self.bt_keyboard = tk.Button(self.window, relief='flat', width=20, height=20, bg='#FFFFFF',
                                     command=self.openkeyboard)
        self.bt_keyboard['image'] = self.img_keyboard
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.bt_keyboard.place(x=1920 - 20 - 5, y=0)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.bt_keyboard.place(x=999, y=0)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.bt_keyboard.place(x=999, y=0)
        else:
            # self.bt_keyboard.place(x=420, y=627)
            self.bt_keyboard.place(x=1280 - 20 - 5, y=0)

        # 载入装载箱状态信息
        self.str_container = StringVar()
        if self.container_open == '1':
            # 加载装载箱手动按钮
            self.img_hujiao = Image.open(work_address + '/hujiao_' + self.language + '.png')
            if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                self.img_hujiao = self.img_hujiao.resize((63, 25), Image.ANTIALIAS)
                self.img_hujiao = ImageTk.PhotoImage(self.img_hujiao)
            elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                self.img_hujiao = self.img_hujiao.resize((63, 25), Image.ANTIALIAS)
                self.img_hujiao = ImageTk.PhotoImage(self.img_hujiao)
            else:
                self.img_hujiao = ImageTk.PhotoImage(self.img_hujiao)
            self.bt_hujiao = tk.Button(self.window, relief='flat', width=75, height=28, bg='#0087DC',
                                       command=self.hujiao)
            self.bt_hujiao['image'] = self.img_hujiao
            if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
                self.bt_hujiao.place(x=1192 + 320 - 80 - 80 - 80+20, y=35)
            elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
                self.bt_hujiao.place(x=956 - 80 - 80 - 80+20, y=27)
            elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                self.bt_hujiao.place(x=956 - 80 - 80 - 80+20, y=27)
            else:
                self.bt_hujiao.place(x=1192 - 80 - 80 - 80+20, y=35)

            self.img_qingkong = Image.open(work_address + '/qingkong_' + self.language + '.png')
            if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                self.img_qingkong = self.img_qingkong.resize((63, 25), Image.ANTIALIAS)
                self.img_qingkong = ImageTk.PhotoImage(self.img_qingkong)
            elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                self.img_qingkong = self.img_qingkong.resize((63, 25), Image.ANTIALIAS)
                self.img_qingkong = ImageTk.PhotoImage(self.img_qingkong)
            else:
                self.img_qingkong = ImageTk.PhotoImage(self.img_qingkong)
            self.bt_qingkong = tk.Button(self.window, relief='flat', width=75, height=28, bg='#0087DC',
                                         command=self.qingkong)
            self.bt_qingkong['image'] = self.img_qingkong
            if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
                self.bt_qingkong.place(x=1192 + 320 - 80 - 80 - 80 - 80+15, y=35)
            elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
                self.bt_qingkong.place(x=956 - 80 - 80 - 80 - 80+15, y=27)
            elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                self.bt_qingkong.place(x=956 - 80 - 80 - 80 - 80+15, y=27)
            else:
                self.bt_qingkong.place(x=1192 - 80 - 80 - 80 - 80+15, y=35)

            self.img_huanwei = Image.open(work_address + '/huanwei_' + self.language + '.png')
            if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                self.img_huanwei = self.img_huanwei.resize((63, 25), Image.ANTIALIAS)
                self.img_huanwei = ImageTk.PhotoImage(self.img_huanwei)
            elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                self.img_huanwei = self.img_huanwei.resize((63, 25), Image.ANTIALIAS)
                self.img_huanwei = ImageTk.PhotoImage(self.img_huanwei)
            else:
                self.img_huanwei = ImageTk.PhotoImage(self.img_huanwei)
            self.bt_huanwei = tk.Button(self.window, relief='flat', width=75, height=28, bg='#0087DC',
                                        command=self.huanwei)
            self.bt_huanwei['image'] = self.img_huanwei
            if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
                self.bt_huanwei.place(x=1192 + 320 - 80 - 80 - 80 - 80 - 80+10, y=35)
            elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
                self.bt_huanwei.place(x=956 - 80 - 80 - 80 - 80 - 80+10, y=27)
            elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                self.bt_huanwei.place(x=956 - 80 - 80 - 80 - 80 - 80+10, y=27)
            else:
                self.bt_huanwei.place(x=1192 - 80 - 80 - 80 - 80 - 80+10, y=35)

            number_max = 1
            for i in range(len(self.modelset_xls)):
                if str(self.modelset_xls[i]['机种型号']) == str(self.model):
                    number_max = int(self.modelset_xls[i]['单箱数量'])
                    break
                elif i == len(self.modelset_xls) - 1:
                    print('model_set机种文件内无法查询到' + str(self.model) + '该机种!')
                    for j in range(len(self.modelset_xls) - 1, -1, -1):
                        if str(self.modelset_xls[j]['机种型号']) == '其他':
                            number_max = int(self.modelset_xls[j]['单箱数量'])
                            print(
                                'model_set机种文件内无法查询到%s机种,自动使用其他型号配置%s' % (
                                    self.model, number_max))
                            break
                        elif j == 0:
                            print('model_set机种文件内无法查询到' + str(self.model) + '该机种!且找不到其他选项')
            str_show = ''
            for i in range(len(self.side_name)):
                str_show = str_show + ' ' + '%s=%s' % (self.side_name[i], self.side_number[i])
            str_show = str_show + ' ' + 'Now=%s Max=%s' % (self.side_now, number_max)
            self.str_container.set(str_show)
        else:
            self.str_container.set('')
        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_container = Label(self.window, textvariable=self.str_container, font=('Arial', 10),
                                         width=50, fg='#FFFFFF', bg='#0087DC', anchor='e')
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_container = Label(self.window, textvariable=self.str_container, font=('Arial', 10),
                                         width=50, fg='#FFFFFF', bg='#0087DC', anchor='e')
        else:
            self.label_container = Label(self.window, textvariable=self.str_container, font=('Arial', 10),
                                         width=50, fg='#FFFFFF', bg='#0087DC', anchor='e')
        if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
            self.label_container.place(x=1060 + 320 - 80-60-80-100, y=3)
        elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
            self.label_container.place(x=834 - 80-60-80-100, y=3)
        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
            self.label_container.place(x=834 - 80-60-80-100, y=3)
        else:
            self.label_container.place(x=1060 - 80-60-80-100, y=3)

        if str(self.light_open) == '1':
            # 加载绿灯图
            try:
                self.img_light_green = Image.open(work_address + '/light_default.png')
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
                self.img_light_green = Image.open(work_address + '/light_default_backup.jpg')
            if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                self.img_light_green = self.img_light_green.resize((20, 20), Image.ANTIALIAS)
            elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                self.img_light_green = self.img_light_green.resize((20, 20), Image.ANTIALIAS)
            else:
                self.img_light_green = self.img_light_green.resize((20, 20), Image.ANTIALIAS)
            self.img_light_green = ImageTk.PhotoImage(self.img_light_green)
            self.label_light_green = tk.Label(self.window, bg='#0087DC')
            self.label_light_green['image'] = self.img_light_green
            if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
                self.label_light_green.place(x=1060 + 320 - 80 - 10, y=35)
            elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
                self.label_light_green.place(x=834 - 80 - 10, y=28)
            elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                self.label_light_green.place(x=834 - 80 - 10, y=28)
            else:
                self.label_light_green.place(x=1060 - 80 - 10, y=35)

            # 加载黄灯图
            try:
                self.img_light_yellow = Image.open(work_address + '/light_default.png')
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
                self.img_light_yellow = Image.open(work_address + '/light_default_backup.jpg')
            if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                self.img_light_yellow = self.img_light_yellow.resize((20, 20), Image.ANTIALIAS)
            elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                self.img_light_yellow = self.img_light_yellow.resize((20, 20), Image.ANTIALIAS)
            else:
                self.img_light_yellow = self.img_light_yellow.resize((20, 20), Image.ANTIALIAS)
            self.img_light_yellow = ImageTk.PhotoImage(self.img_light_yellow)
            self.label_light_yellow = tk.Label(self.window, bg='#0087DC')
            self.label_light_yellow['image'] = self.img_light_yellow
            if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
                self.label_light_yellow.place(x=1060 + 320 - 80 + 30 - 10, y=35)
            elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
                self.label_light_yellow.place(x=834 - 80 + 30 - 10, y=28)
            elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                self.label_light_yellow.place(x=834 - 80 + 30 - 10, y=28)
            else:
                self.label_light_yellow.place(x=1060 - 80 + 30 - 10, y=35)

            # 加载红灯图
            try:
                self.img_light_red = Image.open(work_address + '/light_default.png')
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
                self.img_light_red = Image.open(work_address + '/light_default_backup.jpg')
            if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                self.img_light_red = self.img_light_red.resize((20, 20), Image.ANTIALIAS)
            elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                self.img_light_red = self.img_light_red.resize((20, 20), Image.ANTIALIAS)
            else:
                self.img_light_red = self.img_light_red.resize((20, 20), Image.ANTIALIAS)
            self.img_light_red = ImageTk.PhotoImage(self.img_light_red)
            self.label_light_red = tk.Label(self.window, bg='#0087DC')
            self.label_light_red['image'] = self.img_light_red
            if self.screen_type == '1920x1080' or self.screen_type == '1920*1080':
                self.label_light_red.place(x=1060 + 320 - 80 + 30 + 30 - 10, y=35)
            elif self.screen_type == '1280x768' or self.screen_type == '1280*768':
                self.label_light_red.place(x=834 - 80 + 30 + 30 - 10, y=28)
            elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                self.label_light_red.place(x=834 - 80 + 30 + 30 - 10, y=28)
            else:
                self.label_light_red.place(x=1060 - 80 + 30 + 30 - 10, y=35)

    # 工作线程，开启虚拟键盘
    def threading_openkeyboard(self):
        os.system('onboard')

    # 开启虚拟键盘
    def openkeyboard(self):
        mythread_openkeyboard = threading.Thread(target=self.threading_openkeyboard, name='mainui', daemon=True)
        mythread_openkeyboard.start()

    # 运行程序
    def run(self):
        # 显示界面
        self.show()
        if self.ctct_ip != '':
            self.run_ctct()
        # 开启PQM上传功能
        if len(self.shebei_bianma) > 0 and self.shebei_bianma != ['']:
            self.mythread_uptopqm = threading.Thread(target=self.workthreading_uptopqm, name='thread_uptopqm',
                                                     daemon=True)
            self.mythread_uptopqm.start()
            self.mythread_uptopqm_hold = threading.Thread(target=self.workthreading_uptopqm_hold, name='thread_uptopqm',
                                                          daemon=True)
            self.mythread_uptopqm_hold.start()
            
            if self.today_restart_time != [''] and len(self.today_restart_time) > 0:
                self.mythread_pqm_today_restart = threading.Thread(target=self.workthreading_pqm_today_restart, name='thread_pqm_today_restart',
                                                              daemon=True)
                self.mythread_pqm_today_restart.start()

        # 开启线程，记录及显示程序运行时间
        self.mythread_RunningTime = threading.Thread(target=self.workthreading_RunningTime, name='thread_RunningTime',
                                                     daemon=True)
        self.mythread_RunningTime.start()

        # 开启线程，隔一段时间Ping MES服务器，检查上传网络是否通畅
        self.mythread_PingMES = threading.Thread(target=self.workthreading_PingMES, name='thread_PingMES', daemon=True)
        self.mythread_PingMES.start()

        # 开启线程，隔一段时间，自动绘制并刷新界面的直方图以及圆饼图
        self.mythread_RefreshPicture = threading.Thread(target=self.workthreading_RefreshPicture,
                                                        name='thread_RefreshPicture', daemon=True)
        self.mythread_RefreshPicture.start()

        # 定义装载箱状态类触发信号传输通道
        self.container_status_signal = mp.Queue()
        if self.container_open == '1':
            # 开启接受触发信号的线程
            self.mythread_container = threading.Thread(target=self.workthreading_container, name='thread_container',
                                                       daemon=True)
            self.mythread_container.start()
        # 定义报警灯状态类触发信号传输通道
        self.light_status_signal = mp.Queue()
        if self.light_open == '1':
            # 开启接受触发信号的线程
            self.mythread_light = threading.Thread(target=self.workthreading_light, name='thread_light',
                                                   daemon=True)
            self.mythread_light.start()

            if self.db_ip != '':
                # 开启接受触发信号的线程
                self.mythread_uptodb = threading.Thread(target=self.workthreading_uptodb, name='thread_uptodb',
                                                       daemon=True)
                self.mythread_uptodb.start()

        # 若为无条码，则开启GPIO监控进程，和接受触发信号的线程
        if self.youtiaoma == '0':
            # 开启Check进程，与主进程之间建立trigger_times参数，用来来传递触发次数
            self.tuple_triggerTimes_and_nowUsb_and_nowSignal_and_signalList = mp.Queue()
            if self.io_in_type == 'usb':
                self.myprocess_CheckUSB = mp.Process(target=self.workprocess_CheckUSB, args=(
                    self.tuple_triggerTimes_and_nowUsb_and_nowSignal_and_signalList, self.container_status_signal,))
                self.myprocess_CheckUSB.start()
            elif self.io_in_type == 'gpio_old' and os.popen('uname -a').read().find('raspberrypi') != -1:
                self.myprocess_CheckGPIO = mp.Process(target=self.workprocess_CheckGPIO_old, args=(
                    self.tuple_triggerTimes_and_nowUsb_and_nowSignal_and_signalList, self.container_status_signal,))
                self.myprocess_CheckGPIO.start()
            else:
                self.myprocess_CheckGPIO = mp.Process(target=self.workprocess_CheckGPIO, args=(
                    self.tuple_triggerTimes_and_nowUsb_and_nowSignal_and_signalList, self.container_status_signal,
                    self.light_status_signal,))
                self.myprocess_CheckGPIO.start()

            # 开启接受触发信号的线程
            self.mythread_ChuFa = threading.Thread(target=self.workthreading_ChuFa, name='thread_ChuFa', daemon=True)
            self.mythread_ChuFa.start()
        else:
            self.str_signal.set('无需使用(有条码)')
            self.label_signal.configure(fg='#FFFFFF')

    # 监控收料箱的状态信号
    def workthreading_container(self):
        while True:
            while True:
                if not self.container_status_signal.empty():
                    receive = self.container_status_signal.get()
                    print('监控到收料箱的状态信号')
                    print(receive)
                    container_signal_list = receive[0][1:-1].replace(' ', '').replace('"', '').replace("'", "").split(',')
                    container_signal_list = [int(container_signal_list[i]) for i in range(len(container_signal_list))]
                    container_signal_value_list = receive[1][1:-1].replace(' ', '').replace('"', '').replace("'", "").split(',')
                    container_signal_value_list = [int(container_signal_value_list[i]) for i in
                                                   range(len(container_signal_value_list))]
                    logger.info('监控到收料箱的状态信号,container_signal_list=%s,container_signal_value_list=%s' % (container_signal_list,container_signal_value_list))

                    # old_side_status = deepcopy(self.side_status)

                    for z in range(len(container_signal_list)):
                        number_new = container_signal_list[z]
                        value_new = container_signal_value_list[z]
                        # 上传MES
                        # routing变量
                        url_routing = 'http://' + self.url_mes + ':10101/QueryData/QueryDataByPrc'
                        # 测试QA环境
                        # url_routing = 'http://' + '10.148.200.28' + ':10101/TDC/DELTA_DEAL_TEST_DATA_I'
                        params_routing = {
                            'sign': ''
                        }
                        nowtime = str(datetime.datetime.now())
                        random_code = nowtime[20:26] + str(random.randint(1, 254)) + str(random.randint(1, 254))
                        # random_code = random_code[:39]
                        self.container_NO = random_code
                        data_routing = {
                              "FACTORY": self.usr_factory,
                              "QUERY_TYPE": "SYNC_PORT_STATUS_2DCS",
                              "DATA":{
                                  "FACTORY": self.usr_factory,
                                  "EQP_CODE": self.EQP_CODE[number_new],
                                  "EQP_DESC": self.EQP_DESC[number_new],
                                  "EQP_PORT": self.EQP_PORT[number_new],
                                  "PORT_STATUS": str(value_new)
                              }
                            }
                        # data_routing = {
                        #     'factory': self.usr_factory,
                        #     'testType': 'FG_CONTAINER_LINK',
                        #     'routingData': {
                        #         'mo': self.mo,
                        #         'model': self.model,
                        #         'line': self.line,
                        #         'section': self.section,
                        #         'group': self.group,
                        #         'station': self.station,
                        #         'containerNo': self.container_NO,
                        #         'qty': int(self.side_number[self.side_name.index(self.side_now)]),
                        #         'port': self.side_now,
                        #         'needAGV': 'Y'
                        #     },
                        #     'testData': []
                        # }
                        # 开始上传
                        # 将body的数据转换为字符串格式
                        data_str = json.dumps(data_routing, sort_keys=False)
                        # 字符串拼接md5加密
                        src = self.secretkey + data_str
                        md5 = self.keymd5(src)
                        params_routing['sign'] = md5
                        # 发送数据并接收返回数据
                        print(url_routing)
                        print(data_routing)
                        print(self.headers)
                        print(params_routing)
                        logger.info('url_routing=%s\ndata_routing=%s\nheaders=%s\nparams_routing=%s' % (
                        url_routing, data_routing, self.headers, params_routing))

                        ############################################################
                        if self.not_upload == '1':
                            print('MES线尾仅上传箱子状态成功---receive: 测试用不上传MES')
                            logger.info('MES线尾仅上传箱子状态成功---receive: 测试用不上传MES')
                            if self.language == 'en':
                                message_list = self.cut('Uploaded AGV to MES TEST OK', 25)
                            else:
                                message_list = self.cut(
                                    '(测试模式)MES线尾仅上传箱子状态已上传:%s' % str(data_str).replace(" ", "").replace("\n",
                                                                                                                 ""),
                                    25)
                            message_str = ''
                            # message_str =  message_str
                            for i in range(len(message_list)):
                                if i == len(message_list) - 1:
                                    message_str = message_str + message_list[i]
                                else:
                                    message_str = message_str + message_list[i] + '\n'
                            # message_str =  message_str
                            self.label_keyinmessage.configure(foreground='green')
                            self.str_keyinmessage.set(message_str)
                            # routing_post = {'result': 'OK',
                            #                 'description': 'MES装箱上传成功---receive: 测试用不上传MES'}
                            # return routing_post
                        ############################################################
                        else:
                            try:
                                # logger.info('上传MES装箱数据--body：' + str(data_str))
                                # logger.info('上传MES装箱数据--params：' + str(params_routing))
                                receive = requests.request('POST', url_routing, data=data_str, headers=self.headers,
                                                           params=params_routing, timeout=20)
                                try:
                                    receive = json.loads(receive.text)
                                except:
                                    receive = eval(receive.text)
                                # print(receive['result'])
                                # print(receive['description'])
                                print('上传MES线尾仅上传箱子状态成功：' + str(receive))
                                logger.info('上传MES线尾仅上传箱子状态成功---receive: ' + str(receive))
                            except:
                                message = traceback.format_exc()
                                # print(message)
                                # logger.error(message)
                                print('上传MES线尾仅上传箱子状态失败!\n%s' % message)
                                logger.error('上传MES线尾仅上传箱子状态失败!\n%s' % message)
                                # message = traceback.format_exc()
                                # print(message)
                                # logger.info(message)
                                if self.language == 'en':
                                    message_list = self.cut('Uploaded AGV to MES Error', 25)
                                else:
                                    message_list = self.cut('MES线尾仅上传箱子状态上传失败', 25)
                                message_str = ''
                                # message_str =  message_str
                                for i in range(len(message_list)):
                                    if i == len(message_list) - 1:
                                        message_str = message_str + message_list[i]
                                    else:
                                        message_str = message_str + message_list[i] + '\n'
                                # message_str =  message_str
                                self.label_keyinmessage.configure(foreground='red')
                                self.str_keyinmessage.set(message_str)
                                # try:
                                #     self.messagewindow2 = tk.Toplevel(self.window)
                                #     self.messagewindow2.overrideredirect(True)
                                #     if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                                #         self.messagewindow2.geometry('+555+430')
                                #     elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                                #         self.messagewindow2.geometry('+555+430')
                                #     else:
                                #         self.messagewindow2.geometry('+680+609')
                                #     # 临时数据已清空, 可重新输入!
                                #     # The keyin data has been cleared. You can re-keyin!
                                #     if self.language == 'en':
                                #         msg2 = tk.Message(self.messagewindow2, text='Uploaded AGV to MES Error',
                                #                          font=('Arial', 25), fg='white', bg='#00c7a6',
                                #                          width=500)
                                #     else:
                                #         msg2 = tk.Message(self.messagewindow2, text='MES呼叫AGV小车上传失败',
                                #                          font=('Arial', 25), fg='white', bg='#00c7a6',
                                #                          width=500)
                                #     msg2.pack()
                                #     self.messagewindow2.after(2000, self.check_if_running, self.messagewindow2)
                                # except:
                                #     message = traceback.format_exc()
                                #     print(message)
                                #     logger.error(message)
                            else:
                                if self.language == 'en':
                                    message_list = self.cut('Uploaded AGV to MES', 25)
                                else:
                                    message_list = self.cut(
                                        'MES线尾仅上传箱子状态已上传:%s' % str(data_str).replace(" ", "").replace("\n", ""),
                                        25)
                                message_str = ''
                                # message_str =  message_str
                                for i in range(len(message_list)):
                                    if i == len(message_list) - 1:
                                        message_str = message_str + message_list[i]
                                    else:
                                        message_str = message_str + message_list[i] + '\n'
                                # message_str =  message_str
                                self.label_keyinmessage.configure(foreground='green')
                                self.str_keyinmessage.set(message_str)
                                # try:
                                #     self.messagewindow2 = tk.Toplevel(self.window)
                                #     self.messagewindow2.overrideredirect(True)
                                #     if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                                #         self.messagewindow2.geometry('+555+430')
                                #     elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                                #         self.messagewindow2.geometry('+555+430')
                                #     else:
                                #         self.messagewindow2.geometry('+680+609')
                                #     # 临时数据已清空, 可重新输入!
                                #     # The keyin data has been cleared. You can re-keyin!
                                #     if self.language == 'en':
                                #         msg2 = tk.Message(self.messagewindow2, text='Uploaded AGV to MES',
                                #                          font=('Arial', 25), fg='white', bg='#00c7a6',
                                #                          width=500)
                                #     else:
                                #         msg2 = tk.Message(self.messagewindow2, text='MES呼叫AGV小车已上传:%s' % data_str,
                                #                          font=('Arial', 25), fg='white', bg='#00c7a6',
                                #                          width=500)
                                #     msg2.pack()
                                #     self.messagewindow2.after(2000, self.check_if_running, self.messagewindow2)
                                # except:
                                #     message = traceback.format_exc()
                                #     print(message)
                                #     logger.error(message)

                        # self.side_status[number_new] = value_new

                    if str(self.only_sensor) != "1":
                        for z in range(len(container_signal_list)):
                            number_new = container_signal_list[z]
                            value_new = container_signal_value_list[z]
                            if self.side_status[number_new] == 0 and value_new == 1:
                                self.side_number[number_new] = 0
                                print('检测到收料箱%s已经重置，数据清空' % number_new)
                                logger.info('检测到收料箱%s已经重置，数据清空' % number_new)
                                if self.language == 'en':
                                    message_list = self.cut('%s is cleared' % self.side_name[number_new], 25)
                                else:
                                    message_list = self.cut('检测到收料箱%s已经重置，数据清空' % self.side_name[number_new], 25)
                                message_str = ''
                                for i in range(len(message_list)):
                                    if i == len(message_list) - 1:
                                        message_str = message_str + message_list[i]
                                    else:
                                        message_str = message_str + message_list[i] + '\n'
                                self.label_keyinmessage.configure(foreground='green')
                                self.str_keyinmessage.set(message_str)

                                self.side_status[number_new] = value_new
                                number_max = 1
                                for i in range(len(self.modelset_xls)):
                                    if str(self.modelset_xls[i]['机种型号']) == str(self.model):
                                        number_max = int(self.modelset_xls[i]['单箱数量'])
                                        break
                                    elif i == len(self.modelset_xls) - 1:
                                        print('model_set机种文件内无法查询到' + str(self.model) + '该机种!')
                                        logger.info('model_set机种文件内无法查询到' + str(self.model) + '该机种!')
                                        for j in range(len(self.modelset_xls) - 1, -1, -1):
                                            if str(self.modelset_xls[j]['机种型号']) == '其他':
                                                number_max = int(self.modelset_xls[j]['单箱数量'])
                                                print(
                                                    'model_set机种文件内无法查询到%s机种,自动使用其他型号配置%s' % (
                                                        self.model, number_max))
                                                logger.info(
                                                    'model_set机种文件内无法查询到%s机种,自动使用其他型号配置%s' % (
                                                        self.model, number_max))
                                                break
                                            elif j == 0:
                                                print('model_set机种文件内无法查询到' + str(
                                                    self.model) + '该机种!且找不到其他选项')
                                                logger.info('model_set机种文件内无法查询到' + str(
                                                    self.model) + '该机种!且找不到其他选项')

                                if int(self.side_number[self.side_name.index(self.side_now)]) >= number_max:
                                    for i in range(len(self.side_name)):
                                        if self.side_now != self.side_name[i] and int(self.side_number[i]) < number_max and \
                                                self.side_status[i] == 1:
                                            self.side_now = self.side_name[i]
                                            # 将压力阀打到对应的位置
                                            self.container_control(self.side_now)
                                            print('已更新出料口为：%s' % self.side_now)
                                            logger.info('已更新出料口为：%s' % self.side_now)
                                            if self.language == 'en':
                                                message_list = self.cut('Change OK', 25)
                                            else:
                                                message_list = self.cut('已更新出料口为：%s' % self.side_now, 25)
                                            message_str = ''
                                            for j in range(len(message_list)):
                                                if j == len(message_list) - 1:
                                                    message_str = message_str + message_list[j]
                                                else:
                                                    message_str = message_str + message_list[j] + '\n'
                                            self.label_keyinmessage.configure(foreground='green')
                                            self.str_keyinmessage.set(message_str)
                                            # try:
                                            #     self.messagewindow = tk.Toplevel(self.window)
                                            #     self.messagewindow.overrideredirect(True)
                                            #     if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                                            #         self.messagewindow.geometry('+555+430')
                                            #     elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                                            #         self.messagewindow.geometry('+555+430')
                                            #     else:
                                            #         self.messagewindow.geometry('+680+609')
                                            #     # 临时数据已清空, 可重新输入!
                                            #     # The keyin data has been cleared. You can re-keyin!
                                            #     if self.language == 'en':
                                            #         msg = tk.Message(self.messagewindow, text='Change OK',
                                            #                          font=('Arial', 25), fg='white', bg='#00c7a6',
                                            #                          width=500)
                                            #     else:
                                            #         msg = tk.Message(self.messagewindow,
                                            #                          text='已更新出料口为：%s' % self.side_now,
                                            #                          font=('Arial', 25), fg='white', bg='#00c7a6',
                                            #                          width=500)
                                            #     msg.pack()
                                            #     self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
                                            # except:
                                            #     message = traceback.format_exc()
                                            #     print(message)
                                            #     logger.error(message)
                                        elif i == len(self.side_name) - 1:
                                            print('无法找到未满的出料箱,故无法切换出料口')
                                            logger.info('无法找到未满的出料箱,故无法切换出料口')
                                            # self.container_control('OUT-PCS')

                                str_show = ''
                                for i in range(len(self.side_name)):
                                    str_show = str_show + ' ' + '%s=%s' % (self.side_name[i], self.side_number[i])
                                str_show = str_show + ' ' + 'Now=%s Max=%s' % (self.side_now, number_max)
                                self.str_container.set(str_show)
                            self.side_status[number_new] = value_new

                    print('收料箱最新状态=%s' % self.side_number)
                    logger.info('收料箱最新状态=%s' % self.side_number)
                else:
                    pass

    # 监控报警灯的状态信号
    def workthreading_light(self):
        while True:
            while True:
                if not self.light_status_signal.empty():
                    receive = self.light_status_signal.get()
                    print('监控到报警灯的状态信号')
                    print(receive)
                    light_signal_list = receive[0][1:-1].replace(' ', '').replace('"', '').replace("'", "").split(',')
                    light_signal_value_list = receive[1][1:-1].replace(' ', '').replace('"', '').replace("'", "").split(',')
                    logger.info('监控到报警灯的状态信号,light_signal_list=%s,light_signal_value_list=%s'%(light_signal_list,light_signal_value_list))
                    for j in range(len(light_signal_list)):
                        color_new = light_signal_list[j]
                        value_new = light_signal_value_list[j]
                        # if color_new == 'red':
                        #     self.light_status = 'red'
                        # elif color_new == 'yellow' and self.light_status not in ['red']:
                        #     self.light_status = 'yellow'
                        # elif color_new == 'green' and self.light_status not in ['yellow', 'red']:
                        #     self.light_status = 'green'
                        if color_new == 'red':
                            self.light_status = 'red'
                        elif color_new == 'yellow':
                            self.light_status = 'yellow'
                        elif color_new == 'green':
                            self.light_status = 'green'
                    print('报警灯最新状态=%s' % self.light_status)
                    # 轮替显示图片，并加入self.window.after()函数，达到加载不闪动的效果
                    if self.light_show_flag == 0:
                        self.light_show_flag = 1
                        self.window.after(200)

                        # 加载绿灯图
                        try:
                            if self.light_status == 'green':
                                self.img_light_green2 = Image.open(work_address + '/light_green.png')
                            else:
                                self.img_light_green2 = Image.open(work_address + '/light_default.png')
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.img_light_green2 = self.img_light_green2.resize((20, 20), Image.ANTIALIAS)
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.img_light_green2 = self.img_light_green2.resize((20, 20), Image.ANTIALIAS)
                        else:
                            self.img_light_green2 = self.img_light_green2.resize((20, 20), Image.ANTIALIAS)
                        self.img_light_green2 = ImageTk.PhotoImage(self.img_light_green2)
                        self.label_light_green['image'] = self.img_light_green2

                        # 加载黄灯图
                        try:
                            if self.light_status == 'yellow':
                                self.img_light_yellow2 = Image.open(work_address + '/light_yellow.png')
                            else:
                                self.img_light_yellow2 = Image.open(work_address + '/light_default.png')
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.img_light_yellow2 = self.img_light_yellow2.resize((20, 20), Image.ANTIALIAS)
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.img_light_yellow2 = self.img_light_yellow2.resize((20, 20), Image.ANTIALIAS)
                        else:
                            self.img_light_yellow2 = self.img_light_yellow2.resize((20, 20), Image.ANTIALIAS)
                        self.img_light_yellow2 = ImageTk.PhotoImage(self.img_light_yellow2)
                        self.label_light_yellow['image'] = self.img_light_yellow2

                        # 加载红灯图
                        try:
                            if self.light_status == 'red':
                                self.img_light_red2 = Image.open(work_address + '/light_red.png')
                            else:
                                self.img_light_red2 = Image.open(work_address + '/light_default.png')
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.img_light_red2 = self.img_light_red2.resize((20, 20), Image.ANTIALIAS)
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.img_light_red2 = self.img_light_red2.resize((20, 20), Image.ANTIALIAS)
                        else:
                            self.img_light_red2 = self.img_light_red2.resize((20, 20), Image.ANTIALIAS)
                        self.img_light_red2 = ImageTk.PhotoImage(self.img_light_red2)
                        self.label_light_red['image'] = self.img_light_red2
                    else:
                        self.light_show_flag = 0
                        self.window.after(200)
                        # 加载绿灯图
                        try:
                            if self.light_status == 'green':
                                self.img_light_green = Image.open(work_address + '/light_green.png')
                            else:
                                self.img_light_green = Image.open(work_address + '/light_default.png')
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.img_light_green = self.img_light_green.resize((20, 20), Image.ANTIALIAS)
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.img_light_green = self.img_light_green.resize((20, 20), Image.ANTIALIAS)
                        else:
                            self.img_light_green = self.img_light_green.resize((20, 20), Image.ANTIALIAS)
                        self.img_light_green = ImageTk.PhotoImage(self.img_light_green)
                        self.label_light_green['image'] = self.img_light_green

                        # 加载黄灯图
                        try:
                            if self.light_status == 'yellow':
                                self.img_light_yellow = Image.open(work_address + '/light_yellow.png')
                            else:
                                self.img_light_yellow = Image.open(work_address + '/light_default.png')
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.img_light_yellow = self.img_light_yellow.resize((20, 20), Image.ANTIALIAS)
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.img_light_yellow = self.img_light_yellow.resize((20, 20), Image.ANTIALIAS)
                        else:
                            self.img_light_yellow = self.img_light_yellow.resize((20, 20), Image.ANTIALIAS)
                        self.img_light_yellow = ImageTk.PhotoImage(self.img_light_yellow)
                        self.label_light_yellow['image'] = self.img_light_yellow

                        # 加载红灯图
                        try:
                            if self.light_status == 'red':
                                self.img_light_red = Image.open(work_address + '/light_red.png')
                            else:
                                self.img_light_red = Image.open(work_address + '/light_default.png')
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.img_light_red = self.img_light_red.resize((20, 20), Image.ANTIALIAS)
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.img_light_red = self.img_light_red.resize((20, 20), Image.ANTIALIAS)
                        else:
                            self.img_light_red = self.img_light_red.resize((20, 20), Image.ANTIALIAS)
                        self.img_light_red = ImageTk.PhotoImage(self.img_light_red)
                        self.label_light_red['image'] = self.img_light_red
                else:
                    pass

    # 运行CTCT模块
    def run_ctct(self):
        # 开启线程，连接CTCT，持续间隔时间发送心跳信号
        self.mythread_SubfromCTCT1 = threading.Thread(target=self.workthreading_SubfromCTCT1, name='thread_SubfromCTCT',
                                                      daemon=True)
        self.mythread_SubfromCTCT1.start()

    # 用于添加插入历史信息记录列表
    def tree_set(self, code):
        self.tree_history.insert('', 0,
                                 values=[code['pro_code'], str(datetime.datetime.now())[:19], code['err_meaning'],
                                         code['result'], 'OK', code['updated'], '无'])

    # 用于切割字符串
    def cut(self, obj, sec):
        return [obj[i:i + sec] for i in range(0, len(obj), sec)]

    # 绘制圆饼图
    def yuanbingtu(self, name_number_result):
        try:
            name_list, number_list, result_list = name_number_result.get()
            colors = ['darkviolet', 'darkgrey', 'darksalmon',
                      'darkturquoise', 'darkslateblue', 'darkorchid', 'darkcyan',
                      'darkblue', 'darkmagenta', 'darkolivegreen', 'darkslategrey',
                      'darkgray', 'darkgoldenrod', 'darkkhaki', 'darkslategray',
                      'cyan', 'lightpink', 'moccasin', 'lightyellow', 'blanchedalmond',
                      'gainsboro', 'mistyrose', 'wheat', 'lightcoral', 'grey', 'dodgerblue',
                      'lightgrey', 'antiquewhite', 'peru', 'lavender', 'violet',
                      'dimgrey', 'peachpuff', 'sandybrown', 'aqua', 'lightseagreen',
                      'orchid', 'mediumblue', 'dimgray', 'slategrey', 'orange',
                      'teal', 'sienna', 'plum', 'lightcyan', 'mediumturquoise',
                      'navy', 'lightskyblue', 'lightgray', 'paleturquoise', 'seashell',
                      'beige', 'honeydew', 'slateblue', 'papayawhip', 'slategray',
                      'rosybrown', 'lightslategrey', 'chocolate', 'palegoldenrod',
                      'silver', 'aquamarine', 'gold', 'navajowhite', 'burlywood',
                      'lightsalmon', 'royalblue', 'lavenderblush', 'bisque', 'cornflowerblue',
                      'mediumorchid', 'oldlace', 'lightblue', 'thistle', 'khaki',
                      'goldenrod', 'deepskyblue', 'linen', 'saddlebrown', 'cadetblue',
                      'cornsilk', 'midnightblue', 'rebeccapurple', 'yellow', 'blueviolet',
                      'ivory', 'salmon', 'mediumaquamarine', 'lightsteelblue',
                      'pink', 'turquoise', 'indigo', 'lightslategray', 'olive', 'coral',
                      'lightgoldenrodyellow', 'tan', 'skyblue', 'purple', 'blue', 'steelblue',
                      'powderblue', 'lemonchiffon', 'mediumslateblue', 'mediumpurple']
            yuanbing_name = []
            yuanbing_number = []
            zero_list = [1]
            number_all_list = [0, 0, 0]
            colors_all_list = ['#afafaf', '#00c7a6', 'red']
            name_all_list = ['未完成', '良品', '不良品']

            err_name = []
            err_number = []
            for i in range(len(name_list)):
                if result_list[i] == 'FAIL':
                    number_all_list[2] = number_all_list[2] + number_list[i]
                    yuanbing_name.append(deepcopy(name_list[i]))
                    yuanbing_number.append(deepcopy(number_list[i]))
                    err_name.append(deepcopy(name_list[i]))
                    err_number.append(deepcopy(number_list[i]))
            for i in range(len(name_list)):
                if result_list[i] == 'PASS':
                    number_all_list[1] = number_all_list[1] + number_list[i]
                    yuanbing_name.append(deepcopy(name_list[i]))
                    yuanbing_number.append(deepcopy(number_list[i]))

            number_all_list[0] = self.all_number - number_all_list[1]
            if number_all_list[0] < 0:
                number_all_list[0] = 0

            percent_none = '{:.0f}'.format(float(number_all_list[0]))
            percent_pass = '{:.0f}'.format(float(number_all_list[1]))
            percent_fail = '{:.0f}'.format(float(number_all_list[2]))

            if self.language == 'en':
                str_pass = 'Pass:' + str(percent_pass) + ' pcs'
                str_fail = 'Fail:' + str(percent_fail) + ' pcs'
                str_none = 'Unfinished:' + str(percent_none) + ' pcs'
            else:
                str_pass = '良品数:' + str(percent_pass) + ' 个'
                str_fail = '不良数:' + str(percent_fail) + ' 个'
                str_none = '未完成:' + str(percent_none) + ' 个'
            font_set = FontProperties(fname=work_address + '/msyh.ttf')

            fig, ax = plt.subplots(1, 1)

            plt.style.use('ggplot')

            plt.pie(number_all_list, radius=1.2, colors=colors_all_list, startangle=90,
                    textprops={'fontproperties': font_set}, wedgeprops={'linewidth': 0.4, 'edgecolor': 'black'})

            try:
                try:
                    passx = yuanbing_name.index('良品')
                except:
                    message = traceback.format_exc()
                    print(message)
                    logger.error(message)
                    try:
                        passx = yuanbing_name.index('PASS')
                    except:
                        message = traceback.format_exc()
                        print(message)
                        logger.error(message)
                        try:
                            passx = yuanbing_name.index('Pass')
                        except:
                            try:
                                message = traceback.format_exc()
                                print(message)
                                logger.error(message)
                                passx = yuanbing_name.index('pass')
                            except:
                                message = traceback.format_exc()
                                print(message)
                                logger.error(message)
                                raise
                yuanbing_name[0], yuanbing_name[passx] = yuanbing_name[passx], yuanbing_name[0]
                yuanbing_number[0], yuanbing_number[passx] = yuanbing_number[passx], yuanbing_number[0]
                colors_yuanbing = ['#00c7a6'] + colors
                if len(yuanbing_name) > 2:
                    for i in range(len(yuanbing_name) - 2):
                        yuanbing_name[len(yuanbing_name) - (i + 2)], yuanbing_name[len(yuanbing_name) - (i + 1)] = \
                            yuanbing_name[len(yuanbing_name) - (i + 1)], yuanbing_name[len(yuanbing_name) - (i + 2)]
                        yuanbing_number[len(yuanbing_name) - (i + 2)], yuanbing_number[len(yuanbing_name) - (i + 1)] = \
                            yuanbing_number[len(yuanbing_name) - (i + 1)], yuanbing_number[len(yuanbing_name) - (i + 2)]

                none_number = self.all_number - yuanbing_number[0]
                # print('self.all_number')
                # print(self.all_number)
                # print(yuanbing_number[0])
                # print(none_number)
                if none_number < 0:
                    none_number = 0
                yuanbing_name.insert(0, '未完成')
                yuanbing_number.insert(0, none_number)
                colors_yuanbing.insert(0, '#afafaf')

                plt.pie(yuanbing_number, radius=1.2, autopct='', pctdistance=0.8, colors=colors_yuanbing,
                        startangle=90, textprops={'fontproperties': font_set},
                        wedgeprops={'linewidth': 0.4, 'edgecolor': 'black'})
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
                colors_yuanbing = colors
                none_number = self.all_number - yuanbing_number[0]
                if none_number < 0:
                    none_number = 0
                yuanbing_name.insert(0, '未完成')
                yuanbing_number.insert(0, none_number)
                colors_yuanbing.insert(0, '#afafaf')
                plt.pie(yuanbing_number, radius=1.2, autopct='%1.1f%%', pctdistance=0.8, colors=colors_yuanbing,
                        startangle=90, textprops={'fontproperties': font_set},
                        wedgeprops={'linewidth': 0.4, 'edgecolor': 'black'})

            plt.pie(zero_list, radius=0.25, colors='white', wedgeprops={'linewidth': 0.4, 'edgecolor': 'black'})
            plt.pie(number_all_list, radius=0.2, colors=colors_all_list, autopct='%.2f%%', pctdistance=6.3,
                    textprops={'fontproperties': font_set}, startangle=90,
                    wedgeprops={'linewidth': 0.4, 'edgecolor': 'black'})
            plt.pie(zero_list, radius=0.17, colors='white', wedgeprops={'linewidth': 0.4, 'edgecolor': 'black'})
            plt.axis('equal')

            plt.legend([str_none, str_pass, str_fail], ncol=1, loc=(0.80, -0.05), prop=font_set)

            plt.figure(num=1, figsize=(12, 8))
            plt.savefig(work_address + '/yuanbingtu.jpg')
        except:
            message = traceback.format_exc()
            print(message)
            logger.error(message)

    # 绘制直方图
    def zhifangtu(self, name_number):
        try:
            name_list, number_list = name_number.get()
            font_set = FontProperties(fname=work_address + '/msyh.ttf')

            meaning_list = []
            x_list = []
            y_list = []
            for i in range(len(self.result_xls)):
                if self.result_xls[i]['Meaning'] != '良品' and self.result_xls[i]['Meaning'] != 'PASS' and \
                        self.result_xls[i]['Meaning'] != 'pass' and self.result_xls[i]['Meaning'] != 'Pass':
                    x_list.append(self.result_xls[i]['Code'])
                    meaning_list.append(self.result_xls[i]['Meaning'])
                    y_list.append(0)

            for i in range(len(x_list)):
                for j in range(len(name_list)):
                    if name_list[j] == meaning_list[i]:
                        y_list[i] = number_list[j]

            rect1 = plt.bar(range(len(x_list)), y_list, color=['#009dd4' for i in range(len(x_list))])
            plt.xticks(range(len(x_list)), x_list, fontproperties=font_set)
            plt.legend()
            plt.grid(alpha=0.5)  # 添加网格线
            for r1 in rect1:
                h01 = r1.get_height()
                plt.text(r1.get_x() + r1.get_width() / 2, h01, str(h01), size=13, ha='center', va='bottom')
            if self.language == 'en':
                plt.ylabel('Number', fontproperties=font_set)
                plt.xlabel('Code', fontproperties=font_set)
            else:
                plt.ylabel('数量', fontproperties=font_set)
                plt.xlabel('不良码', fontproperties=font_set)
            plt.figure(num=1, figsize=(12, 8))
            plt.savefig(work_address + '/zhifangtu.jpg')
        except:
            message = traceback.format_exc()
            print(message)
            logger.error(message)

    # md5加密使用
    def keymd5(self, src):
        str_md5 = hashlib.md5(src.encode('utf-8')).hexdigest()
        str_md5 = str_md5.upper()
        return str_md5

    # MES途程更新及测试数据上传接口
    def routing(self):
        # 将body的数据转换为字符串格式
        data_str = json.dumps(self.data_routing, sort_keys=False)
        # 字符串拼接md5加密
        src = self.secretkey + data_str
        md5 = self.keymd5(src)
        self.params_routing['sign'] = md5
        # 发送数据并接收返回数据
        print(self.url_routing)
        print(self.data_routing)
        print(self.headers)
        print(self.params_routing)
        logger.info('url_routing=%s\ndata_routing=%s\nheaders=%s\nparams_routing=%s' % (
            self.url_routing, self.data_routing, self.headers, self.params_routing))
        ############################################################
        if self.not_upload == '1' or self.only_pqm == '1' or self.only_sensor == '1':
            if self.data_routing['testType'] != 'GET_STATION_QTY':
                print('MES上传成功---receive: 测试用（或特定模式）不上传MES')
                logger.info('MES上传成功---receive: 测试用（或特定模式）不上传MES')
                routing_post = {'result': 'OK',
                                'description': 'MES上传成功---receive: 测试用（或特定模式）不上传MES'}
                return routing_post
        ############################################################
        else:
            try:
                # logger.info('上传MES数据--body：' + str(data_str))
                # logger.info('上传MES数据--params：' + str(self.params_routing))
                receive = requests.request('POST', self.url_routing, data=data_str, headers=self.headers,
                                           params=self.params_routing, timeout=20)
                try:
                    receive = json.loads(receive.text)
                except:
                    receive = eval(receive.text)
                # print(receive['result'])
                # print(receive['description'])
                print('上传MES成功：' + str(receive))
                logger.info('上传MES成功---receive: ' + str(receive))

                if receive['result'] != 'OK':
                    if receive['description'] == 'NO DATA':
                        if self.all_number != 0:
                            self.CheckState_returnCTCT('', self.all_number, self.pass_number, self.err_number,
                                                       '该工单无计划数!')
                        else:
                            self.CheckState_returnCTCT('', self.pass_number + self.err_number, self.pass_number,
                                                       self.err_number, '该工单无计划数!')
                    else:
                        if self.all_number != 0:
                            self.CheckState_returnCTCT('', self.all_number, self.pass_number, self.err_number,
                                                       receive['description'])
                        else:
                            self.CheckState_returnCTCT('', self.pass_number + self.err_number, self.pass_number,
                                                       self.err_number, receive['description'])
                return receive
            except:
                message = traceback.format_exc()
                print(message)
                print('上传MES失败!')
                logger.error('上传MES失败!\n%s' % message)

                if self.all_number != 0:
                    self.CheckState_returnCTCT('', self.all_number, self.pass_number, self.err_number, 'MES上传失败!')
                else:
                    self.CheckState_returnCTCT('', self.pass_number + self.err_number, self.pass_number,
                                               self.err_number, 'MES上传失败!')

                return {'result': 'FAIL', 'description': 'MES上传失败!'}

    # MES查询达成数目接口
    def checknumber(self):
        testdata = []
        self.data_routing['factory'] = self.usr_factory
        self.data_routing['testType'] = 'GET_STATION_QTY'
        self.data_routing[
            'routingData'] = self.mo + '}' + self.model + '}' + self.line + '}' + self.section + '}' + self.group + '}' + self.station + '}'
        self.data_routing['testData'] = testdata
        routing_post = self.routing()
        try:
            if routing_post['result'] == 'OK':
                try:
                    receive_list = routing_post['description'].split('}')
                    all_number = int(receive_list[len(receive_list) - 4])
                    pass_number = int(receive_list[len(receive_list) - 3])
                    err_number = int(receive_list[len(receive_list) - 2])
                    return all_number, pass_number, err_number
                except:
                    message = traceback.format_exc()
                    print(message)
                    # logger.error(message)
                    print('解析失败：工单达成数信息')
                    logger.error('解析失败：工单达成数信息\n%s'%message)
                    return 0, 0, 0
            else:
                print('获取MES该工单的达成数失败!')
                logger.error('获取MES该工单的达成数失败!')
                return 0, 0, 0
        except:
            print('获取MES该工单的达成数失败!')
            logger.error('获取MES该工单的达成数失败!')
            return 0, 0, 0

    # 每隔5秒自动刷新图片及保存历史数据
    def workthreading_RefreshPicture(self):
        old_history_len = 0
        while True:
            # 倒计时5秒
            while True:
                try:
                    for i in range(6):
                        self.str_flash_clock.set(str(5 - int(i)) + 's')
                        time.sleep(1)
                    break
                except:
                    message = traceback.format_exc()
                    print(message)
                    logger.error(message)
                    time.sleep(1)

            # 开始绘制图片
            if (old_history_len != len(self.history_xls) and len(self.image_name) > 0 and len(
                    self.image_number) > 0 and len(self.image_result) > 0) or self.first_upload:
                self.first_upload = False
                old_history_len = len(self.history_xls)
                # 获取MES的达成率数据
                self.all_number, self.pass_number, self.err_number = self.checknumber()
                if self.all_number != 0:
                    self.CheckState_returnCTCT('', self.all_number, self.pass_number, self.err_number, 'none')
                else:
                    self.CheckState_returnCTCT('', self.pass_number + self.err_number, self.pass_number,
                                               self.err_number, '该工单无计划数!')
                if self.pass_number + self.err_number > self.all_number:
                    self.all_number = self.pass_number + self.err_number
                # 检查是否达成率获取成功
                if self.all_number == 0 and self.pass_number == 0 and self.err_number == 0:
                    # 显示获取失败
                    self.label_finish.configure(fg='red')
                    self.label_pass.configure(fg='red')
                    self.str_finish.set('Fail')
                    self.str_pass.set('Fail')
                    nowtime = str(datetime.datetime.now())[:19]
                    self.lineData_list = []
                    lineData = []
                    lineData.append('')
                    lineData.append(nowtime)
                    lineData.append('')
                    lineData.append('2')
                    lineData.append('达成')
                    lineData.append('')
                    lineData.append('')
                    lineData.append('')
                    if self.language == 'en':
                        lineData.append(str('Fail to get'))
                    else:
                        lineData.append(str('获取失败'))
                    lineData.append('')
                    lineData.append('')
                    self.lineData_list.append(deepcopy(lineData))
                    lineData = []
                    lineData.append('')
                    lineData.append(nowtime)
                    lineData.append('')
                    lineData.append('2')
                    lineData.append('良率')
                    lineData.append('')
                    lineData.append('')
                    lineData.append('')
                    if self.language == 'en':
                        lineData.append(str('Fail to get'))
                    else:
                        lineData.append(str('获取失败'))
                    lineData.append('')
                    lineData.append('')
                    self.lineData_list.append(deepcopy(lineData))
                    lineData = []
                    num = 0
                    if self.firstcode != '':
                        if self.firstcode.find('+') != -1:
                            num = len(self.firstcode.split('+'))
                        else:
                            num = len(self.firstcode.split(','))
                    lineData.append('')
                    lineData.append(nowtime)
                    lineData.append('')
                    lineData.append('2')
                    lineData.append('keyin')
                    lineData.append('')
                    lineData.append('')
                    lineData.append('')
                    lineData.append(str(num))
                    lineData.append('')
                    lineData.append('')
                    self.lineData_list.append(deepcopy(lineData))
                    lineData = []
                    lineData.append('')
                    lineData.append(nowtime)
                    lineData.append('')
                    lineData.append('2')
                    lineData.append('产量')
                    lineData.append('')
                    lineData.append('')
                    lineData.append('')
                    lineData.append(str(self.chanliang))
                    lineData.append('')
                    lineData.append('')
                    self.lineData_list.append(deepcopy(lineData))
                    lineData = []
                    lineData.append('')
                    lineData.append(nowtime)
                    lineData.append('')
                    lineData.append('2')
                    lineData.append('IP')
                    lineData.append('')
                    lineData.append('')
                    lineData.append('')
                    lineData.append(str(self.userIP))
                    lineData.append('')
                    lineData.append('')
                    self.lineData_list.append(deepcopy(lineData))
                    self.baseInfor = self.mo + '}' + self.model + '}' + self.line + '}' + self.section + '}' + self.group + '}' + self.station
                    self.CheckState_returnCTCT(self.lineData_list, self.all_number, self.pass_number, self.err_number,
                                               'none')
                else:
                    self.label_finish.configure(fg='#FFFFFF')
                    self.label_pass.configure(fg='#FFFFFF')
                    finish_percent = '{:.2f}'.format(
                        float(100 * float(self.pass_number) / (float(self.all_number))))
                    pass_percent = '{:.2f}'.format(
                        float(100 * float(self.pass_number) / (float(self.pass_number) + float(self.err_number))))
                    self.str_finish.set(str(finish_percent) + '%')
                    self.str_pass.set(str(pass_percent) + '%')

                    nowtime = str(datetime.datetime.now())[:19]
                    self.lineData_list = []
                    lineData = []
                    lineData.append('')
                    lineData.append(nowtime)
                    lineData.append('')
                    lineData.append('2')
                    lineData.append('达成')
                    lineData.append('')
                    lineData.append('100')
                    lineData.append('0')
                    lineData.append(str(finish_percent))
                    lineData.append('')
                    lineData.append('')
                    self.lineData_list.append(deepcopy(lineData))
                    lineData = []
                    lineData.append('')
                    lineData.append(nowtime)
                    lineData.append('')
                    lineData.append('2')
                    lineData.append('良率')
                    lineData.append('')
                    lineData.append('100')
                    lineData.append('0')
                    lineData.append(str(pass_percent))
                    lineData.append('')
                    lineData.append('')
                    self.lineData_list.append(deepcopy(lineData))
                    lineData = []
                    num = 0
                    if self.firstcode != '':
                        if self.firstcode.find('+') != -1:
                            num = len(self.firstcode.split('+'))
                        else:
                            num = len(self.firstcode.split(','))
                    lineData.append('')
                    lineData.append(nowtime)
                    lineData.append('')
                    lineData.append('2')
                    lineData.append('keyin')
                    lineData.append('')
                    lineData.append('')
                    lineData.append('')
                    lineData.append(str(num))
                    lineData.append('')
                    lineData.append('')
                    self.lineData_list.append(deepcopy(lineData))
                    lineData = []
                    lineData.append('')
                    lineData.append(nowtime)
                    lineData.append('')
                    lineData.append('2')
                    lineData.append('产量')
                    lineData.append('')
                    lineData.append('')
                    lineData.append('')
                    lineData.append(str(self.chanliang))
                    lineData.append('')
                    lineData.append('')
                    self.lineData_list.append(deepcopy(lineData))
                    lineData = []
                    lineData.append('')
                    lineData.append(nowtime)
                    lineData.append('')
                    lineData.append('2')
                    lineData.append('IP')
                    lineData.append('')
                    lineData.append('')
                    lineData.append('')
                    lineData.append(str(self.userIP))
                    lineData.append('')
                    lineData.append('')
                    self.lineData_list.append(deepcopy(lineData))
                    self.CheckState_returnCTCT(self.lineData_list, self.all_number, self.pass_number, self.err_number,
                                               'none')

                if len(self.image_name) > 0:
                    # 准备绘图
                    name_list = deepcopy(self.image_name)
                    number_list = deepcopy(self.image_number)
                    result_list = deepcopy(self.image_result)
                    name_number_result_list = (name_list, number_list, result_list)
                    name_number_list = (name_list, number_list)
                    name_number = mp.Queue()
                    name_number.put(name_number_list)
                    name_number_result = mp.Queue()
                    name_number_result.put(name_number_result_list)
                    # 開其他進程進行繪圖工作
                    myprocess_draw_zhifangtu = mp.Process(target=self.zhifangtu, args=(name_number,))
                    myprocess_draw_zhifangtu.start()
                    myprocess_draw_yuanbingtu = mp.Process(target=self.yuanbingtu, args=(name_number_result,))
                    myprocess_draw_yuanbingtu.start()
                    # 本地保存历史数据
                    try:
                        fileAddress = work_address + '/history/' + str(self.mo) + ' + ' + str(self.station) + '.csv'
                        if len(self.history_xls) > 10:
                            pf = pd.DataFrame([self.history_xls[-1]])
                            pf.to_csv(fileAddress, mode='a', index=False, header=False)
                        else:
                            pf = pd.DataFrame(self.history_xls)
                            pf.to_csv(fileAddress, index=False, header=True)
                        # if os.path.exists(fileAddress):
                        #     SaveData = pd.DataFrame([self.history_xls[-1]])
                        #     SaveData.to_csv(fileAddress, mode='a', index=False, header=False)
                        # else:
                        #     SaveData = pd.DataFrame(self.history_xls)
                        #     SaveData.to_csv(fileAddress, index=False, header=True)
                    except:
                        message = traceback.format_exc()
                        print(message)
                        # logger.error(message)
                        print('尝试保存历史文件失败，请查看文件是否损坏!')
                        logger.error('尝试保存历史文件失败，请查看文件是否损坏!\n%s'%message+'\n历史文件地址: ' + work_address + '/history/' + str(self.mo) + ' + ' + str(
                            self.station) + '.csv')

                    # 等待绘图工作完成
                    myprocess_draw_zhifangtu.join()
                    myprocess_draw_yuanbingtu.join()
                    # 轮替显示图片，并加入self.window.after()函数，达到加载不闪动的效果
                    if self.now_pic == 0:
                        self.now_pic = 1
                        self.window.after(200)
                        try:
                            self.img_yuanbing2 = Image.open(work_address + '/yuanbingtu.jpg')
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                            self.img_yuanbing2 = Image.open(work_address + '/yuanbingtu_backup.jpg')

                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.img_yuanbing2 = self.img_yuanbing2.resize((419, 274), Image.ANTIALIAS)
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.img_yuanbing2 = self.img_yuanbing2.resize((419, 274), Image.ANTIALIAS)
                        else:
                            self.img_yuanbing2 = self.img_yuanbing2.resize((525, 368), Image.ANTIALIAS)
                        self.img_yuanbing2 = ImageTk.PhotoImage(self.img_yuanbing2)
                        try:
                            self.img_zhifang2 = Image.open(work_address + '/zhifangtu.jpg')
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                            self.img_zhifang2 = Image.open(work_address + '/zhifangtu_backup.jpg')
                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.img_zhifang2 = self.img_zhifang2.resize((419, 274), Image.ANTIALIAS)
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.img_zhifang2 = self.img_zhifang2.resize((419, 274), Image.ANTIALIAS)
                        else:
                            self.img_zhifang2 = self.img_zhifang2.resize((525, 368), Image.ANTIALIAS)
                        self.img_zhifang2 = ImageTk.PhotoImage(self.img_zhifang2)
                        self.label_yuanbing['image'] = self.img_yuanbing2
                        self.label_zhifang['image'] = self.img_zhifang2
                    else:
                        self.now_pic = 0
                        self.window.after(200)
                        try:
                            self.img_yuanbing = Image.open(work_address + '/yuanbingtu.jpg')
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                            self.img_yuanbing = Image.open(work_address + '/yuanbingtu_backup.jpg')
                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.img_yuanbing = self.img_yuanbing.resize((419, 274), Image.ANTIALIAS)
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.img_yuanbing = self.img_yuanbing.resize((419, 274), Image.ANTIALIAS)
                        else:
                            self.img_yuanbing = self.img_yuanbing.resize((525, 368), Image.ANTIALIAS)
                        self.img_yuanbing = ImageTk.PhotoImage(self.img_yuanbing)
                        try:
                            self.img_zhifang = Image.open(work_address + '/zhifangtu.jpg')
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                            self.img_zhifang = Image.open(work_address + '/zhifangtu_backup.jpg')
                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.img_zhifang = self.img_zhifang.resize((419, 274), Image.ANTIALIAS)
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.img_zhifang = self.img_zhifang.resize((419, 274), Image.ANTIALIAS)
                        else:
                            self.img_zhifang = self.img_zhifang.resize((525, 368), Image.ANTIALIAS)
                        self.img_zhifang = ImageTk.PhotoImage(self.img_zhifang)
                        self.label_yuanbing['image'] = self.img_yuanbing
                        self.label_zhifang['image'] = self.img_zhifang

    # 上传新数据到MES
    def upload_data(self, number):
        # 轮询err_code清单
        self.code['err_code_mes'] = ''
        for i in range(len(self.result_xls)):
            if str(self.code['err_code']) == str(self.result_xls[i]['Code']):
                if str(self.result_xls[i]['ErrorCode']) != 'nan':
                    self.code['err_code_mes'] = str(self.result_xls[i]['ErrorCode'])
                else:
                    self.code['err_code_mes'] = ''
                self.code['err_meaning'] = self.result_xls[i]['Meaning']
                self.code['result'] = self.result_xls[i]['Result']
                break

        # 封装上传数据
        testdata = []
        testdata.append(self.code['err_meaning'])
        testdata.append(str(datetime.datetime.now())[:19])
        testdata.append('O')
        testdata.append('检测结果')
        testdata.append('')
        testdata.append('')
        testdata.append('')
        if self.code['result'] == 'PASS':
            testdata.append(0)
        else:
            testdata.append(1)
        testdata.append(self.code['result'])
        testdata.append('')
        testdata.append(str(datetime.datetime.now())[:19])
        if self.code['result'] == 'PASS':
            testdata.append(0)
        else:
            testdata.append(1)
        self.data_routing['testData'].append(testdata)

        testdata = []
        testdata.append('测试机器编号')
        testdata.append(str(datetime.datetime.now())[:19])
        testdata.append('O')
        testdata.append('测试机器编号')
        testdata.append('')
        testdata.append('')
        testdata.append('')
        testdata.append('PASS')
        testdata.append(self.shebei_bianma[0])
        testdata.append('')
        testdata.append(str(datetime.datetime.now())[:19])
        testdata.append('PASS')
        self.data_routing['testData'].append(testdata)

        # 开始上传
        # 有条码
        if self.youtiaoma == '1':
            # 非首站
            if self.feishouzhan == '1':
                self.data_routing['testType'] = 'ROUTING_UPDATE'
                # = 0则为OK，= 1则为FAIL
                if self.data_routing['testData'][0][7] == 0:
                    # self.data_routing['routingData'] = str(
                    #     self.procode + '}' + self.mo + '}' + self.model + '}' + self.line + '}' + self.section + '}' + self.group + '}' + self.station + '}' + '' + '}' + 'PASS' + '}' + self.usr_id + '}' + '' + '}' + '' + '}' + '' + '}')
                    self.data_routing['routingData'] = str(
                                                            self.procode+'}'
                                                            +self.mo+'}'
                                                            +self.model+'}'
                                                            +self.line+'}'
                                                            +self.section+'}'
                                                            +self.group+'}'
                                                            +self.station+'}'
                                                            +''+'}'
                                                            +'PASS'+'}'
                                                            +self.usr_id+'}'
                                                            +str(self.shebei_bianma[0])+'}'
                                                            +self.ctct_software+'}'
                                                            +''+'}'
                                                            +self.ctct_software+'}'
                                                            +self.ctct_version+'}'
                                                            +''+'}'
                                                            +''+'}'
                                                            +''+'}'
                                                            +''+'}'
                                                            )
                else:
                    # self.data_routing['routingData'] = str(
                    #     self.procode + '}' + self.mo + '}' + self.model + '}' + self.line + '}' + self.section + '}' + self.group + '}' + self.station + '}' +
                    #     self.code['err_code_mes'] + '}' + 'FAIL' + '}' + self.usr_id + '}' + '' + '}' + '' + '}' + '' + '}')
                    self.data_routing['routingData'] = str(
                        self.procode + '}'
                        + self.mo + '}'
                        + self.model + '}'
                        + self.line + '}'
                        + self.section + '}'
                        + self.group + '}'
                        + self.station + '}'
                        + self.code['err_code_mes'] + '}'
                        + 'FAIL' + '}'
                        + self.usr_id + '}'
                        + str(self.shebei_bianma[0]) + '}'
                        + self.ctct_software + '}'
                        + self.code['err_meaning'] + '}'
                        + self.ctct_software + '}'
                        + self.ctct_version + '}'
                        + '' + '}'
                        + '' + '}'
                        + '' + '}'
                        + '' + '}'
                    )
            # 首站
            else:
                self.data_routing['testType'] = 'ROUTING_INPUT'
                # = 0则为OK，= 1则为FAIL
                if self.data_routing['testData'][0][7] == 0:
                    # self.data_routing['routingData'] = str(
                    #     self.procode + '}' + self.mo + '}' + self.model + '}' + self.line + '}' + self.section + '}' + self.group + '}' + self.station + '}'
                    #     + '' + '}' + 'PASS' + '}' + self.usr_id + '}' + self.procode[:2] + '}' + str(len(self.procode)) + '}')
                    self.data_routing['routingData'] = str(
                        self.procode + '}'
                        + self.mo + '}'
                        + self.model + '}'
                        + self.line + '}'
                        + self.section + '}'
                        + self.group + '}'
                        + self.station + '}'
                        + '' + '}'
                        + 'PASS' + '}'
                        + self.usr_id + '}'
                        + self.procode[:2] + '}'
                        + str(len(self.procode)) + '}'
                        + str(self.shebei_bianma[0]) + '}'
                        + self.ctct_software + '}'
                        + self.ctct_version + '}'
                        + '' + '}'
                        + '' + '}'
                        + '' + '}'
                        + '' + '}'
                    )

                else:
                    # self.data_routing['routingData'] = str(
                    #     self.procode + '}' + self.mo + '}' + self.model + '}' + self.line + '}' + self.section + '}' + self.group + '}' + self.station + '}' +
                    #     self.code['err_code_mes'] + '}' + 'FAIL' + '}' + self.usr_id + '}' + self.procode[
                    #                                                                          :2] + '}' + str(
                    #         len(self.procode)) + '}')
                    self.data_routing['routingData'] = str(
                        self.procode + '}'
                        + self.mo + '}'
                        + self.model + '}'
                        + self.line + '}'
                        + self.section + '}'
                        + self.group + '}'
                        + self.station + '}'
                        + self.code['err_code_mes'] + '}'
                        + 'FAIL' + '}'
                        + self.usr_id + '}'
                        + self.procode[:2] + '}'
                        + str(len(self.procode)) + '}'
                        + str(self.shebei_bianma[0]) + '}'
                        + self.ctct_software + '}'
                        + self.ctct_version + '}'
                        + '' + '}'
                        + '' + '}'
                        + '' + '}'
                        + '' + '}'
                    )
        # 随机条码
        else:
            self.data_routing['testType'] = 'NO_ROUTE'
            # 是否需要批量上传
            if number == 1:
                # = 0则为OK，= 1则为FAIL
                # print('self.data_routing[testData][0][7]')
                # print(self.data_routing['testData'])
                if self.data_routing['testData'][0][7] == 0:
                    # self.data_routing['routingData'] = str(
                    #     self.procode + '}' + self.mo + '}' + self.model + '}' + self.line + '}' + self.section + '}' + self.group + '}' + self.station + '}' +
                    #     '' + '}' + 'PASS' + '}' + self.usr_id + '}')
                    self.data_routing['routingData'] = str(
                                                            self.procode+'}'
                                                            +self.mo+'}'
                                                            +self.model+'}'
                                                            +self.line+'}'
                                                            +self.section+'}'
                                                            +self.group+'}'
                                                            +self.station+'}'
                                                            +''+'}'
                                                            +'PASS'+'}'
                                                            +self.usr_id+'}'
                                                            +''+'}'
                                                            +''+'}'
                                                            +str(self.shebei_bianma[0])+'}'
                                                            +self.ctct_software+'}'
                                                            +self.ctct_version+'}'
                                                            +''+'}'
                                                            +''+'}'
                                                            +''+'}'
                                                            +''+'}'
                                                            )
                else:
                    # self.data_routing['routingData'] = str(
                    #     self.procode + '}' + self.mo + '}' + self.model + '}' + self.line + '}' + self.section + '}' + self.group + '}' + self.station + '}' +
                    #     self.code['err_code_mes'] + '}' + 'FAIL' + '}' + self.usr_id + '}')
                    self.data_routing['routingData'] = str(
                        self.procode + '}'
                        + self.mo + '}'
                        + self.model + '}'
                        + self.line + '}'
                        + self.section + '}'
                        + self.group + '}'
                        + self.station + '}'
                        + self.code['err_code_mes'] + '}'
                        + 'FAIL' + '}'
                        + self.usr_id + '}'
                        + '' + '}'
                        + '' + '}'
                        + str(self.shebei_bianma[0]) + '}'
                        + self.ctct_software + '}'
                        + self.ctct_version + '}'
                        + '' + '}'
                        + '' + '}'
                        + '' + '}'
                        + '' + '}'
                    )
            else:
                # = 0则为OK，= 1则为FAIL
                if self.data_routing['testData'][0][7] == 0:
                    # self.data_routing['routingData'] = str(
                    #     self.procode + '}' + self.mo + '}' + self.model + '}' + self.line + '}' + self.section + '}' + self.group + '}' + self.station + '}' +
                    #     '' + '}' + 'PASS' + '}' + self.usr_id + '}' + str(number) + '}')
                    self.data_routing['routingData'] = str(
                        self.procode + '}'
                        + self.mo + '}'
                        + self.model + '}'
                        + self.line + '}'
                        + self.section + '}'
                        + self.group + '}'
                        + self.station + '}'
                        + '' + '}'
                        + 'PASS' + '}'
                        + self.usr_id + '}'
                        + str(number) + '}'
                        + '' + '}'
                        + str(self.shebei_bianma[0]) + '}'
                        + self.ctct_software + '}'
                        + self.ctct_version + '}'
                        + '' + '}'
                        + '' + '}'
                        + '' + '}'
                        + '' + '}'
                    )
                else:
                    # self.data_routing['routingData'] = str(
                    #     self.procode + '}' + self.mo + '}' + self.model + '}' + self.line + '}' + self.section + '}' + self.group + '}' + self.station + '}' +
                    #     self.code['err_code_mes'] + '}' + 'FAIL' + '}' + self.usr_id + '}' + str(number) + '}')
                    self.data_routing['routingData'] = str(
                        self.procode + '}'
                        + self.mo + '}'
                        + self.model + '}'
                        + self.line + '}'
                        + self.section + '}'
                        + self.group + '}'
                        + self.station + '}'
                        + self.code['err_code_mes'] + '}'
                        + 'FAIL' + '}'
                        + self.usr_id + '}'
                        + str(number) + '}'
                        + '' + '}'
                        + str(self.shebei_bianma[0]) + '}'
                        + self.ctct_software + '}'
                        + self.ctct_version + '}'
                        + '' + '}'
                        + '' + '}'
                        + '' + '}'
                        + '' + '}'
                    )
        # 其他参数
        self.data_routing['factory'] = self.usr_factory
        # 开始上传
        routing_post = self.routing()
        # 假如上传成功，则显示已上传
        if routing_post['result'].find('OK') != -1:
            # 显示上传成功
            self.label_keyinmessage.configure(foreground='green')
            # 'MES已上传' + '(SN: ' + self.procode + ')'
            # 'Uploaded to MES' + '(SN: ' + self.procode + ')'
            if self.language == 'en':
                self.str_keyinmessage.set('Uploaded to MES' + '(SN: ' + self.procode + ')')
            else:
                self.str_keyinmessage.set('MES已上传' + '(SN: ' + self.procode + ')')
            # 保存数据
            self.code['updated'] = 'OK'
            # 添加到绘图数据
            if self.code['err_meaning'] not in self.image_name:
                self.image_name.append(deepcopy(self.code['err_meaning']))
                self.image_result.append(deepcopy(self.code['result']))
                self.image_number.append(deepcopy(number))
            else:
                ind = self.image_name.index(self.code['err_meaning'])
                self.image_number[ind] = self.image_number[ind] + number
            # 添加到保存数据
            self.history['time'] = str(datetime.datetime.now())[:19]
            self.history['prodCode'] = str(self.code['pro_code'])
            self.history['checkItem'] = str(self.code['err_meaning'])
            self.history['result'] = str(self.code['result'])
            self.history['mo'] = self.mo
            self.history['model'] = self.model
            self.history['line'] = self.line
            self.history['section'] = self.section
            self.history['group'] = self.group
            self.history['station'] = self.station
            self.history['Uploaded'] = 'OK'
            self.history_xls.append(deepcopy(self.history))
            # 更新显示列表
            self.tree_set(self.code)
            # 如果本次为PASS信号，则输出继电器信号
            if self.code['result'] == 'PASS' and self.io_out_type == 'usb':
                try:
                    strhex = 'A00101A2'
                    c = bytes.fromhex(strhex)
                    if self.now_usb == '1' or self.now_usb == '3':
                        try:
                            self.switch_ser = serial.Serial('/dev/ttyUSB0', 9600, timeout=0.5)
                            self.switch_ser.write(c)
                            self.switch_ser.close()
                            print('输出USB继电器开启信号USB0成功：' + str(c))
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                            print('输出USB继电器开启信号USB0失败，请检查USB端口是否正确或有在使用!')
                            pass
                    if self.now_usb == '0' or self.now_usb == '3':
                        try:
                            self.switch_ser = serial.Serial('/dev/ttyUSB1', 9600, timeout=0.5)
                            self.switch_ser.write(c)
                            self.switch_ser.close()
                            print('输出USB继电器开启信号USB1成功：' + str(c))
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                            print('输出USB继电器开启信号USB1失败，请检查USB端口是否正确或有在使用!')
                            pass
                    time.sleep(0.1)
                    strhex = 'A00100A1'
                    c = bytes.fromhex(strhex)
                    if self.now_usb == '1' or self.now_usb == '3':
                        try:
                            self.switch_ser = serial.Serial('/dev/ttyUSB0', 9600, timeout=0.5)
                            self.switch_ser.write(c)
                            self.switch_ser.close()
                            print('输出USB继电器关闭信号USB0成功：' + str(c))
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                            print('输出USB继电器关闭信号USB0失败，请检查USB端口是否正确或有在使用!')
                            pass
                    if self.now_usb == '0' or self.now_usb == '3':
                        try:
                            self.switch_ser = serial.Serial('/dev/ttyUSB1', 9600, timeout=0.5)
                            self.switch_ser.write(c)
                            self.switch_ser.close()
                            print('输出USB继电器关闭信号USB1成功：' + str(c))
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                            print('输出USB继电器关闭信号USB1失败，请检查USB端口是否正确或有在使用!')
                            pass
                except:
                    message = traceback.format_exc()
                    print(message)
                    logger.error(message)
                    print('USB继电器发送失败')
            elif self.code['result'] == 'PASS' and self.io_out_type == 'gpio':
                cmd = 'gpio write {} {}'.format(5, 1)
                print('>>>', cmd)
                os.system(cmd)
                cmd = 'gpio write {} {}'.format(6, 1)
                print('>>>', cmd)
                os.system(cmd)
                time.sleep(0.1)
                cmd = 'gpio write {} {}'.format(5, 0)
                print('>>>', cmd)
                os.system(cmd)
                cmd = 'gpio write {} {}'.format(6, 0)
                print('>>>', cmd)
                os.system(cmd)
        else:
            if self.youtiaoma == '0' and self.watercode > 0:
                self.watercode -= number
            self.label_keyinmessage.configure(foreground='red')
            # 'MES上传失败：' + routing_post['description'] + '(SN: ' + str(self.procode) + ')'
            # 'Fail to upload to MES：' + routing_post['description'] + '(SN: ' + str(self.procode) + ')'
            if self.language == 'en':
                message_list = self.cut(
                    'Fail to upload to MES：' + routing_post['description'] + '(SN: ' + str(self.procode) + ')', 20)
            else:
                message_list = self.cut(
                    'MES上传失败：' + routing_post['description'] + '(SN: ' + str(self.procode) + ')', 20)
            message_str = ''
            for i in range(len(message_list)):
                if i == len(message_list) - 1:
                    message_str = message_str + message_list[i]
                else:
                    message_str = message_str + message_list[i] + '\n'
            self.str_keyinmessage.set(message_str)
        # 初始化变量
        self.data_routing['testData'] = []
        self.code = {
            'pro_code': '',
            'err_code': '',
            'err_meaning': '',
            'result': '',
            'updated': ''
        }

    # 关闭错误提示图
    def check_if_running(self, window):
        window.destroy()

    # 输入数据触发事件
    def getbarcode(self, event):
        logger.info('接收到本地keyin数据:' + str(self.text_keyin.get()))
        if self.text_keyin.get() == '':
            return 0
        # -号可清空临时数据
        if self.text_keyin.get() == '-':
            self.str_keyin.set('')
            self.label_keyinmessage.configure(foreground='green')
            # 临时数据已清空, 可重新输入!
            # The keyin data has been cleared. You can re-keyin!
            if self.language == 'en':
                message_list = self.cut('The keyin data has been cleared. You can re-keyin!', 20)
            else:
                message_list = self.cut('临时数据已清空, 可重新输入!', 20)
            message_str = ''
            for i in range(len(message_list)):
                if i == len(message_list) - 1:
                    message_str = message_str + message_list[i]
                else:
                    message_str = message_str + message_list[i] + '\n'
            self.str_keyinmessage.set(message_str)
            try:
                self.messagewindow = tk.Toplevel(self.window)
                self.messagewindow.overrideredirect(True)
                if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                    self.messagewindow.geometry('+555+430')
                elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                    self.messagewindow.geometry('+555+430')
                else:
                    self.messagewindow.geometry('+680+609')
                # 临时数据已清空, 可重新输入!
                # The keyin data has been cleared. You can re-keyin!
                if self.language == 'en':
                    msg = tk.Message(self.messagewindow, text='The keyin data has been cleared. You can re-keyin!',
                                     font=('Arial', 25), fg='white', bg='#00c7a6',
                                     width=500)
                else:
                    msg = tk.Message(self.messagewindow, text='临时数据已清空, 可重新输入!',
                                     font=('Arial', 25), fg='white', bg='#00c7a6',
                                     width=500)
                msg.pack()
                self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
            self.firstcode = ''
            self.secondcode = ''
            self.procode = ''
        # 第一次接受数据，可分几种情况
        if self.firstcode == '' and self.text_keyin.get() != '':
            self.text_keyin.select_range(0, END)
            self.text_keyin.icursor(0)
            self.firstcode = self.text_keyin.get()
            # 有条码, SN长度满足要求, SN固定码满足要求
            if self.youtiaoma == '1' and len(self.firstcode) == int(self.snlength) and self.firstcode.find(
                    str(self.snguding)) != -1:
                # 有条码非首站，需要进行routingcheck
                if self.feishouzhan == '1':
                    self.data_routing['factory'] = self.usr_factory
                    self.data_routing['testType'] = 'ROUTING_CHECK'
                    self.data_routing['routingData'] = str(
                        self.firstcode + '}' + self.mo + '}' + self.model + '}' + self.line + '}' + self.section + '}' + self.group + '}' + self.station + '}')
                    self.data_routing['testData'] = []
                    routingcheck_receive = self.routing()
                # 有条码首站，不需要进行routingcheck
                else:
                    routingcheck_receive = {'result': 'OK', 'description': '此产品为首站条码产品，无需进行 工单校验!'}
                # 检查routingcheck是否成功
                if routingcheck_receive['result'].find('OK') != -1:
                    self.code['pro_code'] = self.firstcode
                    self.code['err_code'] = 'PASS'
                    self.procode = self.firstcode
                    # 上传数据
                    self.upload_data(1)
                    if self.container_open == '1':
                        self.container_data(1)
                    try:
                        self.messagewindow = tk.Toplevel(self.window)
                        self.messagewindow.overrideredirect(True)
                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.messagewindow.geometry('+555+430')
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.messagewindow.geometry('+555+430')
                        else:
                            self.messagewindow.geometry('+680+609')
                        msg = tk.Message(self.messagewindow, text='PASS', font=('Arial', 25), fg='white',
                                         bg='#00c7a6', width=500)
                        msg.pack()
                        self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
                    except:
                        message = traceback.format_exc()
                        print(message)
                        logger.error(message)
                    self.firstcode = ''
                    self.str_keyin.set('')
                else:
                    self.firstcode = ''
                    self.secondcode = ''
                    errmessage = routingcheck_receive['description']
                    self.label_keyinmessage.configure(foreground='red')
                    # 'MES校验条码错误:' + errmessage
                    # 'Fail to routingCheck from MES:' + errmessage
                    if self.language == 'en':
                        message_list = self.cut('Fail to routingCheck from MES:' + errmessage, 20)
                    else:
                        message_list = self.cut('MES校验条码错误:' + errmessage, 20)
                    message_str = ''
                    for i in range(len(message_list)):
                        if i == len(message_list) - 1:
                            message_str = message_str + message_list[i]
                        else:
                            message_str = message_str + message_list[i] + '\n'
                    self.str_keyinmessage.set(message_str)
                    try:
                        self.messagewindow = tk.Toplevel(self.window)
                        self.messagewindow.overrideredirect(True)
                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.messagewindow.geometry('+555+430')
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.messagewindow.geometry('+555+430')
                        else:
                            self.messagewindow.geometry('+680+609')
                        # 'MES校验条码错误:' + errmessage
                        # 'Fail to routingCheck from MES:' + errmessage
                        if self.language == 'en':
                            msg = tk.Message(self.messagewindow, text='Fail to routingCheck from MES:' + errmessage,
                                             font=('Arial', 25), fg='red', bg='#afafaf',
                                             width=500)
                        else:
                            msg = tk.Message(self.messagewindow, text='MES校验条码错误:' + errmessage,
                                             font=('Arial', 25), fg='red', bg='#afafaf',
                                             width=500)
                        msg.pack()
                        self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
                    except:
                        message = traceback.format_exc()
                        print(message)
                        logger.error(message)
                    self.str_keyin.set('')
            # 无条码(触发信号), 随机码上有工单号, 满足随机码规则
            elif self.youtiaoma == '0' and self.firstcode.find(self.mo) != -1:
                # 检查是不是批量上传, 有---则是， 没有则不是
                if self.firstcode.find('---') != -1:
                    getnumber = int(self.firstcode.split('---')[1])
                else:
                    getnumber = 1
                self.code['pro_code'] = self.firstcode
                self.code['err_code'] = 'PASS'
                self.procode = self.firstcode
                self.upload_data(getnumber)
                if self.container_open == '1':
                    self.container_data(getnumber)
                self.firstcode = ''
                self.str_keyin.set('')
            # 无条码(手动keyin条码), SN长度满足要求, SN固定码满足要求, SN长度不能为0
            elif self.youtiaoma == '0' and len(self.firstcode) == int(self.snlength) and self.firstcode.find(
                    str(self.snguding)) != -1 and len(self.snlength) != 0:
                self.code['pro_code'] = self.firstcode
                self.code['err_code'] = 'PASS'
                self.procode = self.firstcode
                self.upload_data(int(float(self.chanliang)))
                if self.container_open == '1':
                    self.container_data(int(float(self.chanliang)))
                self.firstcode = ''
                self.str_keyin.set('')
            # 检查码
            else:
                errmeaning = ''
                # 检查是否批量输入检查码，使用+号，或*号
                if self.firstcode.find('+') != -1:
                    errcodes = self.firstcode.split('+')
                    for i in range(len(errcodes) - 1, -1, -1):
                        if errcodes[i].find('*') != -1:
                            num = int(errcodes[i].split('*')[1])
                            for j in range(num - 1):
                                errcodes.append(deepcopy(errcodes[i].split('*')[0]))
                            errcodes[i] = deepcopy(errcodes[i].split('*')[0])
                    self.firstcode = ''
                    for i in range(len(errcodes)):
                        self.firstcode = self.firstcode + '+' + deepcopy(errcodes[i])
                    self.firstcode = self.firstcode[1:]
                else:
                    errcodes = self.firstcode.split(',')
                    for i in range(len(errcodes) - 1, -1, -1):
                        if errcodes[i].find('*') != -1:
                            num = int(errcodes[i].split('*')[1])
                            for j in range(num - 1):
                                errcodes.append(deepcopy(errcodes[i].split('*')[0]))
                            errcodes[i] = deepcopy(errcodes[i].split('*')[0])
                    self.firstcode = ''
                    for i in range(len(errcodes)):
                        self.firstcode = self.firstcode + ',' + deepcopy(errcodes[i])
                    self.firstcode = self.firstcode[1:]
                for i in range(len(errcodes)):
                    # sign=1说明检查项检索成功，sign=0说明检查项检索不到，一旦有其中一个检查项检索不到，报错返回
                    sign = 0
                    for item in self.result_xls:
                        if errcodes[i] == str(item['Code']):
                            if self.firstcode.find('+') != -1:
                                errmeaning = errmeaning + '+' + deepcopy(item['Meaning'])
                            else:
                                errmeaning = errmeaning + ',' + deepcopy(item['Meaning'])
                            sign = 1
                            break
                    if sign == 0:
                        self.label_keyinmessage.configure(foreground='red')
                        # errcodes[i] + '!条码有误或无该检查项,请重新输入!'
                        # errcodes[i] + '!SN is wrong or has not this Code, please check and re-keyin!'
                        if self.language == 'en':
                            message_list = self.cut(
                                errcodes[i] + '!SN is wrong or has not this Code, please check and re-keyin!', 20)
                        else:
                            message_list = self.cut(errcodes[i] + '!条码有误或无该检查项,请重新输入!', 20)
                        message_str = ''
                        for j in range(len(message_list)):
                            if j == len(message_list) - 1:
                                message_str = message_str + message_list[j]
                            else:
                                message_str = message_str + message_list[j] + '\n'
                        self.str_keyinmessage.set(message_str)
                        try:
                            self.messagewindow = tk.Toplevel(self.window)
                            self.messagewindow.overrideredirect(True)
                            if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                                self.messagewindow.geometry('+555+430')
                            elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                                self.messagewindow.geometry('+555+430')
                            else:
                                self.messagewindow.geometry('+680+609')
                            msg = tk.Message(self.messagewindow, text=message_str,
                                             font=('Arial', 25), fg='red', bg='#afafaf', width=500)
                            msg.pack()
                            self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                        self.firstcode = ''
                        self.str_keyin.set('')
                        return 0
                self.str_keyin.set('')
                errmeaning = errmeaning[1:]
                self.label_keyinmessage.configure(foreground='red')
                message_list = self.cut(errmeaning, 20)
                message_str = ''
                for i in range(len(message_list)):
                    if i == len(message_list) - 1:
                        message_str = message_str + message_list[i]
                    else:
                        message_str = message_str + message_list[i] + '\n'
                self.str_keyinmessage.set(message_str)
                try:
                    self.messagewindow = tk.Toplevel(self.window)
                    self.messagewindow.overrideredirect(True)
                    if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                        self.messagewindow.geometry('+555+430')
                    elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                        self.messagewindow.geometry('+555+430')
                    else:
                        self.messagewindow.geometry('+680+609')
                    msg = tk.Message(self.messagewindow, text=message_str, font=('Arial', 25), fg='red',
                                     bg='#afafaf',
                                     width=500)
                    msg.pack()
                    self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
                except:
                    message = traceback.format_exc()
                    print(message)
                    logger.error(message)
        # 第二次接受数据，可分几种情况
        elif self.firstcode != '' and self.firstcode != self.text_keyin.get():
            self.secondcode = self.text_keyin.get()
            self.procode = self.secondcode
            # 有条码, SN长度满足要求, SN固定码满足要求
            if self.youtiaoma == '1' and len(self.secondcode) == int(self.snlength) and self.secondcode.find(
                    str(self.snguding)) != -1:
                # 非首站，需要进行routingcheck
                if self.feishouzhan == '1':
                    self.data_routing['factory'] = self.usr_factory
                    self.data_routing['testType'] = 'ROUTING_CHECK'
                    self.data_routing['routingData'] = str(
                        self.secondcode + '}' + self.mo + '}' + self.model + '}' + self.line + '}' + self.section + '}' + self.group + '}' + self.station + '}')
                    self.data_routing['testData'] = []
                    routingcheck_receive = self.routing()
                # 首站，不需要进行routingcheck
                else:
                    routingcheck_receive = {'result': 'OK', 'description': '此产品为首站条码产品，无需进行工单校验!'}
                # 检查routingcheck是否成功
                if routingcheck_receive['result'].find('OK') != -1:
                    self.code['pro_code'] = self.secondcode
                    if self.firstcode.find('+') != -1:
                        self.code['err_code'] = self.firstcode.split('+')[0]
                    else:
                        self.code['err_code'] = self.firstcode.split(',')[0]
                    self.upload_data(1)
                    try:
                        self.messagewindow = tk.Toplevel(self.window)
                        self.messagewindow.overrideredirect(True)
                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.messagewindow.geometry('+555+430')
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.messagewindow.geometry('+555+430')
                        else:
                            self.messagewindow.geometry('+680+609')
                        msg = tk.Message(self.messagewindow, text='FAIL', font=('Arial', 25), fg='red',
                                         bg='#afafaf', width=500)
                        msg.pack()
                        self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
                    except:
                        message = traceback.format_exc()
                        print(message)
                        logger.error(message)
                    newfirstcode = ''
                    if self.firstcode.find('+') != -1:
                        for i in range(len(self.firstcode.split('+')[1:])):
                            newfirstcode = newfirstcode + '+' + str(self.firstcode.split('+')[1:][i])
                    else:
                        for i in range(len(self.firstcode.split(',')[1:])):
                            newfirstcode = newfirstcode + ',' + str(self.firstcode.split(',')[1:][i])
                    newfirstcode = newfirstcode[1:]
                    self.firstcode = newfirstcode
                    if self.firstcode != '':
                        errmeaning = ''
                        if self.firstcode.find('+') != -1:
                            errcodes = self.firstcode.split('+')
                        else:
                            errcodes = self.firstcode.split(',')
                        for i in range(len(errcodes)):
                            for item in self.result_xls:
                                if errcodes[i] == str(item['Code']):
                                    if self.firstcode.find('+') != -1:
                                        errmeaning = errmeaning + '+' + deepcopy(item['Meaning'])
                                    else:
                                        errmeaning = errmeaning + ',' + deepcopy(item['Meaning'])
                        errmeaning = errmeaning[1:]
                        self.label_keyinmessage.configure(foreground='red')
                        message_list = self.cut(errmeaning, 20)
                        message_str = ''
                        for i in range(len(message_list)):
                            if i == len(message_list) - 1:
                                message_str = message_str + message_list[i]
                            else:
                                message_str = message_str + message_list[i] + '\n'
                        self.str_keyinmessage.set(message_str)
                    # 默認為通過
                    self.str_keyin.set('')
                else:
                    self.secondcode = ''
                    errmessage = routingcheck_receive['description']
                    try:
                        self.messagewindow = tk.Toplevel(self.window)
                        self.messagewindow.overrideredirect(True)
                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.messagewindow.geometry('+555+430')
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.messagewindow.geometry('+555+430')
                        else:
                            self.messagewindow.geometry('+680+609')
                        # '请重新输入条码, MES校验条码错误:' + errmessage
                        # 'Please re-keyin, fail to routingCheck from MES:' + errmessage
                        if self.language == 'en':
                            msg = tk.Message(self.messagewindow,
                                             text='Please re-keyin, fail to routingCheck from MES:\n' + errmessage,
                                             font=('Arial', 25), fg='red', bg='#afafaf',
                                             width=500)
                        else:
                            msg = tk.Message(self.messagewindow,
                                             text='请重新输入条码, MES校验条码错误:' + errmessage,
                                             font=('Arial', 25), fg='red', bg='#afafaf',
                                             width=500)
                        msg.pack()
                        self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
                    except:
                        message = traceback.format_exc()
                        print(message)
                        logger.error(message)
                    self.str_keyin.set('')
            # 无条码(触发信号), 随机码上有工单号, 满足随机码规则
            elif self.youtiaoma == '0' and self.procode.find(self.mo) != -1:
                self.code['pro_code'] = self.procode
                if self.firstcode.find('+') != -1:
                    self.code['err_code'] = self.firstcode.split('+')[0]
                else:
                    self.code['err_code'] = self.firstcode.split(',')[0]
                self.upload_data(1)
                # 删减并更新检查码
                newfirstcode = ''
                if self.firstcode.find('+') != -1:
                    for i in range(len(self.firstcode.split('+')[1:])):
                        newfirstcode = newfirstcode + '+' + str(self.firstcode.split('+')[1:][i])
                else:
                    for i in range(len(self.firstcode.split(',')[1:])):
                        newfirstcode = newfirstcode + '+' + str(self.firstcode.split(',')[1:][i])
                newfirstcode = newfirstcode[1:]
                self.firstcode = newfirstcode
                if self.firstcode != '':
                    errmeaning = ''
                    if self.firstcode.find('+') != -1:
                        errcodes = self.firstcode.split('+')
                    else:
                        errcodes = self.firstcode.split(',')
                    for i in range(len(errcodes)):
                        for item in self.result_xls:
                            if errcodes[i] == str(item['Code']):
                                if self.firstcode.find('+') != -1:
                                    errmeaning = errmeaning + '+' + deepcopy(item['Meaning'])
                                else:
                                    errmeaning = errmeaning + ',' + deepcopy(item['Meaning'])
                    errmeaning = errmeaning[1:]

                    message_list = self.cut(errmeaning, 20)
                    message_str = ''
                    for i in range(len(message_list)):
                        if i == len(message_list) - 1:
                            message_str = message_str + message_list[i]
                        else:
                            message_str = message_str + message_list[i] + '\n'
                    self.label_keyinmessage.configure(foreground='red')
                    self.str_keyinmessage.set(message_str)
                # 默認為通過
                self.str_keyin.set('')
                return 1
            # 无条码(手动keyin条码), SN长度满足要求, SN固定码满足要求, SN长度不能为0
            elif self.youtiaoma == '0' and len(self.secondcode) == int(self.snlength) and self.secondcode.find(
                    str(self.snguding)) != -1 and len(self.snlength) != 0:
                self.code['pro_code'] = self.procode
                if self.firstcode.find('+') != -1:
                    self.code['err_code'] = self.firstcode.split('+')[0]
                else:
                    self.code['err_code'] = self.firstcode.split(',')[0]
                self.upload_data(1)
                # 删减并更新检查码
                newfirstcode = ''
                if self.firstcode.find('+') != -1:
                    for i in range(len(self.firstcode.split('+')[1:])):
                        newfirstcode = newfirstcode + '+' + str(self.firstcode.split('+')[1:][i])
                else:
                    for i in range(len(self.firstcode.split(',')[1:])):
                        newfirstcode = newfirstcode + '+' + str(self.firstcode.split(',')[1:][i])
                newfirstcode = newfirstcode[1:]
                self.firstcode = newfirstcode
                if self.firstcode != '':
                    errmeaning = ''
                    if self.firstcode.find('+') != -1:
                        errcodes = self.firstcode.split('+')
                    else:
                        errcodes = self.firstcode.split(',')
                    for i in range(len(errcodes)):
                        for item in self.result_xls:
                            if errcodes[i] == str(item['Code']):
                                if self.firstcode.find('+') != -1:
                                    errmeaning = errmeaning + '+' + deepcopy(item['Meaning'])
                                else:
                                    errmeaning = errmeaning + ',' + deepcopy(item['Meaning'])
                    errmeaning = errmeaning[1:]
                    message_list = self.cut(errmeaning, 20)
                    message_str = ''
                    for i in range(len(message_list)):
                        if i == len(message_list) - 1:
                            message_str = message_str + message_list[i]
                        else:
                            message_str = message_str + message_list[i] + '\n'
                    self.label_keyinmessage.configure(foreground='red')
                    self.str_keyinmessage.set(message_str)
                self.str_keyin.set('')
                return 1
            # 用户覆盖输入检查码
            else:
                errmeaning = ''
                # 检查是否批量输入检查码，使用+号，或*号
                if self.secondcode.find('+') != -1:
                    errcodes = self.secondcode.split('+')
                    for i in range(len(errcodes) - 1, -1, -1):
                        if errcodes[i].find('*') != -1:
                            num = int(errcodes[i].split('*')[1])
                            for j in range(num - 1):
                                errcodes.append(deepcopy(errcodes[i].split('*')[0]))
                            errcodes[i] = deepcopy(errcodes[i].split('*')[0])
                    self.secondcode = ''
                    for i in range(len(errcodes)):
                        self.secondcode = self.secondcode + '+' + deepcopy(errcodes[i])
                    self.secondcode = self.secondcode[1:]
                else:
                    errcodes = self.secondcode.split(',')
                    for i in range(len(errcodes) - 1, -1, -1):
                        if errcodes[i].find('*') != -1:
                            num = int(errcodes[i].split('*')[1])
                            for j in range(num - 1):
                                errcodes.append(deepcopy(errcodes[i].split('*')[0]))
                            errcodes[i] = deepcopy(errcodes[i].split('*')[0])
                    self.secondcode = ''
                    for i in range(len(errcodes)):
                        self.secondcode = self.secondcode + ',' + deepcopy(errcodes[i])
                    self.secondcode = self.secondcode[1:]

                if self.secondcode.find('+') != -1:
                    errcodes = self.secondcode.split('+')
                else:
                    errcodes = self.secondcode.split(',')

                for i in range(len(errcodes)):
                    # sign=1说明检查项检索成功，sign=0说明检查项检索不到，一旦有其中一个检查项检索不到，报错返回
                    sign = 0
                    for item in self.result_xls:
                        if errcodes[i] == str(item['Code']):
                            if self.secondcode.find('+') != -1:
                                errmeaning = errmeaning + '+' + deepcopy(item['Meaning'])
                            else:
                                errmeaning = errmeaning + ',' + deepcopy(item['Meaning'])
                            sign = 1
                            break

                    if sign == 0:
                        self.label_keyinmessage.configure(foreground='red')
                        # errcodes[i] + '!条码长度有误或无该检查项,请重新输入!'
                        # errcodes[i] + '!SN length is wrong or has not this Code, please re-keyin!'
                        if self.language == 'en':
                            message_list = self.cut(
                                errcodes[i] + '!SN length is wrong or has not this Code, please re-keyin!', 20)
                        else:
                            message_list = self.cut(errcodes[i] + '!条码长度有误或无该检查项,请重新输入!', 20)
                        message_str = ''
                        for j in range(len(message_list)):
                            if j == len(message_list) - 1:
                                message_str = message_str + message_list[j]
                            else:
                                message_str = message_str + message_list[j] + '\n'
                        self.str_keyinmessage.set(message_str)
                        try:
                            self.messagewindow = tk.Toplevel(self.window)
                            self.messagewindow.overrideredirect(True)
                            if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                                self.messagewindow.geometry('+555+430')
                            elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                                self.messagewindow.geometry('+555+430')
                            else:
                                self.messagewindow.geometry('+680+609')
                            msg = tk.Message(self.messagewindow, text=message_str,
                                             font=('Arial', 25), fg='red', bg='#afafaf',
                                             width=500)
                            msg.pack()
                            self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                        # 清空self.secondcode
                        self.firstcode = ''
                        self.secondcode = ''
                        self.procode = ''
                        self.str_keyin.set('')
                        return 0

                self.str_keyin.set('')
                errmeaning = errmeaning[1:]
                self.label_keyinmessage.configure(foreground='red')
                message_list = self.cut(errmeaning, 20)
                message_str = ''
                for i in range(len(message_list)):
                    if i == len(message_list) - 1:
                        message_str = message_str + message_list[i]
                    else:
                        message_str = message_str + message_list[i] + '\n'
                self.str_keyinmessage.set(message_str)
                self.firstcode = self.secondcode
                self.secondcode = ''
                try:
                    self.messagewindow = tk.Toplevel(self.window)
                    self.messagewindow.overrideredirect(True)
                    if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                        self.messagewindow.geometry('+555+430')
                    elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                        self.messagewindow.geometry('+555+430')
                    else:
                        self.messagewindow.geometry('+680+609')
                    msg = tk.Message(self.messagewindow, text=message_str, font=('Arial', 25), fg='red', bg='#afafaf',
                                     width=500)
                    msg.pack()
                    self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
                except:
                    message = traceback.format_exc()
                    print(message)
                    logger.error(message)
        # 重复输入
        elif self.firstcode != '' and self.firstcode == self.text_keyin.get():
            self.str_keyin.set('')
            self.text_keyin.select_range(0, END)
            self.text_keyin.icursor(0)

    # 统计并上传MES, 装载箱的数量
    def container_data(self, number):
        print('self.side_name=%s' % self.side_name)
        print('self.side_now=%s' % self.side_now)
        print('self.side_number=%s,type=%s' % (self.side_number, type(self.side_number)))
        self.side_number[self.side_name.index(self.side_now)] += number
        print('当前出料口名称：%s,当前出料箱数据：%s' % (self.side_now, self.side_number))
        number_max = 1
        for i in range(len(self.modelset_xls)):
            if str(self.modelset_xls[i]['机种型号']) == str(self.model):
                number_max = int(self.modelset_xls[i]['单箱数量'])
                break
            elif i == len(self.modelset_xls) - 1:
                print('model_set机种文件内无法查询到' + str(self.model) + '该机种!')
                for j in range(len(self.modelset_xls) - 1, -1, -1):
                    if str(self.modelset_xls[j]['机种型号']) == '其他':
                        number_max = int(self.modelset_xls[j]['单箱数量'])
                        print('model_set机种文件内无法查询到%s机种,自动使用其他型号配置%s' % (self.model, number_max))
                        break
                    elif j == 0:
                        print('model_set机种文件内无法查询到' + str(self.model) + '该机种!且找不到其他选项')
        print('当前出料口数量：%s,最大装载箱数量：%s' % (
            int(self.side_number[self.side_name.index(self.side_now)]), number_max))

        if int(self.side_number[self.side_name.index(self.side_now)]) >= number_max:
            # 上传MES
            # routing变量
            url_routing = 'http://' + self.url_mes + ':10101/TDC/DELTA_DEAL_TEST_DATA_I'
            # 测试QA环境
            # url_routing = 'http://' + '10.148.200.28' + ':10101/TDC/DELTA_DEAL_TEST_DATA_I'
            params_routing = {
                'sign': ''
            }
            nowtime = str(datetime.datetime.now())
            random_code = nowtime[20:26]+str(random.randint(1, 254))+str(random.randint(1, 254))
            # random_code = random_code[:39]
            self.container_NO = random_code
            data_routing = {
                'factory': self.usr_factory,
                'testType': 'FG_CONTAINER_LINK',
                'routingData': {
                    'mo': self.mo,
                    'model': self.model,
                    'line': self.line,
                    'section': self.section,
                    'group': self.group,
                    'station': self.station,
                    'containerNo': self.container_NO,
                    'qty': int(self.side_number[self.side_name.index(self.side_now)]),
                    'port': self.side_now,
                    'needAGV': 'Y'
                },
                'testData': []
            }
            # 开始上传
            # 将body的数据转换为字符串格式
            data_str = json.dumps(data_routing, sort_keys=False)
            # 字符串拼接md5加密
            src = self.secretkey + data_str
            md5 = self.keymd5(src)
            params_routing['sign'] = md5
            # 发送数据并接收返回数据
            print(url_routing)
            print(data_routing)
            print(self.headers)
            print(params_routing)
            logger.info('url_routing=%s\ndata_routing=%s\nheaders=%s\nparams_routing=%s'%(url_routing,data_routing,self.headers,params_routing))

            ############################################################
            if self.not_upload == '1':
                print('MES装箱上传成功---receive: 测试用不上传MES')
                logger.info('MES装箱上传成功---receive: 测试用不上传MES')
                if self.language == 'en':
                    message_list = self.cut( 'Uploaded AGV to MES TEST OK', 25)
                else:
                    message_list = self.cut( '(测试模式)MES呼叫AGV小车已上传:%s'%str(data_str).replace(" ","").replace("\n",""), 25)
                message_str = ''
                # message_str =  message_str
                for i in range(len(message_list)):
                    if i == len(message_list) - 1:
                        message_str = message_str + message_list[i]
                    else:
                        message_str = message_str + message_list[i] + '\n'
                # message_str =  message_str
                self.label_keyinmessage.configure(foreground='green')
                self.str_keyinmessage.set(message_str)
                # routing_post = {'result': 'OK',
                #                 'description': 'MES装箱上传成功---receive: 测试用不上传MES'}
                # return routing_post
            ############################################################
            else:
                try:
                    # logger.info('上传MES装箱数据--body：' + str(data_str))
                    # logger.info('上传MES装箱数据--params：' + str(params_routing))
                    receive = requests.request('POST', url_routing, data=data_str, headers=self.headers,
                                               params=params_routing, timeout=20)
                    try:
                        receive = json.loads(receive.text)
                    except:
                        receive = eval(receive.text)
                    # print(receive['result'])
                    # print(receive['description'])
                    print('上传MES装箱成功：' + str(receive))
                    logger.info('上传MES装箱成功---receive: ' + str(receive))
                except:
                    message = traceback.format_exc()
                    # print(message)
                    # logger.error(message)
                    print('上传MES装箱失败!\n%s'%message)
                    logger.error('上传MES装箱失败!\n%s'%message)
                    # message = traceback.format_exc()
                    # print(message)
                    # logger.info(message)
                    if self.language == 'en':
                        message_list = self.cut('Uploaded AGV to MES Error', 25)
                    else:
                        message_list = self.cut('MES呼叫AGV小车上传失败', 25)
                    message_str = ''
                    # message_str =  message_str
                    for i in range(len(message_list)):
                        if i == len(message_list) - 1:
                            message_str = message_str + message_list[i]
                        else:
                            message_str = message_str + message_list[i] + '\n'
                    # message_str =  message_str
                    self.label_keyinmessage.configure(foreground='red')
                    self.str_keyinmessage.set(message_str)
                    # try:
                    #     self.messagewindow2 = tk.Toplevel(self.window)
                    #     self.messagewindow2.overrideredirect(True)
                    #     if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                    #         self.messagewindow2.geometry('+555+430')
                    #     elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                    #         self.messagewindow2.geometry('+555+430')
                    #     else:
                    #         self.messagewindow2.geometry('+680+609')
                    #     # 临时数据已清空, 可重新输入!
                    #     # The keyin data has been cleared. You can re-keyin!
                    #     if self.language == 'en':
                    #         msg2 = tk.Message(self.messagewindow2, text='Uploaded AGV to MES Error',
                    #                          font=('Arial', 25), fg='white', bg='#00c7a6',
                    #                          width=500)
                    #     else:
                    #         msg2 = tk.Message(self.messagewindow2, text='MES呼叫AGV小车上传失败',
                    #                          font=('Arial', 25), fg='white', bg='#00c7a6',
                    #                          width=500)
                    #     msg2.pack()
                    #     self.messagewindow2.after(2000, self.check_if_running, self.messagewindow2)
                    # except:
                    #     message = traceback.format_exc()
                    #     print(message)
                    #     logger.error(message)
                else:
                    if self.language == 'en':
                        message_list = self.cut('Uploaded AGV to MES', 25)
                    else:
                        message_list = self.cut('MES呼叫AGV小车已上传:%s' % str(data_str).replace(" ","").replace("\n",""), 25)
                    message_str = ''
                    # message_str =  message_str
                    for i in range(len(message_list)):
                        if i == len(message_list) - 1:
                            message_str = message_str + message_list[i]
                        else:
                            message_str = message_str + message_list[i] + '\n'
                    # message_str =  message_str
                    self.label_keyinmessage.configure(foreground='green')
                    self.str_keyinmessage.set(message_str)
                    # try:
                    #     self.messagewindow2 = tk.Toplevel(self.window)
                    #     self.messagewindow2.overrideredirect(True)
                    #     if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                    #         self.messagewindow2.geometry('+555+430')
                    #     elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                    #         self.messagewindow2.geometry('+555+430')
                    #     else:
                    #         self.messagewindow2.geometry('+680+609')
                    #     # 临时数据已清空, 可重新输入!
                    #     # The keyin data has been cleared. You can re-keyin!
                    #     if self.language == 'en':
                    #         msg2 = tk.Message(self.messagewindow2, text='Uploaded AGV to MES',
                    #                          font=('Arial', 25), fg='white', bg='#00c7a6',
                    #                          width=500)
                    #     else:
                    #         msg2 = tk.Message(self.messagewindow2, text='MES呼叫AGV小车已上传:%s' % data_str,
                    #                          font=('Arial', 25), fg='white', bg='#00c7a6',
                    #                          width=500)
                    #     msg2.pack()
                    #     self.messagewindow2.after(2000, self.check_if_running, self.messagewindow2)
                    # except:
                    #     message = traceback.format_exc()
                    #     print(message)
                    #     logger.error(message)

            # 检查是否需要更换出料口
            for i in range(len(self.side_name)):
                # print('循环次数%s'%i)
                # print('self.side_now=%s,self.side_name[i]=%s,self.side_status[i]=%s'%(self.side_now,self.side_name[i],self.side_status[i]))
                if self.side_now != self.side_name[i] and int(self.side_number[i]) < number_max and self.side_status[i] == 1:
                    self.side_now = self.side_name[i]
                    # 将压力阀打到对应的位置
                    self.container_control(self.side_now)
                    print('已更新出料口名称：%s' % self.side_now)
                    logger.info('已更新出料口名称：%s' % self.side_now)
                    if self.language == 'en':
                        message_list = self.cut('Change OK', 25)
                    else:
                        message_list = self.cut('已更新出料口为：%s' % self.side_now, 25)
                    message_str = ''
                    for j in range(len(message_list)):
                        if j == len(message_list) - 1:
                            message_str = message_str + message_list[j]
                        else:
                            message_str = message_str + message_list[j] + '\n'
                    # message_str =  message_str
                    self.label_keyinmessage.configure(foreground='green')
                    self.str_keyinmessage.set(message_str)
                    break
                    # try:
                    #     self.messagewindow3 = tk.Toplevel(self.window)
                    #     self.messagewindow3.overrideredirect(True)
                    #     if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                    #         self.messagewindow3.geometry('+555+430')
                    #     elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                    #         self.messagewindow3.geometry('+555+430')
                    #     else:
                    #         self.messagewindow3.geometry('+680+609')
                    #     # 临时数据已清空, 可重新输入!
                    #     # The keyin data has been cleared. You can re-keyin!
                    #     if self.language == 'en':
                    #         msg = tk.Message(self.messagewindow3, text='Change OK',
                    #                          font=('Arial', 25), fg='white', bg='#00c7a6',
                    #                          width=500)
                    #     else:
                    #         msg = tk.Message(self.messagewindow3,
                    #                          text='已更新出料口为：%s' % self.side_now,
                    #                          font=('Arial', 25), fg='white', bg='#00c7a6',
                    #                          width=500)
                    #     msg.pack()
                    #     self.messagewindow3.after(1000, self.check_if_running, self.messagewindow3)
                    # except:
                    #     message = traceback.format_exc()
                    #     print(message)
                    #     logger.error(message)
                elif i == len(self.side_name) - 1:
                    print('无法找到空的出料箱,故无法切换出料口,发出设备停机指令')
                    logger.info('无法找到空的出料箱,故无法切换出料口,发出设备停机指令')
                    self.container_control('OUT-PCS')
                    if self.language == 'en':
                        message_list = self.cut('Change Error Because Full', 25)
                    else:
                        message_list = self.cut('无法找到空的出料箱,故无法切换出料口,发出设备停机指令', 25)
                    message_str = ''
                    for j in range(len(message_list)):
                        if j == len(message_list) - 1:
                            message_str = message_str + message_list[j]
                        else:
                            message_str = message_str + message_list[j] + '\n'
                    # message_str =  message_str
                    self.label_keyinmessage.configure(foreground='red')
                    self.str_keyinmessage.set(message_str)
                    # try:
                    #     self.messagewindow3 = tk.Toplevel(self.window)
                    #     self.messagewindow3.overrideredirect(True)
                    #     if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                    #         self.messagewindow3.geometry('+555+430')
                    #     elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                    #         self.messagewindow3.geometry('+555+430')
                    #     else:
                    #         self.messagewindow3.geometry('+680+609')
                    #     # 临时数据已清空, 可重新输入!
                    #     # The keyin data has been cleared. You can re-keyin!
                    #     if self.language == 'en':
                    #         msg3 = tk.Message(self.messagewindow3, text='Change Error Because Full',
                    #                          font=('Arial', 25), fg='white', bg='#00c7a6',
                    #                          width=500)
                    #     else:
                    #         msg3 = tk.Message(self.messagewindow3,
                    #                          text='无法找到空的出料箱,故无法切换出料口,发出设备停机指令',
                    #                          font=('Arial', 25), fg='white', bg='#00c7a6',
                    #                          width=500)
                    #     msg3.pack()
                    #     self.messagewindow3.after(1000, self.check_if_running, self.messagewindow3)
                    # except:
                    #     message = traceback.format_exc()
                    #     print(message)
                    #     logger.error(message)

        str_show = ''
        for i in range(len(self.side_name)):
            str_show = str_show + ' ' + '%s=%s' % (self.side_name[i], self.side_number[i])
        str_show = str_show + ' ' + 'Now=%s Max=%s' % (self.side_now, number_max)
        self.str_container.set(str_show)

        self.setini_lock.acquire()
        self.config.set('container_config', 'side_number', str(self.side_number)[1:-1].replace(' ', ''))
        self.config.set('container_config', 'side_now', str(self.side_now))
        self.config.write(open(work_address + '/set.ini', 'w', encoding='utf-8-sig'))
        self.config.write(open(work_address + '/set_backup.ini', 'w', encoding='utf-8-sig'))
        self.setini_lock.release()

    # 控制装载箱气阀
    def container_control(self, side_name):
        # 舊款
        # RS485_command = {
        #     'A': '000600040001081A',
        #     'B': '000600040000C9DA',
        # }
        # switch_ser = serial.Serial('/dev/ttyS5', 9600, timeout=0.5)
        # c = bytes.fromhex(RS485_command[side_name])
        # switch_ser.write(c)
        # switch_ser.close()
        # 新款
        RS485_command = {
            'COMMON1': 'FF050000FF0099E4',
            'COMMON2': 'FF0500000000D814',
            'OUT-Y': 'FF050001FF00C824',
            'OUT-N': 'FF050001000089D4',
            'OUT-PCS': 'FF10000800020400040014842C'
        }
        switch_ser = serial.Serial('/dev/ttyS5', 9600, timeout=0.5)
        c = bytes.fromhex(RS485_command[side_name])
        switch_ser.write(c)
        switch_ser.close()

    # (仅用于无条码)根据流水号，生成新的随机条码
    def madeSN(self, number):
        if number > 1:
            nowtime = str(datetime.datetime.now())
            random_code = '`' + self.mo + nowtime[18:26] + str(self.watercode).rjust(6, '0') + '---' + str(number)
            self.watercode += number
        else:
            nowtime = str(datetime.datetime.now())
            random_code = '`' + self.mo + nowtime[18:26] + str(self.watercode).rjust(6, '0')
            self.watercode += 1
        return random_code

    # 工作线程1,检测产品触发信号(无条码模式才开启)
    def workthreading_ChuFa(self):
        new_trigger_times = 0
        old_trigger_times = 0
        while True:
            try:
                self.str_signal.set(self.now_signal)
                self.label_signal.configure(fg='#FFFFFF')
                while True:
                    if not self.tuple_triggerTimes_and_nowUsb_and_nowSignal_and_signalList.empty():
                        receive = self.tuple_triggerTimes_and_nowUsb_and_nowSignal_and_signalList.get()
                        print('receive')
                        print(receive)
                        new_trigger_times = receive[0]
                        self.now_usb = receive[1]
                        self.now_signal = receive[2]
                        self.signal_list = receive[3][1:-1].replace(' ', '').split(',')
                        self.cttimeset = deepcopy(receive[4])
                    else:
                        break
                if new_trigger_times > old_trigger_times:
                    if self.mo != '******':
                        # 计算需要生成的SN码数量
                        keyin_times = int(float(self.chanliang)) * (new_trigger_times - old_trigger_times)
                        # 假如用户没有输入过检查码, 即self.firstcode没有值，则可以直接批量全部上传PASS
                        while keyin_times > 0:
                            if self.firstcode != '':
                                self.str_keyin.set(self.madeSN(1))
                                self.getbarcode('<Return>')
                                keyin_times -= 1
                            else:
                                self.str_keyin.set(self.madeSN(keyin_times))
                                self.getbarcode('<Return>')
                                keyin_times = 0
                    else:
                        self.label_keyinmessage.configure(foreground='red')
                        # 已触发,但未设置工单,等待PMM Table换工单后才会触发上传!
                        # Has triggered, but not set MO, will Upload until link to PMM Table
                        if self.language == 'en':
                            message_list = self.cut('Has triggered, but not set MO, will Upload until link to PMM Table', 25)
                        else:
                            message_list = self.cut('已触发,但未设置工单,等待PMM Table换工单后才会触发上传!', 20)
                        message_str = ''
                        for i in range(len(message_list)):
                            if i == len(message_list) - 1:
                                message_str = message_str + message_list[i]
                            else:
                                message_str = message_str + message_list[i] + '\n'
                        self.str_keyinmessage.set(message_str)
                    old_trigger_times = new_trigger_times
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
                # 已断开
                # Fail
                self.str_signal.set('FAIL')
                self.label_signal.configure(fg='red')
                print('触发线程出现异常!')
                time.sleep(1)

    # 工作进程，轮询检查GPIO触发情况
    def workprocess_CheckGPIO(self, signal_channel, container_channel, light_channel):
        # 初始化GPIO
        def init_GPIO():
            return os.system('gpio mode 9 in;'
                             'gpio mode 8 in;'
                             'gpio mode 10 in;'
                             'gpio mode 13 in;'
                             "gpio mode 6 out;"
                             "gpio mode 5 out;"
                             )

        # 读取所有GPIO输出口信息
        def read_GPIO():
            # 0 - OUT0
            # 1 - OUT1
            # 2 - IN0
            # 3 - IN1
            # 4 - IN2
            # 5 - IN3
            res = [int(x.replace('\n', '')) for x in os.popen('gpio read 9;'
                                                              'gpio read 8;'
                                                              'gpio read 10;'
                                                              'gpio read 13')]
            return res

        # 定义触发次数参数
        trigger_times = 0

        # 循环程序
        while True:
            # 轮询Check GPIO的触发情况
            while True:
                try:
                    init_GPIO()

                    config = configparser.ConfigParser()
                    try:
                        config.read(work_address + '/set.ini', encoding='utf-8-sig')
                    except:
                        config.read(work_address + '/set_backup.ini', encoding='utf-8-sig')

                    gpio_in_com_new = config.get('else_config', 'gpio_in_com_new').replace(' ','').split(',')
                    gpio_in_com_new = [] if gpio_in_com_new == [''] else [int(gpio_in_com_new[i]) for i in range(len(gpio_in_com_new))]

                    container_open = config.get('container_config', 'container_open')
                    print('载入配置container_open=%s' % container_open)
                    side_gpio = config.get('container_config', 'side_gpio').replace(' ','').split(',')
                    side_gpio = [] if side_gpio == [''] else [int(side_gpio[i]) for i in range(len(side_gpio))]

                    light_open = config.get('light_config', 'light_open')
                    print('载入配置light_open=%s' % light_open)
                    gpio_green = int(config.get('light_config', 'gpio_green'))
                    gpio_yellow = int(config.get('light_config', 'gpio_yellow'))
                    gpio_red = int(config.get('light_config', 'gpio_red'))
                    hold_green_time = 0 if config.get('light_config', 'hold_green_time') == '' else int(config.get('light_config', 'hold_green_time'))
                    hold_time_start = time.time()
                    hold_flag = False

                    cttime_same_type = config.get('pqm_config', 'cttime_same_type')
                    cttimestart_com = config.get('pqm_config', 'cttimestart_com')
                    cttimeend_com = config.get('pqm_config', 'cttimeend_com')
                    ct_time_start = time.time()
                    ct_time_get = '5' if config.get('pqm_config', 'cttimeset') == '' else config.get('pqm_config', 'cttimeset')

                    old_light = ''
                except:
                    message = traceback.format_exc()
                    print(message)
                    logger.error(message)
                    print('GPIO初始化错误')
                    time.sleep(1)
                else:
                    logger.error('GPIO初始化成功')
                    print('GPIO初始化成功')
                    break
            # 定义采集输入变量
            old_trigger_list = [-1, -1, -1, -1]
            # 轮询读取GPIO触发次数，并往参数通道内put发送
            while True:
                try:
                    new_trigger_list = read_GPIO()
                    # 假如距离上一次触发后经过的保持时间小于窗口时间，则无视本次黄灯信号
                    if str(light_open) == '1':
                        if time.time() - hold_time_start < hold_green_time:
                            new_trigger_list[gpio_yellow] = 1
                        else:
                            if hold_flag:
                                logger.info('屏蔽黄灯结束，当前GPIO情况：%s' % new_trigger_list)
                                hold_flag = False

                    if new_trigger_list != old_trigger_list:
                        signal_list = []
                        container_signal_list = []
                        container_signal_value_list = []
                        gpio_light_list = []
                        gpio_light_value_list = []
                        for i in range(4):
                            if new_trigger_list[i] != old_trigger_list[i]:
                                if (i in side_gpio) and str(container_open) == '1':
                                    print('采集到装载箱信号变化---端口%s的值=%s' % (i, new_trigger_list[i]))
                                    logger.info('采集到装载箱信号变化---端口%s的值=%s' % (i, new_trigger_list[i]))
                                    number = side_gpio.index(i)
                                    container_signal_list.append(number)
                                    container_signal_value_list.append(new_trigger_list[i])
                                elif (i in [gpio_green, gpio_yellow, gpio_red]) and new_trigger_list[i] == 0 and str(light_open) == '1':
                                    print('采集到报警灯信号---端口号=%s' % i)
                                    logger.info('采集到报警灯信号---端口号=%s' % i)
                                    if i == gpio_green:
                                        gpio_light_list.append('green')
                                        gpio_light_value_list.append(new_trigger_list[i])
                                        old_light = 'green'
                                    elif i == gpio_yellow:
                                        if old_light == 'green':
                                            if not hold_flag:
                                                hold_time_start = time.time()
                                                hold_flag = True
                                                logger.info('检测到由绿灯变黄灯，无视本次黄灯，并开始屏蔽黄灯，共持续%s秒' % hold_green_time)
                                        else:
                                            gpio_light_list.append('yellow')
                                            gpio_light_value_list.append(new_trigger_list[i])
                                        old_light = 'yellow'
                                    elif i == gpio_red:
                                        gpio_light_list.append('red')
                                        gpio_light_value_list.append(new_trigger_list[i])
                                        old_light = 'red'
                                    if cttime_same_type == '1':
                                        if str(i) == cttimestart_com:
                                            ct_time_start = deepcopy(time.time())
                                            logger.info('CT时间计算开始，当前时间：%s' % ct_time_start)
                                        elif str(i) == cttimeend_com:
                                            ct_time_get = str(round(time.time() - ct_time_start,1))
                                            logger.info('CT时间计算完毕，从%s到%s，总计%s秒' % (ct_time_start,round(time.time(),1),ct_time_get))
                                if (i in gpio_in_com_new) and new_trigger_list[i] == 0:
                                    trigger_times += 1
                                    print('采集到生产信号下降沿---端口号=%s' % i)
                                    logger.info('采集到生产信号下降沿---端口号=%s' % i)
                                    signal_list.append(i)

                        old_trigger_list = deepcopy(new_trigger_list)
                        if hold_flag:
                            old_trigger_list[gpio_yellow] = 1

                        if len(signal_list) > 0:
                            signal_channel.put((trigger_times, '3', 'OK', str(signal_list), ct_time_get))
                        if len(container_signal_list) > 0:
                            container_channel.put((str(container_signal_list), str(container_signal_value_list)))
                        if len(gpio_light_list) > 0:
                            light_channel.put((str(gpio_light_list), str(gpio_light_value_list)))
                except:
                    message = traceback.format_exc()
                    print('轮询采集错误'+message)
                    logger.error('轮询采集错误'+message)
                    signal_channel.put((0, '3', 'FAIL', str([])))
                    break

    # 如果是V0模组，则开启gpio_old的库
    if os.popen('uname -a').read().find('raspberrypi') != -1:
        def workprocess_CheckGPIO_old(self, q):
            import RPi.GPIO as GPIO
            # 定义触发次数参数
            trigger_times = 0
            # 初始化GPIO相关变量
            GPIO.setmode(GPIO.BCM)
            GPIO.setup(int(self.gpio_in_com_old), GPIO.IN, pull_up_down=GPIO.PUD_UP)  # up Button for GPIO-BCM17
            flag = False
            print('GPIO ' + self.gpio_in_com_old + ': ' + str(GPIO.input(int(self.gpio_in_com_old))))
            logger.info('GPIO ' + self.gpio_in_com_old + ': ' + str(GPIO.input(int(self.gpio_in_com_old))))
            while True:
                try:
                    # 检测到0到1的上升沿
                    if (not flag) and (not GPIO.input(int(self.gpio_in_com_old))):
                        time.sleep(.04)
                        if (not flag) and (not GPIO.input(int(self.gpio_in_com_old))):
                            flag = True
                            trigger_times += 1
                            print('收到信号--' + self.gpio_in_com_old)
                            print('GPIO ' + self.gpio_in_com_old + ': ' + str(GPIO.input(int(self.gpio_in_com_old))))
                            logger.info(
                                'GPIO ' + self.gpio_in_com_old + ': ' + str(GPIO.input(int(self.gpio_in_com_old))))
                            q.put((trigger_times, '3', 'OK', str([0])))

                    # 检测到1到0的下降沿
                    elif flag and GPIO.input(int(self.gpio_in_com_old)):
                        time.sleep(.04)
                        if flag and GPIO.input(int(self.gpio_in_com_old)):
                            flag = False
                            print('释放信号--' + self.gpio_in_com_old)
                            print('GPIO ' + self.gpio_in_com_old + ': ' + str(GPIO.input(int(self.gpio_in_com_old))))
                            logger.info(
                                'GPIO ' + self.gpio_in_com_old + ': ' + str(GPIO.input(int(self.gpio_in_com_old))))
                            # lock.acquire()
                            # down_number += 1
                            # lock.release()
                except:
                    message = traceback.format_exc()
                    print(message)
                    logger.error(message)
                    q.put((0, '3', 'FAIL', str([])))
                    print('轮询采集错误')
                    break

    # 工作进程，轮询检查USB触发情况
    def workprocess_CheckUSB(self, q):
        while True:
            try:
                # 连接USB采集器
                while True:
                    try:
                        try:
                            s = serial.Serial('/dev/ttyUSB0', 9600, timeout=0.5)
                            c = bytes.fromhex('5A5D')
                            s.write(c)
                            time.sleep(0.05)
                            n = s.inWaiting()
                            if n:
                                data = str(binascii.b2a_hex(s.read(n)))[2:-1]
                                if data == 'ad':
                                    now_usb = '0'
                                    print('连接USB采集模块成功，USB0')
                                else:
                                    raise
                            else:
                                raise
                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                            try:
                                s = serial.Serial('/dev/ttyUSB1', 9600, timeout=0.5)
                                c = bytes.fromhex('5A5D')
                                s.write(c)
                                time.sleep(0.05)
                                n = s.inWaiting()
                                if n:
                                    data = str(binascii.b2a_hex(s.read(n)))[2:-1]
                                    if data == 'ad':
                                        now_usb = '1'
                                        print('USB采集器连接成功，USB1')
                                    else:
                                        raise
                                else:
                                    raise
                            except:
                                message = traceback.format_exc()
                                print(message)
                                logger.error(message)
                                raise
                    except:
                        message = traceback.format_exc()
                        print(message)
                        logger.error(message)
                        print('连接USB采集模块失败!请检查是否连接!')
                        time.sleep(2)
                        q.put((0, '3', 'FAIL', str([])))
                    else:
                        q.put((0, now_usb, 'OK', str([])))
                        break
                # 定义采集输入变量
                new_select_list = ['1', '1', '1', '1']
                old_select_list = ['1', '1', '1', '1']
                # 定义usbcheck时间计数器
                number = 0
                # 定义触发次数参数
                trigger_times = 0
                while True:
                    try:
                        number = number + 1
                        if number > 2500:
                            number = 0
                            s = serial.Serial('/dev/ttyUSB' + now_usb, 9600, timeout=0.5)
                            c = bytes.fromhex('5A5D')
                            s.write(c)
                            time.sleep(0.05)
                            n = s.inWaiting()
                            if n:
                                data = str(binascii.b2a_hex(s.read(n)))[2:-1]
                                if data == 'ad':
                                    print('间隔120s检查USB采集模块成功，USB' + now_usb)
                                else:
                                    print('间隔120s检查USB采集模块失败失败，USB' + now_usb)
                                    raise
                            else:
                                print('间隔120s检查USB采集模块失败失败，USB' + now_usb)
                                raise
                        time.sleep(0.05)
                        n = s.inWaiting()
                    except:
                        message = traceback.format_exc()
                        print(message)
                        logger.error(message)
                        print('间隔120s检查USB采集模块检查程序运行失败，USB' + now_usb)
                        break
                    else:
                        if n:
                            new_select = str(binascii.b2a_hex(s.read(n)))[2:-1]
                            new_select = str(bin(int(new_select, 16))[2:]).rjust(4, '0')
                            for i in range(len(new_select_list)):
                                new_select_list[i] = new_select[i]
                            signal_list = []
                            for i in range(4):
                                if new_select_list[i] != old_select_list[i] and new_select_list[i] == '0':
                                    trigger_times += 1
                                    print('采集到信号---' + str(i))
                                    signal_list.append(i)
                            print('old_select_list:' + str(old_select_list))
                            print('new_select_list:' + str(new_select_list))
                            old_select_list = deepcopy(new_select_list)
                            q.put((trigger_times, now_usb, 'OK', str(signal_list)))
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)

    # 工作线程，上传PQM
    def workthreading_uptopqm(self):
        while True:
            try:
                for i in range(len(self.signal_list)):
                    if self.signal_list[i] != '':
                        signal_number = int(self.signal_list[i])
                        self.signal_list[i] = ''
                        if signal_number in self.gpio_in_com_new:
                            interfaceID = self.shebei_bianma[signal_number]
                            self.pqm_allnumber_list[signal_number] += 1


                            # pqm_http变量
                            url_pqm = "http://" + self.url_pqm + ":8090/sensordata"
                            headers_pqm = {
                                'Content-Type': "application/json"
                            }
                            params_pqm = {
                                'sensorId': "UploadMachineData"
                            }
                            if self.light_status == 'green':
                                status = int(self.code_green)
                            elif self.light_status == 'yellow':
                                status = int(self.code_yellow)
                            elif self.light_status == 'red':
                                status = int(self.code_red)
                            else:
                                status = 0
                            data_pqm = [{
                                'interfaceID': interfaceID,
                                'status': status,
                                'statusCode': '',
                                'passQty': self.pqm_allnumber_list[signal_number],
                                'failQty': 0,
                                'errorCnt': 0,
                                'errorTimes': 0,
                                # 'cycleTime': round(time.time() - self.pqm_cttime_list[signal_number], 1),
                                'cycleTime': int(self.cttimeset) + round(random.random(), 2),
                                'runningTime': round(time.time() - self.pqm_starttime, 1),
                                'waitingTime': 0,
                                'selfCheck': 1,
                                'inputQty': self.pqm_allnumber_list[signal_number],
                                'barcode': 'NFA',
                                'model': self.model,
                                'paramList': [{'paramCode': 'CT_M', 'paramValue': '0'},
                                              {'paramCode': 'CT_Q', 'paramValue': '0'}]
                            }]
                            try:
                                # logger.info('上传PQM数据--body：' + str(data_pqm).replace("'", '"'))
                                # logger.info('上传PQM数据--params：' + str(params_pqm).replace("'", '"'))
                                self.pqm_cttime_list[signal_number] = deepcopy(time.time())
                                self.pqm_hold_flag_list[signal_number] = deepcopy(True)
                                logger.info('url_pqm=%s\ndata_pqm=%s\nheaders_pqm=%s\nparams_pqm=%s' % (
                                    url_pqm, data_pqm, headers_pqm, params_pqm))
                                # 发送数据并接收返回数据
                                receive = requests.request("POST", url_pqm, data=str(data_pqm), headers=headers_pqm,
                                                           params=params_pqm)
                                print(receive)
                                logger.info('上传PQM成功!\nPQM返回:\n%s'%receive)
                                # logger.error('PQM返回:')
                                # logger.error(receive)
                                self.setini_lock.acquire()
                                self.config.set('pqm_data', 'status', '0,0,0,0')
                                self.config.set('pqm_data', 'statusCode', ',,,')
                                self.config.set('pqm_data', 'passQty', str(self.pqm_allnumber_list)[1:-1])
                                self.config.set('pqm_data', 'failQty', '0,0,0,0')
                                self.config.set('pqm_data', 'errorCnt', '0,0,0,0')
                                self.config.set('pqm_data', 'errorTimes', '0,0,0,0')
                                self.config.set('pqm_data', 'cycleTime', '0,0,0,0')
                                self.config.set('pqm_data', 'runningTime', str(round(time.time() - self.pqm_starttime, 1)))
                                self.config.set('pqm_data', 'waitingTime', '0,0,0,0')
                                self.config.set('pqm_data', 'inputQty', str(self.pqm_allnumber_list)[1:-1])

                                self.config.write(open(work_address + '/set.ini', 'w', encoding='utf-8-sig'))
                                self.config.write(open(work_address + '/set_backup.ini', 'w', encoding='utf-8-sig'))
                                self.setini_lock.release()
                                logger.info('保存PQM数据到本地set.ini完成')
                                # str转换为字典列表
                                # receive = eval(receive.text)
                                # return receive
                            except:
                                message = traceback.format_exc()
                                print(message)
                                # logger.error(message)
                                print('上传PQM失败!')
                                logger.error('上传PQM失败!\n%s'%message)
                                # return receive
                time.sleep(0.2)
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)

    # 工作线程，保持上传PQM数据
    def workthreading_uptopqm_hold(self):
        while True:
            try:
                for i in range(len(self.shebei_bianma)):
                    signal_number = i
                    if signal_number in self.gpio_in_com_new:
                        if time.time() - self.pqm_cttime_list[signal_number] <= self.hold_max_time and \
                                self.pqm_hold_flag_list[signal_number]:
                            interfaceID = self.shebei_bianma[signal_number]
                            url_pqm = "http://" + self.url_pqm + ":8090/sensordata"
                            headers_pqm = {
                                'Content-Type': "application/json"
                            }
                            params_pqm = {
                                'sensorId': "UploadMachineData"
                            }
                            if self.light_status == 'green':
                                status = int(self.code_green)
                            elif self.light_status == 'yellow':
                                status = int(self.code_yellow)
                            elif self.light_status == 'red':
                                status = int(self.code_red)
                            else:
                                status = 0
                            data_pqm = [{
                                'interfaceID': interfaceID,
                                'status': status,
                                'statusCode': '',
                                'passQty': self.pqm_allnumber_list[signal_number],
                                'failQty': 0,
                                'errorCnt': 0,
                                'errorTimes': 0,
                                # 'cycleTime': round(time.time() - self.pqm_cttime_list[signal_number], 1),
                                'cycleTime': int(self.cttimeset) + round(random.random(), 2),
                                'runningTime': round(time.time() - self.pqm_starttime, 1),
                                'waitingTime': 0,
                                'selfCheck': 1,
                                'inputQty': self.pqm_allnumber_list[signal_number],
                                'barcode': 'NFA',
                                'model': self.model,
                                'paramList': [{'paramCode': 'CT_M', 'paramValue': '0'},
                                              {'paramCode': 'CT_Q', 'paramValue': '0'}]
                            }]
                            try:
                                # logger.info('上传PQM_hold数据--body：' + str(data_pqm).replace("'", '"'))
                                # logger.info('上传PQM_hold数据--params：' + str(params_pqm).replace("'", '"'))
                                logger.info('url_pqm=%s\ndata_pqm=%s\nheaders_pqm=%s\nparams_pqm=%s' % (
                                    url_pqm, data_pqm, headers_pqm, params_pqm))
                                self.pqm_cttime_list[signal_number] = deepcopy(time.time())
                                # 发送数据并接收返回数据
                                receive = requests.request("POST", url_pqm, data=str(data_pqm), headers=headers_pqm,
                                                           params=params_pqm)
                                print(receive)
                                logger.info('保持上传PQM成功!\nPQM返回:\n%s' % receive)
                                # self.config.set('pqm_data', 'status', '0,0,0,0')
                                # self.config.set('pqm_data', 'statusCode', ',,,')
                                # self.config.set('pqm_data', 'passQty', str(self.pqm_allnumber_list)[1:-1])
                                # self.config.set('pqm_data', 'failQty', '0,0,0,0')
                                # self.config.set('pqm_data', 'errorCnt', '0,0,0,0')
                                # self.config.set('pqm_data', 'errorTimes', '0,0,0,0')
                                # self.config.set('pqm_data', 'cycleTime', '0,0,0,0')
                                # self.config.set('pqm_data', 'runningTime', str(round(time.time() - self.pqm_starttime, 1)))
                                # self.config.set('pqm_data', 'waitingTime', '0,0,0,0')
                                # self.config.set('pqm_data', 'inputQty', str(self.pqm_allnumber_list)[1:-1])
                                # self.config.write(open(work_address + '/set.ini', 'w', encoding='utf-8-sig'))
                                # self.config.write(open(work_address + '/set_backup.ini', 'w', encoding='utf-8-sig'))
                                # logger.info('保存PQM数据到本地set.ini完成')

                                # str转换为字典列表
                                # receive = eval(receive.text)
                                # return receive
                            except:
                                message = traceback.format_exc()
                                print(message)
                                # logger.error(message)
                                print('上传PQM_hold失败!')
                                logger.error('上传PQM_hold失败!\n%s'%message)
                                # return receive
                        else:
                            self.pqm_hold_flag_list[signal_number] = deepcopy(False)
                            self.pqm_cttime_list[signal_number] = deepcopy(time.time())
                time.sleep(60 if self.hold_pcs_time <= 60 else int(self.hold_pcs_time))
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)

    # 每天达到上班班次后，自动重启
    def workthreading_pqm_today_restart(self):
        start_time = datetime.datetime.now()
        logger.info('班次监控已启动，程式开启时间：%s' % str(start_time))
        while True:
            try:
                now_time = datetime.datetime.now()
                now_year = int(str(now_time)[:4])
                now_month = int(str(now_time)[5:7])
                now_day = int(str(now_time)[8:10])

                # 循环检查已到达某个上班班次
                for i in range(len(self.today_restart_time)):
                    restart_hour = int(self.today_restart_time[i][:2])
                    restart_min = int(self.today_restart_time[i][2:4])
                    if start_time < datetime.datetime(now_year, now_month, now_day, restart_hour, restart_min) < now_time:
                        logger.info(
                            '已到新的上班次%s，准备自动上传黄灯到PQM!\n当前时间：%s'%(str(datetime.datetime(now_year, now_month, now_day, restart_hour, restart_min)),str(datetime.datetime.now())[11:16]))
                        try:
                            for j in range(len(self.shebei_bianma)):
                                if j in self.gpio_in_com_new:
                                    interfaceID = self.shebei_bianma[j]
                                    url_pqm = "http://" + self.url_pqm + ":8090/sensordata"
                                    headers_pqm = {
                                        'Content-Type': "application/json"
                                    }
                                    params_pqm = {
                                        'sensorId': "UploadMachineData"
                                    }
                                    status = int(self.code_yellow)
                                    data_pqm = [{
                                        'interfaceID': interfaceID,
                                        'status': status,
                                        'statusCode': '',
                                        'passQty': 0,
                                        'failQty': 0,
                                        'errorCnt': 0,
                                        'errorTimes': 0,
                                        'cycleTime': 0,
                                        'runningTime': 0,
                                        'waitingTime': 0,
                                        'selfCheck': 1,
                                        'inputQty': 0,
                                        'barcode': 'NFA',
                                        'model': '',
                                        'paramList': [{'paramCode': 'CT_M', 'paramValue': '0'},
                                                      {'paramCode': 'CT_Q', 'paramValue': '0'}]
                                    }]
                                    try:
                                        logger.info('url_pqm=%s\ndata_pqm=%s\nheaders_pqm=%s\nparams_pqm=%s' % (
                                            url_pqm, data_pqm, headers_pqm, params_pqm))
                                        # 发送数据并接收返回数据
                                        receive = requests.request("POST", url_pqm, data=str(data_pqm), headers=headers_pqm,
                                                                   params=params_pqm)
                                        print(receive)
                                        logger.info('重启前发送黄灯到PQM成功!\nPQM返回:\n%s' % receive)
                                        self.setini_lock.acquire()
                                        self.config.set('pqm_data', 'status', '')
                                        self.config.set('pqm_data', 'statusCode', '')
                                        self.config.set('pqm_data', 'passQty', '')
                                        self.config.set('pqm_data', 'failQty', '')
                                        self.config.set('pqm_data', 'errorCnt', '')
                                        self.config.set('pqm_data', 'errorTimes', '')
                                        self.config.set('pqm_data', 'cycleTime', '')
                                        self.config.set('pqm_data', 'runningTime', '')
                                        self.config.set('pqm_data', 'waitingTime', '')
                                        self.config.set('pqm_data', 'inputQty', '')
                                        self.config.set('light_config', 'status_time_list', '')


                                        self.config.write(open(work_address + '/set.ini', 'w', encoding='utf-8-sig'))
                                        self.config.write(open(work_address + '/set_backup.ini', 'w', encoding='utf-8-sig'))
                                        self.setini_lock.release()
                                        logger.info('重启前PQM数据重置后保存PQM数据到本地set.ini完成')
                                    except:
                                        message = traceback.format_exc()
                                        print(message)
                                        print('重启前上传黄灯到PQM失败!')
                                        logger.error('重启前上传黄灯到PQM失败!\n%s' % message)


                        except:
                            message = traceback.format_exc()
                            print(message)
                            logger.error(message)
                        else:
                            logger.info('已触发到新的上班次自动重启!\n' + '当前时间：' + str(datetime.datetime.now())[11:16])
                            time.sleep(5)
                            os.system('reboot')

            except:
                message = traceback.format_exc()
                print(message)
                logger.warning('工作线程出错：每天达到上班班次后，自动重启\n' + message)
                print('工作线程出错：每天达到上班班次后，自动重启')
            else:
                time.sleep(60)

    # 工作线程，实时显示程序运行时间
    def workthreading_RunningTime(self):
        if self.auto_reboot == '1':
            logger.info('自动重启功能已开启!\n'+'自动重启时间：' + str(self.reboot_time))
        start = time.time()
        while True:
            try:
                time.sleep(1)
                end = time.time()
                seconds = end - start
                m, s = divmod(seconds, 60)
                h, m = divmod(m, 60)
                str_time = ('%d:%02d:%02d' % (h, m, s))
                self.str_runtime.set(str_time)
                self.str_nowtime.set(str(datetime.datetime.now())[:19])
                if self.auto_reboot == '1':
                    if str(datetime.datetime.now())[11:16] == self.reboot_time[:5]:
                        logger.info('已触发自动重启!\n'+'当前时间：' + str(datetime.datetime.now())[11:16])
                        os.system('reboot')
            except:
                message = traceback.format_exc()
                print(message)
                logger.warning('工作线程出错：实时显示程序运行时间\n'+message)
                print('工作线程出错：实时显示程序运行时间')

    # 工作线程，实时查询并显示目前系统与MES服务器连接情况，每隔一段时间PingMES服务器显示延时
    def workthreading_PingMES(self):
        ping_err_times = 0
        while True:
            try:
                ping = os.popen('ping -c 1 10.148.192.37')
                if str(ping) != '[]':
                    # if self.ping_reboot == '1':
                    #     ping_err_times = 0
                    try:
                        ping_number = str(ping.readlines()).replace('\\n', '').split('time=')[1].split(' ms')[0]
                        ping_str = ping_number + 'ms'
                        logger.info('MES网络连接延时:' + str(ping_str))
                        self.str_network.set(ping_str)
                    except:
                        pass
                    # self.label_network.configure(fg='#FFFFFF')
                else:
                    # if self.ping_reboot == '1':
                    #     ping_err_times += 1
                    #     if ping_err_times >= int(self.ping_reboot_times):
                    #         logger.info('ping MES多次失败，已触发自动重启!')
                    #         logger.info('当前时间：' + str(datetime.datetime.now())[11:16])
                    #         os.system('sudo reboot')
                    logger.error('MES网络连接断开!')
                    self.str_network.set('Fail')
                    # self.label_network.configure(fg='red')
            except:
                message = traceback.format_exc()
                print(message)
                # if self.ping_reboot == '1':
                #     ping_err_times += 1
                #     if ping_err_times >= int(self.ping_reboot_times):
                #         logger.info('ping MES多次失败，已触发自动重启!')
                #         logger.info('当前时间：' + str(datetime.datetime.now())[11:16])
                #         os.system('sudo reboot')
                logger.error('MES网络测试失败,连接断开!\n'+message)

                self.str_network.set('Fail')
                # self.label_network.configure(fg='red')
            time.sleep(20)

    # 更换工单按钮事件
    def changemo(self):
        global changmoflag
        self.setini_lock.acquire()
        self.config.write(open(work_address + '/set.ini', 'w', encoding='utf-8-sig'))
        self.config.write(open(work_address + '/set_backup.ini', 'w', encoding='utf-8-sig'))
        self.setini_lock.release()
        self.window.destroy()
        changmoflag = True
        window = tk.Tk()
        mainclass = ModelWindow(window)
        mainclass.run()
        window.mainloop()

    def qingkong(self):
        print('用户手动清空出料口数量')
        logger.info('用户手动清空出料口数量')
        try:
            print('当前出料口名称：%s,当前出料箱数据：%s' % (self.side_now, self.side_number))
            logger.info('当前出料口名称：%s,当前出料箱数据：%s' % (self.side_now, self.side_number))
            self.side_number[self.side_name.index(self.side_now)] = 0
            print('最新出料口名称：%s,最新出料箱数据：%s' % (self.side_now, self.side_number))
            logger.info('最新出料口名称：%s,最新出料箱数据：%s' % (self.side_now, self.side_number))

            number_max = 1
            for i in range(len(self.modelset_xls)):
                if str(self.modelset_xls[i]['机种型号']) == str(self.model):
                    number_max = int(self.modelset_xls[i]['单箱数量'])
                    break
                elif i == len(self.modelset_xls) - 1:
                    print('model_set机种文件内无法查询到' + str(self.model) + '该机种!')
                    logger.info('model_set机种文件内无法查询到' + str(self.model) + '该机种!')
                    for j in range(len(self.modelset_xls) - 1, -1, -1):
                        if str(self.modelset_xls[j]['机种型号']) == '其他':
                            number_max = int(self.modelset_xls[j]['单箱数量'])
                            print(
                                'model_set机种文件内无法查询到%s机种,自动使用其他型号配置%s' % (
                                    self.model, number_max))
                            logger.info(
                                'model_set机种文件内无法查询到%s机种,自动使用其他型号配置%s' % (
                                    self.model, number_max))
                            break
                        elif j == 0:
                            print('model_set机种文件内无法查询到' + str(
                                self.model) + '该机种!且找不到其他选项')
                            logger.info('model_set机种文件内无法查询到' + str(
                                self.model) + '该机种!且找不到其他选项')

            if int(self.side_number[self.side_name.index(self.side_now)]) >= number_max:
                for i in range(len(self.side_name)):
                    if self.side_now != self.side_name[i] and int(self.side_number[i]) < number_max and \
                            self.side_status[i] == 1:
                        self.side_now = self.side_name[i]
                        # 将压力阀打到对应的位置
                        self.container_control(self.side_now)
                        print('已更新出料口为：%s' % self.side_now)
                        logger.info('已更新出料口为：%s' % self.side_now)
                    elif i == len(self.side_name) - 1:
                        print('无法找到未满的出料箱,故无法切换出料口')
                        logger.info('无法找到未满的出料箱,故无法切换出料口')
                        # self.container_control('OUT-PCS')

            str_show = ''
            for i in range(len(self.side_name)):
                str_show = str_show + ' ' + '%s=%s' % (self.side_name[i], self.side_number[i])
            str_show = str_show + ' ' + 'Now=%s Max=%s' % (self.side_now, number_max)
            self.str_container.set(str_show)
            self.setini_lock.acquire()
            self.config.set('container_config', 'side_number', str(self.side_number)[1:-1].replace(' ', ''))
            self.config.set('container_config', 'side_now', str(self.side_now))

            self.config.write(open(work_address + '/set.ini', 'w', encoding='utf-8-sig'))
            self.config.write(open(work_address + '/set_backup.ini', 'w', encoding='utf-8-sig'))
            self.setini_lock.release()
        except:
            message = traceback.format_exc()
            print(message)
            logger.info(message)
            if self.language == 'en':
                message_list = self.cut('Delete Error', 25)
            else:
                message_list = self.cut('用户手动清空出料口数量报错', 25)
            message_str = ''
            for i in range(len(message_list)):
                if i == len(message_list) - 1:
                    message_str = message_str + message_list[i]
                else:
                    message_str = message_str + message_list[i] + '\n'
            self.label_keyinmessage.configure(foreground='red')
            self.str_keyinmessage.set(message_str)
            try:
                self.messagewindow = tk.Toplevel(self.window)
                self.messagewindow.overrideredirect(True)
                if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                    self.messagewindow.geometry('+555+430')
                elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                    self.messagewindow.geometry('+555+430')
                else:
                    self.messagewindow.geometry('+680+609')
                # 临时数据已清空, 可重新输入!
                # The keyin data has been cleared. You can re-keyin!
                if self.language == 'en':
                    msg = tk.Message(self.messagewindow, text='Delete Error',
                                     font=('Arial', 25), fg='white', bg='#00c7a6',
                                     width=500)
                else:
                    msg = tk.Message(self.messagewindow, text='用户手动清空出料口数量报错',
                                     font=('Arial', 25), fg='white', bg='#00c7a6',
                                     width=500)
                msg.pack()
                self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
            # # 显示上传成功
            # self.label_keyinmessage.configure(foreground='red')
            # if self.language == 'en':
            #     self.str_keyinmessage.set('Delete Error')
            # else:
            #     self.str_keyinmessage.set('用户手动清空出料口数量报错')
        else:
            if self.language == 'en':
                message_list = self.cut('Delete OK', 25)
            else:
                message_list = self.cut('用户手动清空出料口%s数量OK' % self.side_now, 25)
            message_str = ''
            for i in range(len(message_list)):
                if i == len(message_list) - 1:
                    message_str = message_str + message_list[i]
                else:
                    message_str = message_str + message_list[i] + '\n'
            self.label_keyinmessage.configure(foreground='green')
            self.str_keyinmessage.set(message_str)
            try:
                self.messagewindow = tk.Toplevel(self.window)
                self.messagewindow.overrideredirect(True)
                if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                    self.messagewindow.geometry('+555+430')
                elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                    self.messagewindow.geometry('+555+430')
                else:
                    self.messagewindow.geometry('+680+609')
                # 临时数据已清空, 可重新输入!
                # The keyin data has been cleared. You can re-keyin!
                if self.language == 'en':
                    msg = tk.Message(self.messagewindow, text='Delete OK',
                                     font=('Arial', 25), fg='white', bg='#00c7a6',
                                     width=500)
                else:
                    msg = tk.Message(self.messagewindow, text='用户手动清空出料口%s数量OK' % self.side_now,
                                     font=('Arial', 25), fg='white', bg='#00c7a6',
                                     width=500)
                msg.pack()
                self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)

            # # 显示上传成功
            # self.label_keyinmessage.configure(foreground='green')
            # if self.language == 'en':
            #     self.str_keyinmessage.set('Delete OK')
            # else:
            #     self.str_keyinmessage.set('用户手动清空出料口%s数量OK' % self.side_now)

    def huanwei(self):
        print('用户手动更换出料口位置')
        logger.info('用户手动更换出料口位置')
        try:
            print("self.side_name=%s" % self.side_name)
            print("self.side_now=%s" % self.side_now)
            if self.side_name.index(self.side_now) < len(self.side_name) - 1:
                number = self.side_name.index(self.side_now) + 1
            else:
                number = 0
            self.side_now = self.side_name[number]
            # 将压力阀打到对应的位置
            self.container_control(self.side_now)
            print('已更新出料口为：%s' % self.side_now)
            logger.info('已更新出料口为：%s' % self.side_now)

            number_max = 1
            for i in range(len(self.modelset_xls)):
                if str(self.modelset_xls[i]['机种型号']) == str(self.model):
                    number_max = int(self.modelset_xls[i]['单箱数量'])
                    break
                elif i == len(self.modelset_xls) - 1:
                    print('model_set机种文件内无法查询到' + str(self.model) + '该机种!')
                    logger.info('model_set机种文件内无法查询到' + str(self.model) + '该机种!')
                    for j in range(len(self.modelset_xls) - 1, -1, -1):
                        if str(self.modelset_xls[j]['机种型号']) == '其他':
                            number_max = int(self.modelset_xls[j]['单箱数量'])
                            print(
                                'model_set机种文件内无法查询到%s机种,自动使用其他型号配置%s' % (
                                    self.model, number_max))
                            logger.info(
                                'model_set机种文件内无法查询到%s机种,自动使用其他型号配置%s' % (
                                    self.model, number_max))
                            break
                        elif j == 0:
                            print('model_set机种文件内无法查询到' + str(
                                self.model) + '该机种!且找不到其他选项')
                            logger.info('model_set机种文件内无法查询到' + str(
                                self.model) + '该机种!且找不到其他选项')

            str_show = ''
            for i in range(len(self.side_name)):
                str_show = str_show + ' ' + '%s=%s' % (self.side_name[i], self.side_number[i])
            str_show = str_show + ' ' + 'Now=%s Max=%s' % (self.side_now, number_max)
            self.str_container.set(str_show)
            self.setini_lock.acquire()
            self.config.set('container_config', 'side_number', str(self.side_number)[1:-1].replace(' ', ''))
            self.config.set('container_config', 'side_now', str(self.side_now))

            self.config.write(open(work_address + '/set.ini', 'w', encoding='utf-8-sig'))
            self.config.write(open(work_address + '/set_backup.ini', 'w', encoding='utf-8-sig'))
            self.setini_lock.release()
        except:
            message = traceback.format_exc()
            print(message)
            logger.info(message)
            if self.language == 'en':
                message_list = self.cut('Change Error', 25)
            else:
                message_list = self.cut('用户手动更换出料口位置报错', 25)
            message_str = ''
            for i in range(len(message_list)):
                if i == len(message_list) - 1:
                    message_str = message_str + message_list[i]
                else:
                    message_str = message_str + message_list[i] + '\n'
            self.label_keyinmessage.configure(foreground='red')
            self.str_keyinmessage.set(message_str)
            try:
                self.messagewindow = tk.Toplevel(self.window)
                self.messagewindow.overrideredirect(True)
                if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                    self.messagewindow.geometry('+555+430')
                elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                    self.messagewindow.geometry('+555+430')
                else:
                    self.messagewindow.geometry('+680+609')
                # 临时数据已清空, 可重新输入!
                # The keyin data has been cleared. You can re-keyin!
                if self.language == 'en':
                    msg = tk.Message(self.messagewindow, text='Change Error',
                                     font=('Arial', 25), fg='white', bg='#00c7a6',
                                     width=500)
                else:
                    msg = tk.Message(self.messagewindow, text='用户手动更换出料口位置报错',
                                     font=('Arial', 25), fg='white', bg='#00c7a6',
                                     width=500)
                msg.pack()
                self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
            # self.label_keyinmessage.configure(foreground='red')
            # if self.language == 'en':
            #     self.str_keyinmessage.set('Change Error')
            # else:
            #     self.str_keyinmessage.set('用户手动更换出料口位置报错')
        else:
            if self.language == 'en':
                message_list = self.cut('Change OK', 25)
            else:
                message_list = self.cut('用户手动更换出料口位置为：%s' % self.side_now, 25)
            message_str = ''
            for i in range(len(message_list)):
                if i == len(message_list) - 1:
                    message_str = message_str + message_list[i]
                else:
                    message_str = message_str + message_list[i] + '\n'
            self.label_keyinmessage.configure(foreground='green')
            self.str_keyinmessage.set(message_str)
            try:
                self.messagewindow = tk.Toplevel(self.window)
                self.messagewindow.overrideredirect(True)
                if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                    self.messagewindow.geometry('+555+430')
                elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                    self.messagewindow.geometry('+555+430')
                else:
                    self.messagewindow.geometry('+680+609')
                # 临时数据已清空, 可重新输入!
                # The keyin data has been cleared. You can re-keyin!
                if self.language == 'en':
                    msg = tk.Message(self.messagewindow, text='Change OK',
                                     font=('Arial', 25), fg='white', bg='#00c7a6',
                                     width=500)
                else:
                    msg = tk.Message(self.messagewindow, text='用户手动更换出料口位置为：%s' % self.side_now,
                                     font=('Arial', 25), fg='white', bg='#00c7a6',
                                     width=500)
                msg.pack()
                self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
            # # 显示上传成功
            # self.label_keyinmessage.configure(foreground='green')
            # if self.language == 'en':
            #     self.str_keyinmessage.set('Change OK')
            # else:
            #     self.str_keyinmessage.set('用户手动更换出料口位置为：%s' % self.side_now)

    def hujiao(self):
        print('用户手动呼叫AGV小车')
        logger.info('用户手动呼叫AGV小车')
        try:
            # 上传MES
            # routing变量
            url_routing = 'http://' + self.url_mes + ':10101/TDC/DELTA_DEAL_TEST_DATA_I'
            # 测试QA环境
            # url_routing = 'http://' + '10.148.200.28' + ':10101/TDC/DELTA_DEAL_TEST_DATA_I'
            params_routing = {
                'sign': ''
            }
            nowtime = str(datetime.datetime.now())
            random_code = nowtime[20:26]+str(random.randint(1, 254))+str(random.randint(1, 254))
            # random_code = random_code[:39]
            self.container_NO = random_code
            data_routing = {
                'factory': self.usr_factory,
                'testType': 'FG_CONTAINER_LINK',
                'routingData': {
                    'mo': self.mo,
                    'model': self.model,
                    'line': self.line,
                    'section': self.section,
                    'group': self.group,
                    'station': self.station,
                    'containerNo': self.container_NO,
                    'qty': int(self.side_number[self.side_name.index(self.side_now)]),
                    'port': self.side_now,
                    'needAGV': 'Y'
                },
                'testData': []
            }
            # 开始上传
            # 将body的数据转换为字符串格式
            data_str = json.dumps(data_routing, sort_keys=False)
            # 字符串拼接md5加密
            src = self.secretkey + data_str
            md5 = self.keymd5(src)
            params_routing['sign'] = md5
            # 发送数据并接收返回数据
            print(url_routing)
            print(data_routing)
            print(self.headers)
            print(params_routing)
            logger.info('url_routing=%s\ndata_routing=%s\nheaders=%s\nparams_routing=%s' % (
            url_routing, data_routing, self.headers, params_routing))
            ############################################################
            if self.not_upload == '1':
                print('MES装箱上传成功---receive: 测试用不上传MES')
                logger.info('MES装箱上传成功---receive: 测试用不上传MES')
                if self.language == 'en':
                    message_list = self.cut('Uploaded AGV to MES TEST OK', 25)
                else:
                    message_list = self.cut('(测试模式)MES呼叫AGV小车已上传:%s'%str(data_str).replace(" ","").replace("\n",""), 25)
                message_str = ''
                # message_str =  message_str
                for i in range(len(message_list)):
                    if i == len(message_list) - 1:
                        message_str = message_str + message_list[i]
                    else:
                        message_str = message_str + message_list[i] + '\n'
                # message_str =  message_str
                self.label_keyinmessage.configure(foreground='green')
                self.str_keyinmessage.set(message_str)
                # routing_post = {'result': 'OK',
                #                 'description': 'MES装箱上传成功---receive: 测试用不上传MES'}
                # return routing_post
            ############################################################
            else:
                try:
                    # logger.info('上传MES装箱数据--body：' + str(data_str))
                    # logger.info('上传MES装箱数据--params：' + str(params_routing))
                    receive = requests.request('POST', url_routing, data=data_str, headers=self.headers,
                                               params=params_routing, timeout=20)
                    try:
                        receive = json.loads(receive.text)
                    except:
                        receive = eval(receive.text)
                    # print(receive['result'])
                    # print(receive['description'])
                    print('上传MES装箱成功：' + str(receive))
                    logger.info('上传MES装箱成功---receive: ' + str(receive))
                except:
                    message = traceback.format_exc()
                    print(message)
                    logger.error(message)
                    print('上传MES装箱失败!')
                    logger.error('上传MES装箱失败!')
                    message = traceback.format_exc()
                    print(message)
                    logger.info(message)
                    if self.language == 'en':
                        message_list = self.cut('Uploaded AGV to MES Error', 25)
                    else:
                        message_list = self.cut('MES呼叫AGV小车上传失败', 25)
                    message_str = ''
                    for i in range(len(message_list)):
                        if i == len(message_list) - 1:
                            message_str = message_str + message_list[i]
                        else:
                            message_str = message_str + message_list[i] + '\n'
                    self.label_keyinmessage.configure(foreground='red')
                    self.str_keyinmessage.set(message_str)
                    try:
                        self.messagewindow = tk.Toplevel(self.window)
                        self.messagewindow.overrideredirect(True)
                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.messagewindow.geometry('+555+430')
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.messagewindow.geometry('+555+430')
                        else:
                            self.messagewindow.geometry('+680+609')
                        # 临时数据已清空, 可重新输入!
                        # The keyin data has been cleared. You can re-keyin!
                        if self.language == 'en':
                            msg = tk.Message(self.messagewindow, text='Uploaded AGV to MES Error',
                                             font=('Arial', 25), fg='white', bg='#00c7a6',
                                             width=500)
                        else:
                            msg = tk.Message(self.messagewindow, text='MES呼叫AGV小车上传失败',
                                             font=('Arial', 25), fg='white', bg='#00c7a6',
                                             width=500)
                        msg.pack()
                        self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
                    except:
                        message = traceback.format_exc()
                        print(message)
                        logger.error(message)
                else:
                    if self.language == 'en':
                        message_list = self.cut('Uploaded AGV to MES', 25)
                    else:
                        message_list = self.cut('MES呼叫AGV小车已上传:%s' % data_str, 25)
                    message_str = ''
                    for i in range(len(message_list)):
                        if i == len(message_list) - 1:
                            message_str = message_str + message_list[i]
                        else:
                            message_str = message_str + message_list[i] + '\n'
                    self.label_keyinmessage.configure(foreground='green')
                    self.str_keyinmessage.set(message_str)
                    try:
                        self.messagewindow = tk.Toplevel(self.window)
                        self.messagewindow.overrideredirect(True)
                        if self.screen_type == '1280x768' or self.screen_type == '1280*768':
                            self.messagewindow.geometry('+555+430')
                        elif self.screen_type == '1024x768' or self.screen_type == '1024*768':
                            self.messagewindow.geometry('+555+430')
                        else:
                            self.messagewindow.geometry('+680+609')
                        # 临时数据已清空, 可重新输入!
                        # The keyin data has been cleared. You can re-keyin!
                        if self.language == 'en':
                            msg = tk.Message(self.messagewindow, text='Uploaded AGV to MES',
                                             font=('Arial', 25), fg='white', bg='#00c7a6',
                                             width=500)
                        else:
                            msg = tk.Message(self.messagewindow, text='MES呼叫AGV小车已上传:%s' % data_str,
                                             font=('Arial', 25), fg='white', bg='#00c7a6',
                                             width=500)
                        msg.pack()
                        self.messagewindow.after(1000, self.check_if_running, self.messagewindow)
                    except:
                        message = traceback.format_exc()
                        print(message)
                        logger.error(message)
        except:
            message = traceback.format_exc()
            print(message)
            logger.error(message)

    # 发送登录消息到CTCT
    def ClientState_toCTCT(self):
        for i in range(5):
            try:
                logindata = {
                    "Factory": self.ctct_factory,
                    "ProductFactory": self.ctct_produce,
                    "Line": self.ctct_line,
                    "Station": self.ctct_station,
                    "Host": 'None',
                    "IP": self.userIP,
                    "Port": self.ctct_port,
                    "State": "OnLine",
                    "Mo": self.mo,
                    "Model": self.model,
                    "Section": self.section,
                    "Group": self.group
                }
                print('----发送登录数据到CTCT=%s' % logindata)
                logger.info('----发送登录数据到CTCT=%s' % logindata)
                receive = self.sendmqtt(self.ctct_selflog, 'ClientState', logindata, '', '')
            except:
                message = traceback.format_exc()
                receive = {'result': 'FAIL',
                           'description': message}
            if receive['result'] == 'OK':
                print(str(receive))
                logger.info(str(receive))
                return receive
            else:
                time.sleep(2)

    # 发送实时数据到CTCT
    def CheckState_returnCTCT(self, lineData, all_number, pass_number, err_number, reson):
        for i in range(len(self.CheckState_returnCTCT_list) - 1, -1, -1):
            try:
                if reson == 'MO NOT EXISTS!':
                    reson = '该工单已过期!'
                kanbandata = {
                    "area": self.ctct_produce,
                    "device": self.line,
                    "name": self.mo,
                    "runstate": "1",
                    "reson": reson,
                    "stoptime": "none",
                    "num": str(all_number),
                    "pass": str(pass_number),
                    "fail": str(err_number),
                    "model": self.model,
                    "ip": self.userIP,
                    "cl": self.chanliang
                }
                StateInfor = {
                    "Result": "Success",
                    "ResultCode": 0,
                    "Factory": self.ctct_factory,
                    "ProductFactory": self.ctct_produce,
                    "Line": self.ctct_line,
                    "Station": self.ctct_station,
                    "Host": 'None',
                    "IP": self.userIP,
                    "Port": self.ctct_port,
                    "State": "OnLine",
                    "Mo": self.mo,
                    "Model": self.model,
                    "Section": self.section,
                    "Group": self.group,
                    "Message": {
                        "Line": self.ctct_line,
                        "DeviceID": self.ctct_deviceid,
                        "State": "Online",
                        "LineData": lineData,
                        "KanbanData": kanbandata,
                    }
                }
                # print('----发送实时数据到CTCT=%s' % StateInfor)
                # logger.info('----发送实时数据到CTCT=%s' % StateInfor)
                receive = self.sendmqtt(self.CheckState_returnCTCT_list[i]['Return'], 'CheckStateResponse',
                                        StateInfor, '', '')
                if self.CheckState_returnCTCT_list[i]['Response'] == 'Once' and receive['result'] == 'OK':
                    del self.CheckState_returnCTCT_list[i]
            except:
                message = traceback.format_exc()
                receive = {'result': 'FAIL',
                           'description': message}
            print(str(receive))
            logger.info(str(receive))
        # return receive

    # 发送换线结果到CTCT
    def ChangeLine_returnCTCT(self, result, resultcode, description, mo, model):
        try:
            clresult = {
                "Result": result,
                "ResultCode": resultcode,
                "Message": {
                    "Line": self.line,
                    "mo": mo,
                    "model": model,
                    "DeviceID": self.ctct_deviceid,
                    "StartTime": self.changeline_starttime,
                    "EndTime": str(datetime.datetime.now())[:19],
                    "Description": description
                }
            }
            print('----发送换线结果到CTCT=%s' % clresult)
            logger.info('----发送换线结果到CTCT=%s' % clresult)
            receive = self.sendmqtt(self.ChangeLineResult_returnCTCT['Return'], 'ChangeLineResponse', clresult, '', '')
        except:
            message = traceback.format_exc()
            receive = {'result': 'FAIL',
                       'description': message}
        print(str(receive))
        logger.info(str(receive))
        self.ChangeLineResult_returnCTCT = {}
        return receive

    # 发送换机种参数结果到CTCT
    def ChangeModelSet_returnCTCT(self, result, resultcode, description):
        try:
            clresult = {
                "Result": result,
                "ResultCode": resultcode,
                "Message": {
                    "Line": self.line,
                    "DeviceID": self.ctct_deviceid,
                    "Description": description
                }
            }
            print('----发送换机种参数结果到CTCT=%s' % clresult)
            logger.info('----发送换机种参数结果到CTCT=%s' % clresult)
            receive = self.sendmqtt(self.ChangeModelSetResult_returnCTCT['Return'], 'ChangeModelSetResponse', clresult,
                                    '', '')
        except:
            message = traceback.format_exc()
            receive = {'result': 'FAIL',
                       'description': message}
        print(str(receive))
        logger.info(str(receive))
        self.ChangeLineResult_returnCTCT = {}
        return receive

    # 发送当站状态变化到CTCT
    def StateChangeFun_toCTCT(self, stateChange):
        for i in range(len(self.CheckState_returnCTCT_list) - 1, -1, -1):
            try:
                StateInfor = {
                    "Result": "Success",
                    "ResultCode": 0,
                    "Factory": self.ctct_factory,
                    "ProductFactory": self.ctct_produce,
                    "Line": self.ctct_line,
                    "Station": self.ctct_station,
                    "Host": 'None',
                    "IP": self.userIP,
                    "Port": self.ctct_port,
                    "State": "OnLine",
                    "Mo": self.mo,
                    "Model": self.model,
                    "Section": self.section,
                    "Group": self.group,
                    "Message": {
                        "Line": self.ctct_line,
                        "DeviceID": self.ctct_deviceid,
                        "State": stateChange
                    }
                }
                print('----发送换线状态到CTCT=%s' % StateInfor)
                logger.info('----发送换线状态到CTCT=%s' % StateInfor)
                receive = self.sendmqtt(self.CheckState_returnCTCT_list[i]['Return'], 'CheckStateResponse',
                                        StateInfor, '', '')
                if self.CheckState_returnCTCT_list[i]['Response'] == 'Once' and receive['result'] == 'OK':
                    del self.CheckState_returnCTCT_list[i]
            except:
                message = traceback.format_exc()
                receive = {'result': 'FAIL',
                           'description': message}
            print(str(receive))
            logger.info(str(receive))

    # mqtt订阅回调函数
    def on_message(self, client, userdata, msg):
        receivve_message = msg.payload.decode()
        receive_topic = msg.topic
        print(f"Received `{receivve_message}` from `{receive_topic}` topic")
        receive = json.loads(receivve_message)
        if receive_topic == self.ctct_linecmd or receive_topic == self.ctct_kanbancmd:
            if receive['Key'] == 'CheckStateRequest':
                find_flag = False
                for i in range(len(receive['Body'])):
                    if receive['Body'][i]['Line'] == self.ctct_line:
                        if len(receive['Body'][i]['DeviceIDList']) == 0:
                            find_flag = True
                            CheckState_returnCTCT_pcs = {
                                "Return": receive['Return'],
                                "Response": receive['Response']
                            }
                            if len(self.CheckState_returnCTCT_list) == 0:
                                self.CheckState_returnCTCT_list.append(CheckState_returnCTCT_pcs)
                            else:
                                for z in range(len(self.CheckState_returnCTCT_list)):
                                    if self.CheckState_returnCTCT_list[z]["Return"] == receive['Return']:
                                        self.CheckState_returnCTCT_list[z]["Response"] = receive['Response']
                                    elif z == len(self.CheckState_returnCTCT_list) - 1:
                                        self.CheckState_returnCTCT_list.append(CheckState_returnCTCT_pcs)
                        else:
                            for j in range(len(receive['Body'][i]['DeviceIDList'])):
                                if receive['Body'][i]['DeviceIDList'][j] == self.ctct_deviceid:
                                    find_flag = True
                                    CheckState_returnCTCT_pcs = {
                                        "Return": receive['Return'],
                                        "Response": receive['Response']
                                    }
                                    if len(self.CheckState_returnCTCT_list) == 0:
                                        self.CheckState_returnCTCT_list.append(CheckState_returnCTCT_pcs)
                                    else:
                                        for z in range(len(self.CheckState_returnCTCT_list)):
                                            if self.CheckState_returnCTCT_list[z]["Return"] == receive['Return']:
                                                self.CheckState_returnCTCT_list[z]["Response"] = receive['Response']
                                            elif z == len(self.CheckState_returnCTCT_list) - 1:
                                                self.CheckState_returnCTCT_list.append(CheckState_returnCTCT_pcs)
                    if find_flag:
                        self.first_upload = True
                        break
            elif receive['Key'] == 'ChangeLineRequest':
                find_flag = False
                # for i in range(len(receive['Body'])):
                if receive['Body']['Line'] == self.ctct_line:
                    if len(receive['Body']['DeviceIDList']) == 0:
                        find_flag = True
                        self.ChangeLineResult_returnCTCT = {
                            "Return": receive['Return'],
                            "Response": receive['Response']
                        }
                    else:
                        for j in range(len(receive['Body']['DeviceIDList'])):
                            if receive['Body']['DeviceIDList'][j] == self.ctct_deviceid:
                                find_flag = True
                                self.ChangeLineResult_returnCTCT = {
                                    "Return": receive['Return'],
                                    "Response": receive['Response']
                                }
                if find_flag:
                    cldata = {
                        'MO_NUMBER': receive['Body']['Mo'],
                        'MODEL_NAME': receive['Body']['Model']
                    }
                    self.changeline_starttime = str(datetime.datetime.now())[:19]
                    threading.Thread(target=self.ChangeLineFun_toDuanCTCT, args=(cldata,), daemon=True).start()
                    # break
            elif receive['Key'] == 'ChangeModelSetRequest':
                find_flag = False
                # for i in range(len(receive['Body'])):
                if receive['Body']['Line'] == self.ctct_line:
                    if len(receive['Body']['DeviceIDList']) == 0:
                        find_flag = True
                        self.ChangeModelSetResult_returnCTCT = {
                            "Return": receive['Return'],
                            "Response": receive['Response']
                        }
                    else:
                        for j in range(len(receive['Body']['DeviceIDList'])):
                            if receive['Body']['DeviceIDList'][j] == self.ctct_deviceid:
                                find_flag = True
                                self.ChangeModelSetResult_returnCTCT = {
                                    "Return": receive['Return'],
                                    "Response": receive['Response']
                                }
                if find_flag:
                    cldata = {}
                    try:
                        cldata['单次产量'] = receive['Body']['单次产量']
                    except:
                        pass
                    try:
                        cldata['单箱数量'] = receive['Body']['单箱数量']
                    except:
                        pass

                    threading.Thread(target=self.ChangeModelSetFun_toDuanCTCT, args=(cldata,), daemon=True).start()

    def workthreading_SubfromCTCT1(self):
        while True:
            while True:
                try:
                    self.init_mqtt()
                except:
                    message = traceback.format_exc()
                    logger.error('mqtt服务器连接失败，30秒后重新连接!%s'%message)
                    print('mqtt服务器连接失败，30秒后重新连接!')
                    # 断开连接
                    # self.client.disconnect()
                    # self.client.loop_stop()
                    # 重连订阅
                    # self.client.reconnect()
                    # self.client.on_message = self.on_message
                    # self.client.loop_start()
                else:
                    logger.info('mqtt服务器连接成功')
                    print('mqtt服务器连接成功')
                    break
            self.ClientState_toCTCT()
            while True:
                try:
                    # 运行阻塞线程来自动处理网络事件
                    self.client.on_message = self.on_message
                    self.client.loop_forever()
                except:
                    message = traceback.format_exc()
                    logger.error('mqtt服务器断开，准备重新连接!%s'%message)
                    break
                else:
                    pass

    # 发送换机种参数到设备
    def ChangeModelSetFun_toDuanCTCT(self, cldata):
        self.StateChangeFun_toCTCT('Changing')
        print('----发送换机种参数指令到端=%s' % cldata)
        logger.info('----发送换机种参数指令到端=%s' % cldata)
        ress = self.autochangemodelset(cldata)
        changeModelSetResult = ress['result']
        description = ress['description']

        if changeModelSetResult == 'OK':
            self.ChangeModelSet_returnCTCT('Success', 0, description)
            print('------模组PI发送换机种参数指令到端成功------')
            logger.info('------模组PI发送换机种参数指令到端成功------')
        else:
            self.ChangeModelSet_returnCTCT('Fail', 2, description)
            print('------模组PI发送换机种参数指令到端失败,返回内容：%s' % ress['description'])
            logger.error('------模组PI发送换机种参数指令到端失败,返回内容：%s' % ress['description'])

    # 发送换线指令到设备
    def ChangeLineFun_toDuanCTCT(self, cldata):
        self.StateChangeFun_toCTCT('Changing')
        print('----发送换线指令到端=%s' % cldata)
        logger.info('----发送换线指令到端=%s' % cldata)
        set_mo = cldata['MO_NUMBER']
        set_model = cldata['MODEL_NAME']
        ress = self.autochangemo(set_mo, set_model)
        changeLineResult = ress['result']
        mo = ress['mo']
        model = ress['model']
        description = ress['description']

        if changeLineResult == 'OK':
            self.ChangeLine_returnCTCT('Success', 0, description, mo, model)
            print('------模组PI发送换线指令到端成功------')
            logger.info('------模组PI发送换线指令到端成功------')
            time.sleep(3)
            python = sys.executable  # 获取当前执行python
            os.execl(python, python, *sys.argv)  # 执行重启程序的命令
        else:
            self.ChangeLine_returnCTCT('Fail', 2, description, 'error', 'error')
            print('------模组PI发送换线指令到端失败,返回内容：%s' % ress['description'])
            logger.error('------模组PI发送换线指令到端失败,返回内容：%s' % ress['description'])

    # MES接口：提供厂区工号线别，获取该用户工单/机种/线别信息
    def worksheet_check(self):
        src = self.secretkey + 'EMP_NO' + self.params_worksheet['EMP_NO'] + 'FACTORY' + self.params_worksheet[
            'FACTORY'] + 'GETDATA_TYPE' + self.params_worksheet['GETDATA_TYPE'] + 'LINE_NAME' + self.params_worksheet[
                  'LINE_NAME'] + 'MO_TYPE' + self.params_worksheet['MO_TYPE']
        md5 = self.keymd5(src)
        self.params_worksheet['sign'] = md5

        print(self.url_worksheet)
        print(self.data_worksheet)
        print(self.headers)
        print(self.params_worksheet)
        logger.info('url_routing=%s\ndata_worksheet=%s\nheaders=%s\nparams_worksheet=%s' % (
            self.url_worksheet, self.data_worksheet, self.headers, self.params_worksheet))
        for i in range(5):
            try:
                receive = requests.request('GET', self.url_worksheet, data=self.data_worksheet, headers=self.headers,
                                           params=self.params_worksheet, timeout=20)
                print(receive)
                try:
                    receive = json.loads(receive.text)
                except:
                    receive = eval(receive.text)
                return receive
            except:
                pass

    def autochangemodelset(self, mode_set_dict):
        try:
            findflag = False
            try:
                set_chanliang = mode_set_dict['单次产量']
            except:
                pass
            else:
                findflag = True
                self.chanliang = str(int(set_chanliang))
                self.setini_lock.acquire()
                self.config.set('mode_config', 'chanliang', self.chanliang)

                self.config.write(open(work_address + '/set.ini', 'w', encoding='utf-8-sig'))
                self.config.write(open(work_address + '/set_backup.ini', 'w', encoding='utf-8-sig'))
                self.setini_lock.release()
                for i in range(len(self.modelset_xls)):
                    if str(self.modelset_xls[i]['机种型号']) == str(self.model):
                        self.modelset_xls[i]['单次产量'] = deepcopy(str(set_chanliang))
                        break

            try:
                set_shuliang = mode_set_dict['单箱数量']
            except:
                pass
            else:
                findflag = True
                for i in range(len(self.modelset_xls)):
                    if str(self.modelset_xls[i]['机种型号']) == str(self.model):
                        self.modelset_xls[i]['单箱数量'] = deepcopy(str(set_shuliang))
                        break

            if findflag:
                fileAddress = work_address + '/model_set.xls'
                pf = pd.DataFrame(self.modelset_xls)
                pf.to_excel(fileAddress, index=False, header=True)
            else:
                returndata = {
                    'result': 'FAIL',
                    'description': '收到需要修改的机种参数无效: %s' % mode_set_dict
                }
                return returndata
        except:
            message = traceback.format_exc()
            print(message)
            logger.error(message)
            returndata = {
                'result': 'FAIL',
                'description': '换机种参数失败: %s' % message
            }
            return returndata
        else:
            returndata = {
                'result': 'OK',
                'description': ''
            }
            return returndata

    # 自动切换工单
    def autochangemo(self, set_mo, set_model):
        try:
            new_mo = set_mo
            if str(new_mo) == '':
                print('换线失败,工单号不能为空!')
                logger.error('换线失败,工单号不能为空!')
                returndata = {
                    'result': 'FAIL',
                    'mo': 'error',
                    'model': 'error',
                    'description': '换线失败,工单号不能为空!'
                }
                return returndata
            # 由工号厂区查询工单信息，寻找该工单的机种名
            self.params_worksheet['FACTORY'] = self.usr_factory
            self.params_worksheet['GETDATA_TYPE'] = '1'
            self.params_worksheet['MO_TYPE'] = '0'
            self.params_worksheet['EMP_NO'] = self.usr_id + str(random.randint(1, 254))
            self.params_worksheet['LINE_NAME'] = self.line
            print('##########################')
            print(self.line + ',等待MES回传工单信息中。。。。。')
            print('##########################')
            try:
                worksheet_post = self.worksheet_check()
                print(worksheet_post)
                worksheet = worksheet_post['Message']
                if len(worksheet) == 0:
                    print(self.line + '查询工单为空!')
                    returndata = {
                        'result': 'FAIL',
                        'mo': 'error',
                        'model': 'error',
                        'description': '换线失败,' + self.line + '查询工单为空!'
                    }
                    return returndata
                else:
                    mo_list = []
                    model_list = []
                    for i in worksheet:
                        mo_list.append(i['MO_NUMBER'])
                        model_list.append(i['MODEL_NAME'])
                    try:
                        index = mo_list.index(str(new_mo))
                    except:
                        message = traceback.format_exc()
                        print(message)
                        logger.error(message)
                        print(self.line + '排程中查询不到' + str(new_mo) + '工单!')
                        returndata = {
                            'result': 'FAIL',
                            'mo': 'error',
                            'model': 'error',
                            'description': self.line + '排程中查询不到' + str(new_mo) + '工单!'
                        }
                        return returndata
                    else:
                        new_model = model_list[index]
                        if new_model == '':
                            print(self.line + '排程中查询到' + str(new_mo) + '该工单的机种为空!')
                            returndata = {
                                'result': 'FAIL',
                                'mo': 'error',
                                'model': 'error',
                                'description': self.line + '排程中查询到' + str(new_mo) + '该工单的机种为空!'
                            }
                            return returndata
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
                returndata = {
                    'result': 'FAIL',
                    'mo': 'error',
                    'model': 'error',
                    'description': '换线失败,' + self.line + '查询工单失败!'
                }
                return returndata

            for i in range(len(self.modelset_xls)):
                if str(self.modelset_xls[i]['机种型号']) == str(new_model):
                    self.mo = str(new_mo)
                    self.model = str(new_model)
                    if str(self.modelset_xls[i]['投入类型']) == '首站投入':
                        self.feishouzhan = '0'
                    else:
                        self.feishouzhan = '1'
                    if str(self.modelset_xls[i]['条码模式']) == '无条码模式':
                        self.youtiaoma = '0'
                    else:
                        self.youtiaoma = '1'
                    self.chanliang = str(int(float(self.modelset_xls[i]['单次产量'])))
                    self.snlength = str(self.modelset_xls[i]['条码长度'])
                    if self.snlength == '' or self.snlength == 'nan':
                        self.snlength = '0'
                    self.snguding = str(self.modelset_xls[i]['条码固定码'])
                    break
                elif i == len(self.modelset_xls) - 1:
                    for j in range(len(self.modelset_xls) - 1, -1, -1):
                        if str(self.modelset_xls[j]['机种型号']) == '其他':
                            self.mo = str(new_mo)
                            self.model = new_model
                            if str(self.modelset_xls[j]['投入类型']) == '首站投入':
                                self.feishouzhan = '0'
                            else:
                                self.feishouzhan = '1'
                            if str(self.modelset_xls[j]['条码模式']) == '无条码模式':
                                self.youtiaoma = '0'
                            else:
                                self.youtiaoma = '1'
                            self.chanliang = str(int(float(self.modelset_xls[j]['单次产量'])))
                            self.snlength = str(self.modelset_xls[j]['条码长度'])
                            if self.snlength == '' or self.snlength == 'nan':
                                self.snlength = '0'
                            self.snguding = str(self.modelset_xls[j]['条码固定码'])
                            if self.snguding == '' or self.snguding == 'nan':
                                self.snguding = ''
                            break
                        elif j == 0:
                            print('机种文件内查询到' + new_model + '该机种!')
                            returndata = {
                                'result': 'FAIL',
                                'mo': 'error',
                                'model': 'error',
                                'description': '机种文件内查询到' + new_model + '该机种!'
                            }
                            return returndata
            self.setini_lock.acquire()
            self.config.set('mes_config', 'mo', self.mo)
            self.config.set('mes_config', 'model', self.model)
            self.config.set('mes_config', 'feishouzhan', self.feishouzhan)
            self.config.set('mes_config', 'youtiaoma', self.youtiaoma)
            self.config.set('mode_config', 'chanliang', self.chanliang)
            self.config.set('mes_config', 'snlength', self.snlength)
            self.config.set('mes_config', 'snguding', self.snguding)

            self.config.write(open(work_address + '/set.ini', 'w', encoding='utf-8-sig'))
            self.config.write(open(work_address + '/set_backup.ini', 'w', encoding='utf-8-sig'))
            self.setini_lock.release()
        except:
            message = traceback.format_exc()
            print(message)
            logger.error(message)
            returndata = {
                'result': 'FAIL',
                'mo': 'error',
                'model': 'error',
                'description': '换线失败'
            }
            return returndata
        else:
            returndata = {
                'result': 'OK',
                'mo': self.mo,
                'model': self.model,
                'description': ''
            }
            return returndata

    # 数据库读取函数, 例: 传入dict_data = {'Name': '焊油流量值'}, 查询集合中所有带有{'Name': '焊油流量值'}的数据并返回为列表
    def database_read(self, dict_data, db_collection_name):
        # return_data = []
        client = MongoClient(self.db_ip, self.db_port)
        db = client[self.db_database_name]
        collection = db[db_collection_name]
        # result = collection.find(dict_data)
        result = collection.find_one(dict_data, sort=[('_id', -1)])
        # for r in result:
        #     return_data.append(r)
        client.close()
        return result

    # 数据库写入函数, 写入一个josn格式的数据
    def database_write(self, dict_data, db_collection_name):
        client = MongoClient(self.db_ip, self.db_port)
        db = client[self.db_database_name]
        collection = db[db_collection_name]
        collection.insert_one(dict_data)
        client.close()

    # 数据库更新函数,更新包含dict_data的数据
    def database_update(self, dict_data, db_collection_name, dict_data_new):
        client = MongoClient(self.db_ip, self.db_port)
        db = client[self.db_database_name]
        collection = db[db_collection_name]
        collection.update_one(dict_data, dict_data_new)
        client.close()

    # 轮询查看当前设备状态，转换数据，并发送给DB
    def workthreading_uptodb(self):
        # old_status = ['' for i in range(len(self.shebei_bianma))]
        # old_passQty = [0 for i in range(len(self.shebei_bianma))]

        starttime = time.time()
        while True:
            try:
                for i in range(len(self.shebei_bianma)):
                    # 检查出用于设备的端口
                    if i in self.gpio_in_com_new:
                        signal_number = i
                        interfaceID = self.shebei_bianma[signal_number]

                        # 查看当前设备灯号状态
                        if self.light_status == 'yellow':
                            status = '待料中'
                            self.status_time_list[self.gpio_yellow-1] += int(time.time() - starttime)
                        elif self.light_status == 'red':
                            status = '故障'
                            self.status_time_list[self.gpio_red-1] += int(time.time() - starttime)
                        else:
                            status = '正常生产中'
                            self.status_time_list[self.gpio_green-1] += int(time.time() - starttime)

                        # 重置查看灯号的时间
                        starttime = time.time()

                        # 统计当前状态时间占比，并上传DB
                        now_time = datetime.datetime.now()
                        now_year = int(str(now_time)[:4])
                        now_month = int(str(now_time)[5:7])
                        now_day = int(str(now_time)[8:10])
                        for j in range(len(self.today_restart_time)):
                            restart_hour = int(self.today_restart_time[j][:2])
                            restart_min = int(self.today_restart_time[j][2:4])
                            if now_time > datetime.datetime(now_year, now_month, now_day, restart_hour, restart_min):
                                alltime = self.status_time_list[self.gpio_green-1]+self.status_time_list[self.gpio_red-1]+self.status_time_list[self.gpio_yellow-1]
                                if alltime != 0:
                                    green = int((self.status_time_list[self.gpio_green-1] / alltime)*100 + 0.5)
                                    red = int((self.status_time_list[self.gpio_red-1] / alltime)*100 + 0.5)
                                    yellow = int((self.status_time_list[self.gpio_yellow-1] / alltime)*100 + 0.5)
                                else:
                                    green = 0
                                    red = 0
                                    yellow = 0
                                percentdata = {
                                    'time': str(datetime.datetime.now())[:10],
                                    'Device': self.station,
                                    'green_' + str(self.today_restart_time[j]): green,
                                    'red_' + str(self.today_restart_time[j]): red,
                                    'yellow_' + str(self.today_restart_time[j]): yellow,
                                    'green': green,
                                    'red': red,
                                    'yellow': yellow,
                                    'interfaceID': interfaceID
                                }
                                checkdata = {'interfaceID': interfaceID, 'time': str(datetime.datetime.now())[:10]}
                                getresult = self.database_read(checkdata, 'percent_bar')
                                if getresult is not None:
                                    if getresult != percentdata:
                                        self.database_update(checkdata, 'percent_bar',
                                                             {"$set": percentdata})
                                        logger.info('DB已更新到%s表单：%s' % ('percent_bar', percentdata))
                                    else:
                                        logger.info('DB无需更新到%s表单：%s' % ('percent_bar', percentdata))
                                else:
                                    percentdata = {
                                        'time': str(datetime.datetime.now())[:10],
                                        'Device': self.station,
                                        'green': green,
                                        'red': red,
                                        'yellow': yellow,
                                        'interfaceID': interfaceID
                                    }
                                    for item in self.today_restart_time:
                                        percentdata['green_' + str(item)] = 0
                                        percentdata['red_' + str(item)] = 0
                                        percentdata['yellow_' + str(item)] = 0
                                    percentdata['green_' + str(self.today_restart_time[j])] = green
                                    percentdata['red_' + str(self.today_restart_time[j])] = red
                                    percentdata['yellow_' + str(self.today_restart_time[j])] = yellow
                                    self.database_write(percentdata, 'percent_bar')
                                    logger.info('DB已写入到%s表单：%s' % ('percent_bar', percentdata))
                                break
                            elif j == len(self.today_restart_time) - 1:
                                yesterday = str(datetime.datetime.now() - datetime.timedelta(days=1))[:10]
                                alltime = self.status_time_list[self.gpio_green-1] + self.status_time_list[
                                    self.gpio_red] + self.status_time_list[self.gpio_yellow-1]
                                green = int((self.status_time_list[self.gpio_green-1] / alltime)*100 + 0.5)
                                red = int((self.status_time_list[self.gpio_red-1] / alltime)*100 + 0.5)
                                yellow = int((self.status_time_list[self.gpio_yellow-1] / alltime)*100 + 0.5)
                                percentdata = {
                                    'time': yesterday,
                                    'Device': self.station,
                                    'green_' + str(self.today_restart_time[j]): green,
                                    'red_' + str(self.today_restart_time[j]): red,
                                    'yellow_' + str(self.today_restart_time[j]): yellow,
                                    'green': green,
                                    'red': red,
                                    'yellow': yellow,
                                    'interfaceID': interfaceID
                                }
                                checkdata = {'interfaceID': interfaceID, 'time': yesterday}
                                getresult = self.database_read(checkdata, 'percent_bar')
                                if getresult is not None:
                                    if getresult != percentdata:
                                        self.database_update(checkdata, 'percent_bar',
                                                             {"$set": percentdata})
                                        logger.info('DB已更新到%s表单：%s' % ('percent_bar', percentdata))
                                    else:
                                        logger.info('DB无需更新到%s表单：%s' % ('percent_bar', percentdata))
                                else:
                                    percentdata = {
                                        'time': yesterday,
                                        'Device': self.station,
                                        'green': green,
                                        'red': red,
                                        'yellow': yellow,
                                        'interfaceID': interfaceID
                                    }
                                    for item in self.today_restart_time:
                                        percentdata['green_' + str(item)] = 0
                                        percentdata['red_' + str(item)] = 0
                                        percentdata['yellow_' + str(item)] = 0
                                    percentdata['green_' + str(self.today_restart_time[j])] = green
                                    percentdata['red_' + str(self.today_restart_time[j])] = red
                                    percentdata['yellow_' + str(self.today_restart_time[j])] = yellow
                                    self.database_write(percentdata, 'percent_bar')
                                    logger.info('DB已写入到%s表单：%s' % ('percent_bar', percentdata))

                        # 整理设备产品数量信息，并上传DB
                        passQty = self.pqm_allnumber_list[signal_number]
                        # runningTime = round(time.time() - self.pqm_starttime, 1)

                        # if old_passQty[signal_number] != passQty or old_status[signal_number] != status:
                        now_time = datetime.datetime.now()
                        now_year = int(str(now_time)[:4])
                        now_month = int(str(now_time)[5:7])
                        now_day = int(str(now_time)[8:10])
                        for j in range(len(self.today_restart_time)):
                            restart_hour = int(self.today_restart_time[j][:2])
                            restart_min = int(self.today_restart_time[j][2:4])
                            if now_time > datetime.datetime(now_year, now_month, now_day, restart_hour, restart_min):
                                machine_statusdata = {
                                    'time': str(datetime.datetime.now())[:10],
                                    'status': status,
                                    'line': self.line,
                                    'station': self.station,
                                    'interfaceID': interfaceID,
                                    'count_' + str(self.today_restart_time[j]): passQty,
                                    'eqpType': self.eqpType
                                }
                                checkdata = {'interfaceID': interfaceID, 'time': str(datetime.datetime.now())[:10]}
                                getresult = self.database_read(checkdata, 'machine_statu')
                                if getresult is not None:
                                    if getresult != machine_statusdata:
                                        self.database_update(checkdata, 'machine_statu',
                                                             {"$set": machine_statusdata})
                                        logger.info('DB已更新到%s表单：%s' % ('machine_statu', machine_statusdata))
                                    else:
                                        logger.info('DB无需更新到%s表单：%s' % ('machine_statu', machine_statusdata))
                                else:
                                    machine_statusdata = {
                                        'time': str(datetime.datetime.now())[:10],
                                        'status': status,
                                        'line': self.line,
                                        'station': self.station,
                                        'interfaceID': interfaceID,
                                        'eqpType': self.eqpType
                                    }
                                    for item in self.today_restart_time:
                                        machine_statusdata['count_' + str(item)] = 0
                                    machine_statusdata['count_' + str(self.today_restart_time[j])] = passQty
                                    self.database_write(machine_statusdata, 'machine_statu')
                                    logger.info('DB已写入到%s表单：%s' % ('machine_statu', machine_statusdata))
                                break
                            elif j == len(self.today_restart_time) - 1:
                                yesterday = str(datetime.datetime.now()-datetime.timedelta(days=1))[:10]

                                machine_statusdata = {
                                    'time': yesterday,
                                    'status': status,
                                    'line': self.line,
                                    'station': self.station,
                                    'interfaceID': interfaceID,
                                    'count_' + str(self.today_restart_time[j]): passQty
                                }
                                checkdata = {'interfaceID': interfaceID, 'time': yesterday}
                                getresult = self.database_read(checkdata, 'machine_statu')
                                if getresult is not None:
                                    if getresult != machine_statusdata:
                                        self.database_update(checkdata, 'machine_statu',
                                                             {"$set": machine_statusdata})
                                        logger.info('DB已更新到%s表单：%s' % ('machine_statu', machine_statusdata))
                                    else:
                                        logger.info('DB无需更新到%s表单：%s' % ('machine_statu', machine_statusdata))
                                else:
                                    machine_statusdata = {
                                        'time': yesterday,
                                        'status': status,
                                        'line': self.line,
                                        'station': self.station,
                                        'interfaceID': interfaceID
                                    }
                                    for item in self.today_restart_time:
                                        machine_statusdata['count_' + str(item)] = 0
                                    machine_statusdata['count_' + str(self.today_restart_time[j])] = passQty
                                    self.database_write(machine_statusdata, 'machine_statu')
                                    logger.info('DB已写入到%s表单：%s' % ('machine_statu', machine_statusdata))

                            # old_passQty[signal_number] = deepcopy(passQty)

                        # 将当前状态上传DB
                        # if old_status[signal_number] != status:
                        nowtime = str(datetime.datetime.utcnow())
                        nowtime_str = nowtime[:10] + 'T' + nowtime[11:19] + 'Z'
                        statusdata = {
                            'time': nowtime_str,
                            'status': status,
                            'line': self.line,
                            'station': self.station,
                            'interfaceID': interfaceID
                        }
                        checkdata = {'interfaceID': interfaceID}

                        # try:
                        getresult = self.database_read(checkdata, 'line_statu')
                        if getresult is not None:
                            if getresult['status'] != status:
                                self.database_write(statusdata, 'line_statu')
                                logger.info('DB已写入到%s表单：%s' % ('line_statu', statusdata))
                            else:
                                logger.info('DB已存在相同状态无需写入到%s表单：%s' % ('line_statu', statusdata))
                        else:
                            self.database_write(statusdata, 'line_statu')
                            logger.info('DB已写入到%s表单：%s' % ('line_statu', statusdata))



                        # except:
                        #     message = traceback.format_exc()
                        #     print('DB查询line_statu时报错:'+message)
                        #     logger.error('DB查询line_statu时报错:'+message)
                        #     self.database_write(statusdata, 'line_statu')
                        #     logger.info('DB已写入到%s表单：%s' % ('line_statu', statusdata))

                            # old_status[signal_number] = deepcopy(status)

                        # 保存set.ini
                        self.setini_lock.acquire()
                        self.config.set('light_config', 'status_time_list', str(self.status_time_list)[1:-1])
                        self.config.write(open(work_address + '/set.ini', 'w', encoding='utf-8-sig'))
                        self.config.write(open(work_address + '/set_backup.ini', 'w', encoding='utf-8-sig'))
                        self.setini_lock.release()
                        logger.info('统计当前状态时间占比保存数据到本地set.ini完成')
            except:
                message = traceback.format_exc()
                print(message)
                logger.error(message)
            else:
                time.sleep(5)

# 主程序开启
def start():
    while True:
        try:
            window = tk.Tk()
            loginclass = LoginWindow(window)
            loginclass.run()
            window.mainloop()
        except:
            message = traceback.format_exc()
            print(message)
            logger.error(message)
            print('主程序报错，重新启动主程序!')
            logger.error('主程序报错，重新启动主程序!')
            # 检查是否开启无人模式，如果是的话则自动重启
            time.sleep(30)
            config = configparser.ConfigParser()
            try:
                config.read(work_address + '/set.ini', encoding='utf-8-sig')
            except:
                config.read(work_address + '/set_backup.ini', encoding='utf-8-sig')
            auto_open = config.get('mode_config', 'auto_open')
            if auto_open == '1':
                os.system('reboot')
        else:
            break
