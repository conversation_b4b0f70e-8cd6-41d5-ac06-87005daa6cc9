#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試消息類型支持功能
驗證EAP和SIE配置管理器對UnitsDeparted和WorkCompleted消息的支持
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from kafka_config_manager import kafka_config_manager
from rabbitmq_config_manager import RabbitMQConfigManager

def test_kafka_message_options():
    """測試Kafka消息選項"""
    print("=== 測試Kafka消息選項 ===")
    
    # 測試獲取消息選項
    options = kafka_config_manager.get_message_options()
    print(f"可用的Kafka消息選項: {len(options)}個")
    
    for option in options:
        print(f"  - {option['value']}: {option['label']}")
        print(f"    描述: {option['description']}")
    
    # 測試獲取消息標籤
    for message_name in ['CFX.Production.UnitsDeparted', 'CFX.Production.WorkCompleted']:
        label = kafka_config_manager.get_message_type_label(message_name)
        print(f"消息 '{message_name}' 的標籤: {label}")
    
    print()

def test_rabbitmq_routing_options():
    """測試RabbitMQ路由選項"""
    print("=== 測試RabbitMQ路由選項 ===")
    
    rabbitmq_manager = RabbitMQConfigManager()
    
    # 測試獲取路由選項
    options = rabbitmq_manager.get_routing_options()
    print(f"可用的RabbitMQ路由選項: {len(options)}個")
    
    for option in options:
        print(f"  - {option['value']}")
        print(f"    標籤: {option['label']}")
        print(f"    描述: {option['description']}")
    
    # 測試從模式中提取消息類型
    patterns = [
        'edadata.cfx.#.{device_id}.CFX.Production.UnitsDeparted',
        'edadata.cfx.#.{device_id}.CFX.Production.WorkCompleted'
    ]
    
    for pattern in patterns:
        message_type = rabbitmq_manager.get_message_type_from_pattern(pattern)
        print(f"模式 '{pattern}' 的消息類型: {message_type}")
    
    print()

def test_config_updates():
    """測試配置更新"""
    print("=== 測試配置更新 ===")
    
    # 測試Kafka配置更新
    print("測試Kafka配置更新...")
    original_config = kafka_config_manager.get_config()
    print(f"原始消息名稱: {original_config['message_name']}")
    
    # 更新為WorkCompleted
    new_config = original_config.copy()
    new_config['message_name'] = 'CFX.Production.WorkCompleted'
    
    if kafka_config_manager.update_config(new_config):
        print("✅ Kafka配置更新成功")
        updated_config = kafka_config_manager.get_config()
        print(f"更新後消息名稱: {updated_config['message_name']}")
        
        # 恢復原始配置
        kafka_config_manager.update_config(original_config)
        print("✅ Kafka配置已恢復")
    else:
        print("❌ Kafka配置更新失敗")
    
    # 測試RabbitMQ配置更新
    print("\n測試RabbitMQ配置更新...")
    rabbitmq_manager = RabbitMQConfigManager()
    original_config = rabbitmq_manager.get_config()
    print(f"原始路由模式: {original_config['routing_key_pattern']}")
    
    # 更新為WorkCompleted
    new_config = original_config.copy()
    new_config['routing_key_pattern'] = 'edadata.cfx.#.{device_id}.CFX.Production.WorkCompleted'
    
    if rabbitmq_manager.update_config(new_config):
        print("✅ RabbitMQ配置更新成功")
        updated_config = rabbitmq_manager.get_config()
        print(f"更新後路由模式: {updated_config['routing_key_pattern']}")
        
        # 恢復原始配置
        rabbitmq_manager.update_config(original_config)
        print("✅ RabbitMQ配置已恢復")
    else:
        print("❌ RabbitMQ配置更新失敗")
    
    print()

def test_topic_generation():
    """測試主題生成"""
    print("=== 測試主題生成 ===")
    
    # 測試不同消息類型的主題生成
    test_lines = ['A1', 'A2', 'B1']
    
    for message_name in ['CFX.Production.UnitsDeparted', 'CFX.Production.WorkCompleted']:
        # 臨時更新配置
        config = kafka_config_manager.get_config()
        original_message_name = config['message_name']
        config['message_name'] = message_name
        kafka_config_manager.update_config(config)
        
        # 生成主題
        topics = kafka_config_manager.generate_topic_patterns(test_lines)
        print(f"消息類型 '{message_name}' 的主題:")
        for topic in topics:
            print(f"  - {topic}")
        
        # 恢復原始配置
        config['message_name'] = original_message_name
        kafka_config_manager.update_config(config)
    
    print()

if __name__ == "__main__":
    print("開始測試消息類型支持功能...\n")
    
    try:
        test_kafka_message_options()
        test_rabbitmq_routing_options()
        test_config_updates()
        test_topic_generation()
        
        print("✅ 所有測試完成！")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
