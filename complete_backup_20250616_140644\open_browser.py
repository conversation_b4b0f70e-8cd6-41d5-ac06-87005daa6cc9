#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自動打開瀏覽器腳本
"""

import webbrowser
import time
import sys
import subprocess
import os

def wait_for_server(url="http://localhost:5000", timeout=30):
    """等待服務器啟動"""
    import urllib.request
    import urllib.error
    
    print("Waiting for server to start...")
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            urllib.request.urlopen(url, timeout=2)
            print("Server is ready!")
            return True
        except (urllib.error.URLError, ConnectionError):
            time.sleep(1)
            print(".", end="", flush=True)
    
    print(f"\nTimeout waiting for server at {url}")
    return False

def open_browser_windows(url="http://localhost:5000"):
    """在Windows上打開瀏覽器"""
    browsers_to_try = [
        # Microsoft Edge (most common on Windows 10/11)
        r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
        r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
        # Google Chrome
        r"C:\Program Files\Google\Chrome\Application\chrome.exe", 
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        # Internet Explorer (fallback)
        r"C:\Program Files\Internet Explorer\iexplore.exe",
    ]
    
    # Try default browser first
    try:
        webbrowser.open(url)
        print(f"Opened {url} with default browser")
        return True
    except Exception as e:
        print(f"Default browser failed: {e}")
    
    # Try specific browsers
    for browser_path in browsers_to_try:
        if os.path.exists(browser_path):
            try:
                subprocess.Popen([browser_path, url])
                print(f"Opened {url} with {browser_path}")
                return True
            except Exception as e:
                print(f"Failed to open {browser_path}: {e}")
                continue
    
    # Last resort: use Windows start command
    try:
        os.system(f'start {url}')
        print(f"Opened {url} with Windows start command")
        return True
    except Exception as e:
        print(f"Windows start command failed: {e}")
    
    return False

def main():
    url = "http://localhost:5000"
    
    print("MES Data Forwarding System - Browser Launcher")
    print("=" * 50)
    
    # Wait for server to be ready
    if wait_for_server(url):
        print(f"\nOpening browser to {url}...")
        if open_browser_windows(url):
            print("Browser opened successfully!")
        else:
            print(f"Failed to open browser automatically.")
            print(f"Please manually open your browser and visit: {url}")
    else:
        print(f"Server not responding at {url}")
        print("Please check if the MES system is running properly.")

if __name__ == "__main__":
    main()
