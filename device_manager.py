import json
import os
from datetime import datetime

class DeviceManager:
    def __init__(self, data_file='devices.json'):
        self.data_file = data_file
        self.devices = {}
        self.mes_api = None  # 延遲初始化MES API，避免循環導入
        self.devices = self._load_devices()

        # 初始化完成後檢查所有設備的工單狀態
        self._check_all_devices_work_order_status()

    def _get_mes_api(self):
        """延遲初始化MES API，避免循環導入"""
        if self.mes_api is None:
            from mes_api import MESApi
            self.mes_api = MESApi(self)
        return self.mes_api



    def get_device_running_time(self, device_id):
        """獲取設備運行時間（秒）"""
        if device_id not in self.devices:
            return 0.0

        device = self.devices[device_id]
        mes_start_time = device.get('mesStartTime')

        if not mes_start_time:
            return 0.0

        try:
            start_time = datetime.fromisoformat(mes_start_time)
            current_time = datetime.now()
            running_seconds = (current_time - start_time).total_seconds()
            return max(0.0, running_seconds)
        except Exception as e:
            print(f"⚠️ 計算設備 {device_id} 運行時間失敗: {e}")
            return 0.0

    def get_and_update_cycle_time(self, device_id):
        """獲取並更新產品生產週期時間（秒）"""
        if device_id not in self.devices:
            return 0.0

        device = self.devices[device_id]
        current_time = datetime.now()
        last_product_time = device.get('lastProductTime')

        if not last_product_time:
            # 首次產品，cycleTime為0
            cycle_time = 0.0
        else:
            try:
                last_time = datetime.fromisoformat(last_product_time)
                cycle_time = (current_time - last_time).total_seconds()
                cycle_time = max(0.0, cycle_time)
            except Exception as e:
                print(f"⚠️ 計算設備 {device_id} 週期時間失敗: {e}")
                cycle_time = 0.0

        # 更新本次產品時間
        device['lastProductTime'] = current_time.isoformat()
        self._save_devices()

        return cycle_time
    
    def _load_devices(self):
        """從文件加載設備數據"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    devices = json.load(f)
                    # 為舊設備添加工單隊列字段和時間追蹤字段
                    current_time = datetime.now().isoformat()
                    for device_id, device in devices.items():
                        if 'workOrders' not in device:
                            device['workOrders'] = []
                        if 'currentWorkOrder' not in device:
                            device['currentWorkOrder'] = None
                        if 'currentWorkOrderProgress' not in device:
                            device['currentWorkOrderProgress'] = 0
                        if 'lastCompletedWorkOrder' not in device:
                            device['lastCompletedWorkOrder'] = None
                        if 'mesErrors' not in device:
                            device['mesErrors'] = []
                        if 'recentWorkOrders' not in device:
                            device['recentWorkOrders'] = []

                        # 添加時間追蹤字段
                        if 'mesStartTime' not in device:
                            device['mesStartTime'] = None
                        if 'lastProductTime' not in device:
                            device['lastProductTime'] = None

                        # 如果設備已啟動MES轉發，則重新初始化時間起始點（每次程序啟動都重新計算）
                        if device.get('isMonitoring', False):
                            device['mesStartTime'] = current_time
                            device['lastProductTime'] = None  # 重置產品時間，重新開始cycleTime計算
                            print(f"🚀 設備 {device_id}: 系統啟動時檢測到MES轉發已啟動，重新初始化時間起始點")

                    # 先返回設備數據，稍後再檢查工單狀態
                    return devices
            except Exception as e:
                print(f"加載設備數據失敗: {e}")
                return {}
        return {}

    def _check_all_devices_work_order_status(self):
        """初始化完成後檢查所有設備的工單狀態"""
        for device_id, device in self.devices.items():
            try:
                self._check_and_auto_stop_mes(device_id, device)
            except Exception as e:
                print(f"檢查設備 {device_id} 工單狀態時出錯: {e}")

    def _save_devices(self):
        """保存設備數據到文件"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.devices, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存設備數據失敗: {e}")

    def save_devices(self):
        """公共方法：保存設備數據到文件"""
        return self._save_devices()
    
    def add_device(self, device_id, line_name, section_name, group_name, station_name):
        """添加新設備"""
        if device_id in self.devices:
            raise ValueError(f"設備ID {device_id} 已存在")

        device_info = {
            'deviceId': device_id,
            'lineName': line_name,
            'sectionName': section_name,
            'groupName': group_name,
            'stationName': station_name,
            'createdAt': datetime.now().isoformat(),
            'updatedAt': datetime.now().isoformat(),
            'isActive': True,
            'isMonitoring': False,
            'productCount': 0,
            'forwardedCount': 0,
            'unforwardedCount': 0,
            'lastLog': '',
            'lastUpdateTime': '',
            'recentLogs': [],  # 存儲最近的生產數據
            'lastFrontendUpdate': datetime.now().isoformat(),  # 記錄上次前端更新時間
            'workOrders': [],  # 工單隊列
            'currentWorkOrder': None,  # 當前工單
            'currentWorkOrderProgress': 0,  # 當前工單進度
            'mesErrors': [],  # MES錯誤信息列表
            'mesStartTime': None,  # MES轉發開始時間（用於計算runningTime）
            'lastProductTime': None  # 上一個產品生產時間（用於計算cycleTime）
        }

        self.devices[device_id] = device_info
        self._save_devices()

        return device_id
    
    def update_device(self, device_id, update_data):
        """更新設備信息，支持設備ID修改"""
        if device_id not in self.devices:
            raise ValueError(f"設備ID {device_id} 不存在")

        # 檢查是否要修改設備ID
        new_device_id = update_data.get('deviceId')
        if new_device_id and new_device_id != device_id:
            # 檢查新設備ID是否已存在
            if new_device_id in self.devices:
                raise ValueError(f"設備ID {new_device_id} 已存在")

            # 複製設備數據到新ID
            device_data = self.devices[device_id].copy()
            device_data['deviceId'] = new_device_id

            # 刪除舊設備ID的數據
            del self.devices[device_id]

            # 使用新設備ID
            device_id = new_device_id
            self.devices[device_id] = device_data

            print(f"✅ 設備ID已更新: {update_data.get('deviceId')} → {new_device_id}")

        # 更新允許的字段
        allowed_fields = ['deviceId', 'lineName', 'sectionName', 'groupName', 'stationName', 'workOrder', 'model', 'isActive', 'isMonitoring']
        for field in allowed_fields:
            if field in update_data:
                self.devices[device_id][field] = update_data[field]

        self.devices[device_id]['updatedAt'] = datetime.now().isoformat()
        self._save_devices()
    
    def delete_device(self, device_id):
        """刪除設備"""
        if device_id not in self.devices:
            raise ValueError(f"設備ID {device_id} 不存在")
        
        del self.devices[device_id]
        self._save_devices()
    
    def get_device(self, device_id):
        """獲取單個設備信息"""
        return self.devices.get(device_id)
    
    def get_all_devices(self):
        """獲取所有設備"""
        return self.devices
    
    def get_active_devices(self):
        """獲取所有活躍設備"""
        return {k: v for k, v in self.devices.items() if v.get('isActive', True)}
    
    def is_device_monitored(self, device_id):
        """檢查設備是否需要監控"""
        device = self.get_device(device_id)
        return device is not None and device.get('isActive', True) and device.get('isMonitoring', False)

    def update_device_production(self, device_id, unit_count, log_message, is_forwarded=True):
        """更新設備產量和日誌（考慮模穴數）"""
        if device_id not in self.devices:
            print(f"❌ 設備 {device_id} 不存在於設備列表中")
            return False

        device = self.devices[device_id]

        # 獲取當前工單的模穴數
        current_work_order = self.get_current_work_order(device_id)
        cavity_count = current_work_order.get('cavityCount', 1) if current_work_order else 1

        # 計算實際產品數量 = 原始數量 × 模穴數
        actual_product_count = unit_count * cavity_count

        old_product_count = device.get('productCount', 0)
        old_forwarded_count = device.get('forwardedCount', 0)
        old_unforwarded_count = device.get('unforwardedCount', 0)

        device['productCount'] = old_product_count + actual_product_count

        # 分別統計已轉發和未轉發的數量（都需要乘以模穴數）
        if is_forwarded:
            device['forwardedCount'] = old_forwarded_count + actual_product_count
        else:
            device['unforwardedCount'] = old_unforwarded_count + actual_product_count

        # 更新最新日誌
        device['lastLog'] = log_message
        current_time = datetime.now().isoformat()
        device['lastUpdateTime'] = current_time
        device['updatedAt'] = current_time

        # 添加到最近日誌列表
        if 'recentLogs' not in device:
            device['recentLogs'] = []

        device['recentLogs'].append({
            'message': log_message,
            'timestamp': current_time,
            'unitCount': unit_count,
            'actualProductCount': actual_product_count,
            'cavityCount': cavity_count,
            'isForwarded': is_forwarded
        })

        # 只保留最近50條記錄，避免數據過多
        if len(device['recentLogs']) > 50:
            device['recentLogs'] = device['recentLogs'][-50:]

        print(f"📊 統計更新: 設備={device_id}")
        print(f"   模穴數: {cavity_count}")
        print(f"   原始數量: {unit_count} → 實際產品數: {actual_product_count}")
        print(f"   總產量: {old_product_count} → {device['productCount']} (+{actual_product_count})")
        print(f"   已轉發: {old_forwarded_count} → {device.get('forwardedCount', 0)}")
        print(f"   未轉發: {old_unforwarded_count} → {device.get('unforwardedCount', 0)}")
        print(f"   最新日誌: {log_message}")

        # 如果是轉發成功的產品，更新工單進度（使用實際產品數量）
        if is_forwarded and actual_product_count > 0:
            self.update_work_order_progress(device_id, actual_product_count)

        self._save_devices()
        return True

    def toggle_device_monitoring(self, device_id, is_monitoring):
        """切換設備監控狀態"""
        if device_id not in self.devices:
            raise ValueError(f"設備ID {device_id} 不存在")

        device = self.devices[device_id]
        device['isMonitoring'] = is_monitoring
        device['updatedAt'] = datetime.now().isoformat()

        if is_monitoring:
            # 啟動MES轉發時，記錄開始時間和重置產品時間
            device['mesStartTime'] = datetime.now().isoformat()
            device['lastProductTime'] = None  # 重置產品時間，首次產品cycleTime為0
            print(f"🚀 設備 {device_id}: MES轉發已啟動，記錄開始時間")
        else:
            # 停止MES轉發時，清除時間記錄
            device['mesStartTime'] = None
            device['lastProductTime'] = None
            print(f"🛑 設備 {device_id}: MES轉發已停止，清除時間記錄")

        self._save_devices()
        return True

    def get_monitoring_devices(self):
        """獲取所有正在監控的設備"""
        return {k: v for k, v in self.devices.items() if v.get('isActive', True) and v.get('isMonitoring', False)}

    def get_recent_production_data(self, device_id, since_time=None):
        """獲取設備在指定時間後的生產數據"""
        if device_id not in self.devices:
            return []

        device = self.devices[device_id]
        recent_logs = device.get('recentLogs', [])

        if since_time is None:
            return recent_logs

        # 過濾出指定時間後的數據
        filtered_logs = []
        for log in recent_logs:
            if log['timestamp'] > since_time:
                filtered_logs.append(log)

        return filtered_logs

    def update_frontend_timestamp(self, device_id):
        """更新設備的前端更新時間戳"""
        if device_id in self.devices:
            self.devices[device_id]['lastFrontendUpdate'] = datetime.now().isoformat()
            self._save_devices()
            return True
        return False

    # 工單管理方法
    def add_work_order(self, device_id, work_order_number, target_quantity, model_name="", description="", cavity_count=1, station_type="無條碼過站"):
        """為設備添加工單"""
        if device_id not in self.devices:
            raise ValueError(f"設備ID {device_id} 不存在")

        device = self.devices[device_id]
        if 'workOrders' not in device:
            device['workOrders'] = []

        work_order = {
            'workOrderNumber': work_order_number,
            'targetQuantity': target_quantity,
            'modelName': model_name,
            'description': description,
            'cavityCount': cavity_count,  # 模穴數
            'stationType': station_type,  # 過站類型：無條碼過站、有條碼過站、有條碼投入
            'createdAt': datetime.now().isoformat(),
            'status': 'pending',  # pending, active, completed
            'mesForwardedCount': 0,  # MES已轉發數量
            'completionRate': 0.0    # 工單達成率
        }

        # 新工單添加到列表末尾
        device['workOrders'].append(work_order)

        # 只有在沒有任何工單時才自動激活
        if device.get('currentWorkOrder') is None and len(device['workOrders']) == 1:
            self._activate_next_work_order(device_id)

        self._save_devices()
        return True

    def set_work_orders(self, device_id, work_orders):
        """設置設備的工單列表（通過工單號保持當前工單狀態）"""
        if device_id not in self.devices:
            raise ValueError(f"設備ID {device_id} 不存在")

        device = self.devices[device_id]
        old_work_orders = device.get('workOrders', [])
        old_current_index = device.get('currentWorkOrder')
        old_progress = device.get('currentWorkOrderProgress', 0)

        # 獲取當前活躍工單的工單號
        current_work_order_number = None
        if old_current_index is not None and old_current_index < len(old_work_orders):
            current_work_order_number = old_work_orders[old_current_index].get('workOrderNumber')

        # 創建舊工單的進度映射（以工單號為鍵）
        old_progress_map = {}
        for i, old_wo in enumerate(old_work_orders):
            work_order_number = old_wo.get('workOrderNumber')
            if work_order_number:
                old_progress_map[work_order_number] = {
                    'mesForwardedCount': old_wo.get('mesForwardedCount', 0),
                    'completionRate': old_wo.get('completionRate', 0.0),
                    'status': old_wo.get('status', 'pending'),
                    'startedAt': old_wo.get('startedAt'),
                    'completedAt': old_wo.get('completedAt')
                }

        # 更新工單列表，保持已有的進度數據
        for new_wo in work_orders:
            work_order_number = new_wo.get('workOrderNumber')
            if work_order_number and work_order_number in old_progress_map:
                # 保持舊的進度數據
                old_data = old_progress_map[work_order_number]
                new_wo['mesForwardedCount'] = old_data['mesForwardedCount']
                new_wo['completionRate'] = old_data['completionRate']
                new_wo['status'] = old_data['status']
                if old_data['startedAt']:
                    new_wo['startedAt'] = old_data['startedAt']
                if old_data['completedAt']:
                    new_wo['completedAt'] = old_data['completedAt']
            else:
                # 新工單，設置默認值
                new_wo['mesForwardedCount'] = new_wo.get('mesForwardedCount', 0)
                new_wo['completionRate'] = new_wo.get('completionRate', 0.0)
                new_wo['status'] = new_wo.get('status', 'pending')

            # 確保所有工單都有cavityCount字段
            if 'cavityCount' not in new_wo:
                new_wo['cavityCount'] = 1

        device['workOrders'] = work_orders

        # 檢查工單順序是否發生變化
        current_work_order_new_index = None
        if current_work_order_number:
            for i, new_wo in enumerate(work_orders):
                if new_wo.get('workOrderNumber') == current_work_order_number:
                    current_work_order_new_index = i
                    break

        # 檢查是否需要重新激活工單（優先級邏輯）
        should_reactivate = False

        if current_work_order_new_index is not None:
            # 當前工單仍存在，檢查是否有更高優先級的未完成工單
            for i, wo in enumerate(work_orders):
                if i < current_work_order_new_index:  # 優先級更高（索引更小）
                    if wo.get('completionRate', 0) < 100:  # 未完成
                        should_reactivate = True
                        print(f"🔄 發現更高優先級的未完成工單: 設備={device_id}, 工單={wo.get('workOrderNumber')}, 優先級={i+1}")
                        break

            if not should_reactivate:
                # 沒有更高優先級的工單，繼續當前工單
                device['currentWorkOrder'] = current_work_order_new_index
                device['currentWorkOrderProgress'] = old_progress
                work_orders[current_work_order_new_index]['status'] = 'active'
                print(f"🔄 工單重新排序: 設備={device_id}, 當前工單={current_work_order_number}, 新索引={current_work_order_new_index}, 進度={old_progress}")
            else:
                # 需要重新激活，暫停當前工單
                if current_work_order_new_index < len(work_orders):
                    work_orders[current_work_order_new_index]['status'] = 'pending'
                print(f"⏸️ 暫停當前工單: 設備={device_id}, 工單={current_work_order_number}, 進度={old_progress}")
        else:
            # 當前工單不存在了
            should_reactivate = True
            print(f"⚠️ 當前工單已被刪除: 設備={device_id}, 工單號={current_work_order_number}")

        # 重新激活最高優先級的未完成工單
        if should_reactivate:
            # 首先將所有工單設為pending狀態
            for wo in work_orders:
                if wo.get('status') != 'completed':
                    wo['status'] = 'pending'

            device['currentWorkOrder'] = None
            device['currentWorkOrderProgress'] = 0

            # 激活第一個未完成的工單
            if len(work_orders) > 0:
                if self._activate_next_work_order(device_id):
                    # 自動開始MES轉發
                    self.auto_start_mes_if_possible(device_id)
                    print(f"🚀 重新激活工單: 設備={device_id}, 按優先級順序執行")

        # 檢查是否需要自動停止MES轉發（所有工單都完成或沒有工單）
        self._check_and_auto_stop_mes(device_id, device)

        self._save_devices()
        return True

    def _activate_next_work_order(self, device_id):
        """激活下一個工單（保持歷史進度）"""
        device = self.devices[device_id]
        work_orders = device.get('workOrders', [])

        # 找到第一個pending狀態的工單
        for i, work_order in enumerate(work_orders):
            if work_order['status'] == 'pending':
                device['currentWorkOrder'] = i

                # 從工單的mesForwardedCount載入歷史進度，而不是重置為0
                historical_progress = work_order.get('mesForwardedCount', 0)
                device['currentWorkOrderProgress'] = historical_progress

                work_order['status'] = 'active'
                if 'startedAt' not in work_order or work_order['startedAt'] is None:
                    work_order['startedAt'] = datetime.now().isoformat()

                print(f"🔄 設備 {device_id} 激活工單: {work_order['workOrderNumber']}, 載入歷史進度: {historical_progress}/{work_order.get('targetQuantity', 0)}")
                return True

        # 沒有pending工單
        device['currentWorkOrder'] = None
        device['currentWorkOrderProgress'] = 0
        return False

    def get_current_work_order(self, device_id):
        """獲取設備當前工單信息"""
        if device_id not in self.devices:
            return None

        device = self.devices[device_id]
        current_index = device.get('currentWorkOrder')
        work_orders = device.get('workOrders', [])

        if current_index is None or current_index >= len(work_orders):
            return None

        work_order = work_orders[current_index].copy()
        work_order['progress'] = device.get('currentWorkOrderProgress', 0)
        work_order['completionRate'] = (work_order['progress'] / work_order['targetQuantity']) * 100 if work_order['targetQuantity'] > 0 else 0

        return work_order

    def update_work_order_progress(self, device_id, forwarded_count):
        """更新工單進度，檢查是否需要切換工單"""
        if device_id not in self.devices:
            return False

        device = self.devices[device_id]
        current_index = device.get('currentWorkOrder')

        if current_index is None or current_index >= len(device.get('workOrders', [])):
            return False

        # 更新當前工單進度
        device['currentWorkOrderProgress'] += forwarded_count
        current_work_order = device['workOrders'][current_index]

        # 更新工單的MES轉發數量和達成率
        current_work_order['mesForwardedCount'] = device['currentWorkOrderProgress']
        current_work_order['completionRate'] = (device['currentWorkOrderProgress'] / current_work_order['targetQuantity']) * 100 if current_work_order['targetQuantity'] > 0 else 0

        # 記錄最後一次MES轉發成功時間
        current_work_order['lastMesForwardedTime'] = datetime.now().isoformat()

        print(f"📋 工單進度更新: 設備={device_id}, 工單={current_work_order['workOrderNumber']}, 進度={device['currentWorkOrderProgress']}/{current_work_order['targetQuantity']}")

        # 更新最近使用工單列表（每次轉發成功都更新）
        self._update_recent_work_orders(device_id, current_work_order)

        # 檢查是否達標
        if device['currentWorkOrderProgress'] >= current_work_order['targetQuantity']:
            # 保存最後完成的工單信息
            device['lastCompletedWorkOrder'] = {
                'workOrderNumber': current_work_order['workOrderNumber'],
                'modelName': current_work_order.get('modelName', ''),
                'targetQuantity': current_work_order['targetQuantity'],
                'completedAt': datetime.now().isoformat()
            }

            # 完成當前工單
            current_work_order['status'] = 'completed'
            current_work_order['completedAt'] = datetime.now().isoformat()
            print(f"✅ 工單完成: 設備={device_id}, 工單={current_work_order['workOrderNumber']}")

            # 激活下一個工單
            if self._activate_next_work_order(device_id):
                print(f"🔄 自動切換到下一個工單")
            else:
                print(f"📋 所有工單已完成，自動停止MES轉發")
                # 自動停止MES轉發
                device['isMonitoring'] = False

        self._save_devices()
        return True

    def _update_recent_work_orders(self, device_id, work_order):
        """更新最近使用工單列表（每次MES轉發成功時調用）"""
        device = self.devices[device_id]
        if 'recentWorkOrders' not in device:
            device['recentWorkOrders'] = []

        # 只有當工單有MES轉發成功記錄時才加入列表
        if not work_order.get('lastMesForwardedTime'):
            return

        # 創建或更新最近使用工單記錄
        recent_work_order = {
            'workOrderNumber': work_order['workOrderNumber'],
            'modelName': work_order.get('modelName', ''),
            'targetQuantity': work_order['targetQuantity'],
            'mesForwardedCount': work_order.get('mesForwardedCount', 0),
            'completionRate': work_order.get('completionRate', 0.0),
            'lastMesForwardedTime': work_order['lastMesForwardedTime'],
            'status': work_order.get('status', 'active')
        }

        # 檢查是否已存在相同工單號，如果存在則更新
        existing_index = -1
        for i, existing_wo in enumerate(device['recentWorkOrders']):
            if existing_wo['workOrderNumber'] == work_order['workOrderNumber']:
                existing_index = i
                break

        if existing_index >= 0:
            # 更新現有記錄
            device['recentWorkOrders'][existing_index] = recent_work_order
        else:
            # 添加新記錄
            device['recentWorkOrders'].append(recent_work_order)

        # 按最後轉發時間排序（最新的在前）
        device['recentWorkOrders'].sort(key=lambda x: x['lastMesForwardedTime'], reverse=True)

        # 只保留最近5個工單
        if len(device['recentWorkOrders']) > 5:
            device['recentWorkOrders'] = device['recentWorkOrders'][:5]

    def get_work_orders(self, device_id):
        """獲取設備的所有工單"""
        if device_id not in self.devices:
            return []

        return self.devices[device_id].get('workOrders', [])

    def delete_work_order(self, device_id, work_order_index):
        """刪除指定工單（通過工單號保持當前工單狀態）"""
        if device_id not in self.devices:
            raise ValueError(f"設備ID {device_id} 不存在")

        device = self.devices[device_id]
        work_orders = device.get('workOrders', [])

        if work_order_index < 0 or work_order_index >= len(work_orders):
            raise ValueError(f"工單索引 {work_order_index} 無效")

        # 獲取要刪除的工單信息
        deleted_work_order = work_orders[work_order_index]
        deleted_work_order_number = deleted_work_order.get('workOrderNumber')

        # 獲取當前活躍工單信息
        current_index = device.get('currentWorkOrder')
        current_progress = device.get('currentWorkOrderProgress', 0)
        current_work_order_number = None

        if current_index is not None and current_index < len(work_orders):
            current_work_order_number = work_orders[current_index].get('workOrderNumber')

        # 刪除工單
        del work_orders[work_order_index]

        print(f"🗑️ 刪除工單: 設備={device_id}, 工單={deleted_work_order_number}, 索引={work_order_index}")

        # 重新找到當前活躍工單的新索引
        if current_work_order_number and current_work_order_number != deleted_work_order_number:
            # 當前工單沒有被刪除，需要找到它的新索引
            new_current_index = None
            for i, wo in enumerate(work_orders):
                if wo.get('workOrderNumber') == current_work_order_number:
                    new_current_index = i
                    break

            if new_current_index is not None:
                device['currentWorkOrder'] = new_current_index
                device['currentWorkOrderProgress'] = current_progress
                print(f"🔄 當前工單重新定位: 設備={device_id}, 工單={current_work_order_number}, 新索引={new_current_index}")
            else:
                # 找不到當前工單，重置狀態
                device['currentWorkOrder'] = None
                device['currentWorkOrderProgress'] = 0
                print(f"⚠️ 當前工單丟失: 設備={device_id}, 工單={current_work_order_number}")
        else:
            # 當前工單被刪除了，重置狀態
            device['currentWorkOrder'] = None
            device['currentWorkOrderProgress'] = 0
            print(f"🛑 當前工單被刪除: 設備={device_id}, 工單={deleted_work_order_number}")

        # 檢查是否需要自動停止MES轉發（所有工單都完成或沒有工單）
        self._check_and_auto_stop_mes(device_id, device)

        self._save_devices()
        return True

    def add_mes_error(self, device_id, error_message, full_response=None, card_response=None, request_data=None):
        """添加MES錯誤信息"""
        if device_id not in self.devices:
            return False

        device = self.devices[device_id]
        if 'mesErrors' not in device:
            device['mesErrors'] = []

        error_info = {
            'message': error_message,
            'full_response': full_response or error_message,
            'card_response': card_response or error_message,
            'request_data': request_data,
            'timestamp': datetime.now().isoformat()
        }

        device['mesErrors'].append(error_info)

        # 只保留最近50條錯誤記錄
        if len(device['mesErrors']) > 50:
            device['mesErrors'] = device['mesErrors'][-50:]

        self._save_devices()
        return True



    def forward_to_mes(self, device_id, product_data):
        """轉發產品數據到MES"""
        if device_id not in self.devices:
            print(f"❌ 設備 {device_id} 不存在")
            return False

        device = self.devices[device_id]
        current_work_order = self.get_current_work_order(device_id)

        if not current_work_order:
            error_msg = "沒有可用的工單信息"
            print(f"❌ MES轉發失敗: {error_msg}")
            self.add_mes_error(device_id, error_msg)
            return False

        # 準備設備信息
        device_info = {
            'deviceId': device['deviceId'],
            'lineName': device['lineName'],
            'sectionName': device['sectionName'],
            'groupName': device['groupName'],
            'stationName': device['stationName']
        }

        # 準備工單信息
        work_order_info = {
            'workOrder': current_work_order['workOrderNumber'],
            'model': current_work_order.get('modelName', device.get('model', '')),
            'cavityCount': current_work_order.get('cavityCount', 1),
            'stationType': current_work_order.get('stationType', '無條碼過站'),  # 添加過站類型
            'uploadType': current_work_order.get('uploadType', '同時上報PQM和MES')  # 添加上傳類型
        }

        # 根據上傳類型決定調用哪個API
        upload_type = work_order_info['uploadType']

        if upload_type == '僅上報PQM':
            # 僅調用PQM API
            from pqm_api import PQMApi
            pqm_api = PQMApi(self)

            # 計算轉發數量（用於PQM API）
            forwarded_count = 1  # 默認值，可以根據需要調整

            result = pqm_api.upload_production_data(
                device_info,
                work_order_info,
                product_data,
                forwarded_count
            )

            if result['success']:
                print(f"✅ PQM上傳成功: 設備={device_id}, 產品={product_data['UnitIdentifier']}")
                success_msg = result.get('message', 'PQM上傳成功')
                full_response = result.get('full_response', success_msg)
                card_response = result.get('card_response', success_msg)
                request_data = result.get('request_data')
                print(f"✅ 完整PQM響應: {full_response}")
                self.add_mes_error(device_id, success_msg, full_response, card_response, request_data)
                return True
            else:
                error_msg = result['message']
                full_response = result.get('full_response', error_msg)
                card_response = result.get('card_response', error_msg)
                request_data = result.get('request_data')
                print(f"❌ PQM上傳失敗: 設備={device_id}, 錯誤={error_msg}")
                print(f"❌ 完整PQM錯誤響應: {full_response}")
                self.add_mes_error(device_id, error_msg, full_response, card_response, request_data)
                return False
        else:
            # 調用MES API（包含PQM上傳）- 默認行為
            mes_api = self._get_mes_api()
            result = mes_api.upload_production_data(
                device_info,
                work_order_info,
                product_data
            )

            if result['success']:
                print(f"✅ MES轉發成功: 設備={device_id}, 產品={product_data['UnitIdentifier']}")
                # 成功時也記錄完整響應和請求數據
                success_msg = result.get('message', '轉發成功')
                full_response = result.get('full_response', success_msg)
                card_response = result.get('card_response', success_msg)
                request_data = result.get('request_data')
                print(f"✅ 完整成功響應: {full_response}")
                self.add_mes_error(device_id, success_msg, full_response, card_response, request_data)
                return True
            else:
                error_msg = result['message']
                full_response = result.get('full_response', error_msg)
                card_response = result.get('card_response', error_msg)
                request_data = result.get('request_data')
                print(f"❌ MES轉發失敗: 設備={device_id}, 錯誤={error_msg}")
                print(f"❌ 完整錯誤響應: {full_response}")
                self.add_mes_error(device_id, error_msg, full_response, card_response, request_data)
                return False



    def _check_and_auto_stop_mes(self, device_id, device):
        """檢查工單狀態並自動停止MES轉發"""
        current_work_order = self.get_current_work_order(device_id)

        if current_work_order is None:
            # 沒有當前工單，檢查是否有工單列表
            work_orders = device.get('workOrders', [])
            if len(work_orders) == 0:
                # 情況一：沒有任何工單
                if device.get('isMonitoring', False):
                    device['isMonitoring'] = False
                    print(f"🛑 設備 {device_id}: 無工單配置，自動停止MES轉發")
            else:
                # 情況二：有工單但都已完成
                if device.get('isMonitoring', False):
                    device['isMonitoring'] = False
                    print(f"🛑 設備 {device_id}: 所有工單已完成，自動停止MES轉發")

    def can_start_mes_forwarding(self, device_id):
        """檢查是否可以開始MES轉發"""
        if device_id not in self.devices:
            return False, "設備不存在"

        device = self.devices[device_id]
        current_work_order = self.get_current_work_order(device_id)
        work_orders = device.get('workOrders', [])

        if len(work_orders) == 0:
            # 情況一：沒有任何工單
            return False, "沒有設定工單機種信息，請維護工單管理"

        if current_work_order is None:
            # 情況二：有工單但都已完成，不允許開啟MES轉發
            return False, "所有工單已完成，請配置新的工單才能開啟MES轉發"

        # 有當前工單，可以轉發
        return True, "有當前工單，可以轉發"

    def get_display_work_order_info(self, device_id):
        """獲取設備卡片顯示的工單信息"""
        if device_id not in self.devices:
            return None

        device = self.devices[device_id]
        current_work_order = self.get_current_work_order(device_id)

        if current_work_order:
            # 有當前工單，顯示當前工單信息
            return {
                'workOrderNumber': current_work_order['workOrderNumber'],
                'modelName': current_work_order.get('modelName', ''),
                'isActive': True
            }
        else:
            # 沒有當前工單，顯示空信息
            return {
                'workOrderNumber': '',
                'modelName': '',
                'isActive': False
            }

    def auto_start_mes_if_possible(self, device_id):
        """如果有可用工單，自動開始MES轉發"""
        can_start, message = self.can_start_mes_forwarding(device_id)
        if can_start:
            device = self.devices[device_id]
            if not device.get('isMonitoring', False):
                device['isMonitoring'] = True
                self._save_devices()
                print(f"🚀 設備 {device_id}: 檢測到可用工單，自動開始MES轉發")
                return True
        return False

    def enable_all_available_devices(self):
        """批量啟用所有有可用工單的設備的MES轉發功能"""
        enabled_count = 0

        for device_id, device in self.devices.items():
            # 檢查設備是否已經啟用MES轉發
            if device.get('isMonitoring', False):
                continue

            # 檢查設備是否有可用工單
            can_start, message = self.can_start_mes_forwarding(device_id)
            if can_start:
                device['isMonitoring'] = True
                enabled_count += 1
                print(f"🚀 批量啟用: 設備 {device_id} MES轉發已啟動 - {message}")
            else:
                print(f"⚠️ 跳過設備 {device_id}: {message}")

        if enabled_count > 0:
            self._save_devices()
            print(f"✅ 批量啟用完成: 共啟用 {enabled_count} 個設備的MES轉發功能")
        else:
            print(f"ℹ️ 批量啟用完成: 沒有設備可以啟用MES轉發功能")

        return enabled_count
