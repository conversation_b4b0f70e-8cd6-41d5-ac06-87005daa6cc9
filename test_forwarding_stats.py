"""
測試轉發統計功能
"""

import json
import time
from datetime import datetime
from device_manager import <PERSON><PERSON>Mana<PERSON>
from kafka_monitor import KafkaMonitor
from config import TEST_DATA

class ForwardingStatsTest:
    def __init__(self):
        self.device_manager = DeviceManager()
        self.kafka_monitor = KafkaMonitor(self.device_manager)
    
    def create_test_message(self, unit_identifier='TEST001', status='Pass'):
        """創建測試Kafka消息"""
        test_message = TEST_DATA['sample_kafka_message'].copy()
        
        # 更新時間戳
        current_time = datetime.now().isoformat() + '+00:00'
        test_message['MessageTime'] = current_time
        test_message['Data']['ProcessedTime'] = current_time
        test_message['Data']['RawData']['TimeStamp'] = current_time
        
        # 更新產品數據
        test_message['Data']['RawData']['MessageBody']['Units'][0]['UnitIdentifier'] = unit_identifier
        test_message['Data']['RawData']['MessageBody']['Units'][0]['Status'] = status
        test_message['Data']['RawData']['MessageBody']['Units'][0]['PositionName'] = f'{unit_identifier}_1'
        
        return test_message
    
    def simulate_kafka_message(self, test_message):
        """模擬Kafka消息處理"""
        class MockMessage:
            def __init__(self, value, timestamp):
                self.value = value
                self.timestamp = timestamp * 1000
                self.topic = 'EAP.CZ.MAG.MAG-H71.DEVICE_CFX.CFX.Production.UnitsDeparted'
                self.partition = 0
                self.offset = 99999
        
        mock_message = MockMessage(test_message, time.time())
        self.kafka_monitor._process_message(mock_message)
    
    def test_forwarded_stats(self):
        """測試已轉發統計"""
        print("\n🟢 測試場景1: MES轉發啟用 - 已轉發統計")
        print("="*60)
        
        # 確保設備啟用且MES轉發啟用
        self.device_manager.update_device('SC21100803', {'isActive': True})
        self.device_manager.toggle_device_monitoring('SC21100803', True)
        
        # 記錄初始狀態
        device_before = self.device_manager.get_device('SC21100803')
        forwarded_before = device_before.get('forwardedCount', 0)
        unforwarded_before = device_before.get('unforwardedCount', 0)
        total_before = device_before.get('productCount', 0)
        
        print(f"初始狀態 - 總產量: {total_before}, 已轉發: {forwarded_before}, 未轉發: {unforwarded_before}")
        
        # 發送測試消息
        test_message = self.create_test_message('FORWARD001', 'Pass')
        self.simulate_kafka_message(test_message)
        
        # 檢查結果
        device_after = self.device_manager.get_device('SC21100803')
        forwarded_after = device_after.get('forwardedCount', 0)
        unforwarded_after = device_after.get('unforwardedCount', 0)
        total_after = device_after.get('productCount', 0)
        
        print(f"處理後狀態 - 總產量: {total_after}, 已轉發: {forwarded_after}, 未轉發: {unforwarded_after}")
        print(f"📋 日誌內容: {device_after['lastLog']}")
        print("✅ 預期結果: 總產量+1, 已轉發+1, 未轉發不變, 顯示✅符號")
    
    def test_unforwarded_stats(self):
        """測試未轉發統計"""
        print("\n🟡 測試場景2: MES轉發停用 - 未轉發統計")
        print("="*60)
        
        # 記錄當前狀態
        device_before = self.device_manager.get_device('SC21100803')
        forwarded_before = device_before.get('forwardedCount', 0)
        unforwarded_before = device_before.get('unforwardedCount', 0)
        total_before = device_before.get('productCount', 0)
        
        print(f"初始狀態 - 總產量: {total_before}, 已轉發: {forwarded_before}, 未轉發: {unforwarded_before}")
        
        # 確保設備啟用但MES轉發停用
        self.device_manager.update_device('SC21100803', {'isActive': True})
        self.device_manager.toggle_device_monitoring('SC21100803', False)
        
        # 發送測試消息
        test_message = self.create_test_message('UNFORWARD001', 'Pass')
        self.simulate_kafka_message(test_message)
        
        # 檢查結果
        device_after = self.device_manager.get_device('SC21100803')
        forwarded_after = device_after.get('forwardedCount', 0)
        unforwarded_after = device_after.get('unforwardedCount', 0)
        total_after = device_after.get('productCount', 0)
        
        print(f"處理後狀態 - 總產量: {total_after}, 已轉發: {forwarded_after}, 未轉發: {unforwarded_after}")
        print(f"📋 日誌內容: {device_after['lastLog']}")
        print("✅ 預期結果: 總產量+1, 已轉發不變, 未轉發+1, 顯示❓符號")
    
    def test_multiple_products(self):
        """測試多產品統計"""
        print("\n🔄 測試場景3: 多產品混合統計")
        print("="*60)
        
        # 記錄初始狀態
        device_before = self.device_manager.get_device('SC21100803')
        forwarded_before = device_before.get('forwardedCount', 0)
        unforwarded_before = device_before.get('unforwardedCount', 0)
        total_before = device_before.get('productCount', 0)
        
        print(f"初始狀態 - 總產量: {total_before}, 已轉發: {forwarded_before}, 未轉發: {unforwarded_before}")
        
        # 測試轉發啟用
        self.device_manager.toggle_device_monitoring('SC21100803', True)
        test_message1 = self.create_test_message('MULTI001', 'Pass')
        self.simulate_kafka_message(test_message1)
        
        # 測試轉發停用
        self.device_manager.toggle_device_monitoring('SC21100803', False)
        test_message2 = self.create_test_message('MULTI002', 'Fail')
        self.simulate_kafka_message(test_message2)
        
        # 檢查最終結果
        device_final = self.device_manager.get_device('SC21100803')
        forwarded_final = device_final.get('forwardedCount', 0)
        unforwarded_final = device_final.get('unforwardedCount', 0)
        total_final = device_final.get('productCount', 0)
        
        print(f"最終狀態 - 總產量: {total_final}, 已轉發: {forwarded_final}, 未轉發: {unforwarded_final}")
        print(f"📋 日誌內容: {device_final['lastLog']}")
        print("✅ 預期結果: 總產量+2, 已轉發+1, 未轉發+1")
    
    def run_all_tests(self):
        """運行所有測試"""
        print("🧪 轉發統計功能測試")
        print("="*80)
        
        # 確保測試設備存在
        try:
            self.device_manager.add_device(
                device_id='SC21100803',
                line_name='MAG-H71',
                section_name='測試段',
                group_name='測試組',
                station_name='測試站',
                work_order='WO20250610001',
                model='TestModel-A'
            )
        except ValueError:
            pass  # 設備已存在
        
        # 運行測試
        self.test_forwarded_stats()
        time.sleep(1)
        
        self.test_unforwarded_stats()
        time.sleep(1)
        
        self.test_multiple_products()
        
        print("\n" + "="*80)
        print("✅ 所有轉發統計測試完成")
        print("="*80)

if __name__ == '__main__':
    test = ForwardingStatsTest()
    test.run_all_tests()
