#!/usr/bin/env python3
"""
啟動腳本
提供多種啟動選項
"""

import sys
import os
import subprocess
import argparse

def check_dependencies():
    """檢查依賴是否安裝"""
    try:
        import flask
        import kafka
        print("✅ 所有依賴已安裝")
        return True
    except ImportError as e:
        print(f"❌ 缺少依賴: {e}")
        print("請運行: pip install -r requirements.txt")
        return False

def install_dependencies():
    """安裝依賴"""
    print("正在安裝依賴...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依賴安裝完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依賴安裝失敗")
        return False

def start_web_app():
    """啟動Web應用"""
    print("🚀 啟動Web應用...")
    print("訪問地址: http://localhost:5000")
    print("按 Ctrl+C 停止應用")
    
    try:
        from app import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 應用已停止")
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")

def run_test():
    """運行測試"""
    print("🧪 運行測試數據模擬器...")
    try:
        from test_data_simulator import TestDataSimulator
        simulator = TestDataSimulator()
        simulator.run_all_tests()
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

def main():
    parser = argparse.ArgumentParser(description='設備數據轉發管理系統啟動腳本')
    parser.add_argument('--install', action='store_true', help='安裝依賴')
    parser.add_argument('--test', action='store_true', help='運行測試')
    parser.add_argument('--check', action='store_true', help='檢查依賴')
    
    args = parser.parse_args()
    
    print("🏭 設備數據轉發管理系統")
    print("="*50)
    
    if args.install:
        install_dependencies()
        return
    
    if args.check:
        check_dependencies()
        return
    
    if args.test:
        if not check_dependencies():
            return
        run_test()
        return
    
    # 默認啟動Web應用
    if not check_dependencies():
        print("\n是否要自動安裝依賴? (y/n): ", end="")
        if input().lower() in ['y', 'yes', '是']:
            if not install_dependencies():
                return
        else:
            return
    
    start_web_app()

if __name__ == '__main__':
    main()
