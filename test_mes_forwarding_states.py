"""
測試MES轉發不同狀態下的日誌顯示
"""

import json
import time
from datetime import datetime
from device_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>
from kafka_monitor import KafkaMonitor
from config import TEST_DATA

class MESForwardingStateTest:
    def __init__(self):
        self.device_manager = DeviceManager()
        self.kafka_monitor = KafkaMonitor(self.device_manager)
    
    def create_test_message(self, unit_identifier='TEST001', status='Pass'):
        """創建測試Kafka消息"""
        test_message = TEST_DATA['sample_kafka_message'].copy()
        
        # 更新時間戳
        current_time = datetime.now().isoformat() + '+00:00'
        test_message['MessageTime'] = current_time
        test_message['Data']['ProcessedTime'] = current_time
        test_message['Data']['RawData']['TimeStamp'] = current_time
        
        # 更新產品數據
        test_message['Data']['RawData']['MessageBody']['Units'][0]['UnitIdentifier'] = unit_identifier
        test_message['Data']['RawData']['MessageBody']['Units'][0]['Status'] = status
        test_message['Data']['RawData']['MessageBody']['Units'][0]['PositionName'] = f'{unit_identifier}_1'
        
        return test_message
    
    def simulate_kafka_message(self, test_message):
        """模擬Kafka消息處理"""
        class MockMessage:
            def __init__(self, value, timestamp):
                self.value = value
                self.timestamp = timestamp * 1000
                self.topic = 'EAP.CZ.MAG.MAG-H71.DEVICE_CFX.CFX.Production.UnitsDeparted'
                self.partition = 0
                self.offset = 88888
        
        mock_message = MockMessage(test_message, time.time())
        self.kafka_monitor._process_message(mock_message)
    
    def test_mes_forwarding_enabled(self):
        """測試MES轉發啟用狀態"""
        print("\n🟢 測試場景1: 設備啟用 + MES轉發啟用")
        print("="*60)
        
        # 確保設備啟用且MES轉發啟用
        self.device_manager.update_device('SC21100803', {'isActive': True})
        self.device_manager.toggle_device_monitoring('SC21100803', True)
        
        # 發送測試消息
        test_message = self.create_test_message('ENABLED001', 'Pass')
        self.simulate_kafka_message(test_message)
        
        # 檢查結果
        device = self.device_manager.get_device('SC21100803')
        print(f"📋 日誌內容: {device['lastLog']}")
        print(f"📊 產量統計: {device['productCount']}")
        print("✅ 預期結果: 顯示 ✅ 符號，產量增加")
    
    def test_mes_forwarding_disabled(self):
        """測試MES轉發停用狀態"""
        print("\n🟡 測試場景2: 設備啟用 + MES轉發停用")
        print("="*60)
        
        # 記錄當前產量
        device_before = self.device_manager.get_device('SC21100803')
        product_count_before = device_before['productCount']
        
        # 確保設備啟用但MES轉發停用
        self.device_manager.update_device('SC21100803', {'isActive': True})
        self.device_manager.toggle_device_monitoring('SC21100803', False)
        
        # 發送測試消息
        test_message = self.create_test_message('DISABLED001', 'Pass')
        self.simulate_kafka_message(test_message)
        
        # 檢查結果
        device_after = self.device_manager.get_device('SC21100803')
        print(f"📋 日誌內容: {device_after['lastLog']}")
        print(f"📊 產量統計: {device_after['productCount']} (之前: {product_count_before})")
        print("✅ 預期結果: 顯示 ❓ 符號，產量不變")
    
    def test_device_disabled(self):
        """測試設備停用狀態"""
        print("\n🔴 測試場景3: 設備停用")
        print("="*60)
        
        # 停用設備
        self.device_manager.update_device('SC21100803', {'isActive': False})
        
        # 發送測試消息
        test_message = self.create_test_message('INACTIVE001', 'Pass')
        self.simulate_kafka_message(test_message)
        
        print("✅ 預期結果: 消息被跳過，不處理")
    
    def test_multiple_products_disabled(self):
        """測試多產品在MES轉發停用狀態"""
        print("\n🟡 測試場景4: 多產品 + MES轉發停用")
        print("="*60)
        
        # 確保設備啟用但MES轉發停用
        self.device_manager.update_device('SC21100803', {'isActive': True})
        self.device_manager.toggle_device_monitoring('SC21100803', False)
        
        # 創建多產品消息
        test_message = TEST_DATA['sample_kafka_message'].copy()
        current_time = datetime.now().isoformat() + '+00:00'
        test_message['MessageTime'] = current_time
        test_message['Data']['ProcessedTime'] = current_time
        test_message['Data']['RawData']['TimeStamp'] = current_time
        
        # 多個產品
        units = [
            {
                'UnitIdentifier': 'MULTI001',
                'PositionNumber': 1,
                'PositionName': 'MULTI001_1',
                'X': 0.0, 'Y': 0.0, 'Rotation': 0.0,
                'FlipX': False, 'FlipY': False,
                'Status': 'Pass'
            },
            {
                'UnitIdentifier': 'MULTI002',
                'PositionNumber': 2,
                'PositionName': 'MULTI002_2',
                'X': 0.0, 'Y': 0.0, 'Rotation': 0.0,
                'FlipX': False, 'FlipY': False,
                'Status': 'Fail'
            }
        ]
        
        test_message['Data']['RawData']['MessageBody']['Units'] = units
        test_message['Data']['RawData']['MessageBody']['UnitCount'] = len(units)
        
        self.simulate_kafka_message(test_message)
        
        # 檢查結果
        device = self.device_manager.get_device('SC21100803')
        print(f"📋 日誌內容: {device['lastLog']}")
        print("✅ 預期結果: 顯示多個產品條碼，都帶 ❓ 符號")
    
    def run_all_tests(self):
        """運行所有測試"""
        print("🧪 MES轉發狀態測試")
        print("="*80)
        
        # 確保測試設備存在
        try:
            self.device_manager.add_device(
                device_id='SC21100803',
                line_name='MAG-H71',
                section_name='測試段',
                group_name='測試組',
                station_name='測試站',
                work_order='WO20250610001',
                model='TestModel-A'
            )
        except ValueError:
            pass  # 設備已存在
        
        # 運行測試
        self.test_mes_forwarding_enabled()
        time.sleep(1)
        
        self.test_mes_forwarding_disabled()
        time.sleep(1)
        
        self.test_device_disabled()
        time.sleep(1)
        
        self.test_multiple_products_disabled()
        
        print("\n" + "="*80)
        print("✅ 所有測試完成")
        print("="*80)

if __name__ == '__main__':
    test = MESForwardingStateTest()
    test.run_all_tests()
