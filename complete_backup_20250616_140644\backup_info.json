{"backup_time": "2025-06-16T14:06:55.957573", "backup_type": "complete_project", "python_version": "3.7.9 (tags/v3.7.9:13c94747c7, Aug 17 2020, 18:01:55) [MSC v.1900 32 bit (Intel)]", "working_directory": "E:\\vscode\\tomesmo", "included_files": ["AI編程項目大綱範例.md", "app.py", "BACKUP_SUMMARY.md", "build_english_exe.py", "build_exe.py", "COMPLETE_FEATURE_SUMMARY.md", "config.py", "create_docker_package.py", "create_english_zip.py", "create_zip.py", "devices.json", "device_manager.py", "environment_info.json", "fake_data_generator.py", "FEATURE_UPDATE.md", "FINAL_FEATURE_SUMMARY.md", "getdata.py", "getdatafromrabbitmq.py", "kafka_config.json", "kafka_config_manager.py", "kafka_monitor.py", "MESnewtdc.py", "mes_api.py", "mes_forwarder.py", "mes_system.spec", "mes_system_english.spec", "mes_upload_manager.spec", "migrate_devices.py", "migrate_forwarding_stats.py", "migrate_period_logs.py", "open_browser.py", "Open_Browser.spec", "open_browser_english.spec", "pip_freeze.txt", "python_version.txt", "rabbitmq_config.json", "rabbitmq_config_manager.py", "rabbitmq_monitor.py", "README.md", "README_RabbitMQ.md", "rengong.py", "requirements.txt", "start.py", "test_config.py", "test_data_generator.py", "test_data_simulator.py", "test_devices_100.json", "test_devices_40.json", "test_devices_50.json", "test_devices_60.json", "test_devices_70.json", "UPDATE_SUMMARY.md", "dist\\MES_Upload_Manager.exe", "dist\\Open_Browser.exe", "MES_Upload_Manager_v0.0.1-beta-1\\devices.json", "MES_Upload_Manager_v0.0.1-beta-1\\fake_data_generator.py", "MES_Upload_Manager_v0.0.1-beta-1\\kafka_config.json", "MES_Upload_Manager_v0.0.1-beta-1\\Launch_MES_Upload_Manager.bat", "MES_Upload_Manager_v0.0.1-beta-1\\MES_Upload_Manager.exe", "MES_Upload_Manager_v0.0.1-beta-1\\Open_Browser.exe", "MES_Upload_Manager_v0.0.1-beta-1\\rabbitmq_config.json", "MES_Upload_Manager_v0.0.1-beta-1\\README.txt", "MES_Upload_Manager_v0.0.1-beta-1\\test_config.py", "MES_Upload_Manager_v0.0.1-beta-1\\test_data_generator.py", "MES_Upload_Manager_v0.0.1-beta-1\\static\\logo.png", "MES_Upload_Manager_v0.0.1-beta-1\\static\\script.js", "MES_Upload_Manager_v0.0.1-beta-1\\static\\style.css", "MES_Upload_Manager_v0.0.1-beta-1\\templates\\index.html", "static\\logo.png", "static\\script.js", "static\\style.css", "templates\\index.html"], "included_directories": ["dist", "MES_Upload_Manager_v0.0.1-beta-1", "static", "templates", "MES_Upload_Manager_v0.0.1-beta-1\\static", "MES_Upload_Manager_v0.0.1-beta-1\\templates"]}