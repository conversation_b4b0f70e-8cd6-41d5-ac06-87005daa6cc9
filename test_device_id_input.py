#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試設備ID輸入框修復
驗證添加設備和編輯設備時設備ID字段的可輸入性
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def test_device_id_input():
    """測試設備ID輸入框功能"""
    print("=== 測試設備ID輸入框功能 ===")
    
    # 設置Chrome選項
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 無頭模式
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    driver = None
    
    try:
        # 啟動瀏覽器
        driver = webdriver.Chrome(options=chrome_options)
        driver.get("http://127.0.0.1:5000")
        
        # 等待頁面載入
        wait = WebDriverWait(driver, 10)
        wait.until(EC.presence_of_element_located((By.ID, "add-device-btn")))
        
        print("✅ 頁面載入成功")
        
        # 測試1: 添加設備時設備ID可輸入
        print("\n🧪 測試1: 添加設備時設備ID可輸入")
        
        # 點擊添加設備按鈕
        add_device_btn = driver.find_element(By.ID, "add-device-btn")
        add_device_btn.click()
        
        # 等待模態框出現
        wait.until(EC.visibility_of_element_located((By.ID, "device-modal")))
        
        # 檢查設備ID輸入框
        device_id_input = driver.find_element(By.ID, "deviceId")
        
        # 檢查是否可輸入
        is_readonly = device_id_input.get_attribute("readOnly")
        is_disabled = device_id_input.get_attribute("disabled")
        
        print(f"   設備ID輸入框 readOnly: {is_readonly}")
        print(f"   設備ID輸入框 disabled: {is_disabled}")
        
        if not is_readonly and not is_disabled:
            print("   ✅ 添加設備時設備ID可輸入")
            
            # 嘗試輸入文字
            device_id_input.clear()
            device_id_input.send_keys("TEST_DEVICE_001")
            
            # 檢查輸入是否成功
            input_value = device_id_input.get_attribute("value")
            if input_value == "TEST_DEVICE_001":
                print("   ✅ 設備ID輸入功能正常")
            else:
                print(f"   ❌ 設備ID輸入失敗，實際值: {input_value}")
        else:
            print("   ❌ 添加設備時設備ID不可輸入")
        
        # 關閉模態框
        close_btn = driver.find_element(By.CSS_SELECTOR, "#device-modal .close")
        close_btn.click()
        
        # 等待模態框關閉
        wait.until(EC.invisibility_of_element_located((By.ID, "device-modal")))
        
        # 測試2: 先添加一個設備，然後測試編輯時的行為
        print("\n🧪 測試2: 編輯設備時設備ID只讀")
        
        # 檢查是否有現有設備
        device_cards = driver.find_elements(By.CSS_SELECTOR, ".device-card")
        
        if device_cards:
            # 找到第一個設備的編輯按鈕
            first_device_card = device_cards[0]
            edit_btn = first_device_card.find_element(By.CSS_SELECTOR, ".btn-edit")
            edit_btn.click()
            
            # 等待模態框出現
            wait.until(EC.visibility_of_element_located((By.ID, "device-modal")))
            
            # 檢查設備ID輸入框
            device_id_input = driver.find_element(By.ID, "deviceId")
            
            # 檢查是否只讀
            is_readonly = device_id_input.get_attribute("readOnly")
            is_disabled = device_id_input.get_attribute("disabled")
            
            print(f"   編輯模式 - 設備ID輸入框 readOnly: {is_readonly}")
            print(f"   編輯模式 - 設備ID輸入框 disabled: {is_disabled}")
            
            if is_readonly == "true":
                print("   ✅ 編輯設備時設備ID正確設為只讀")
            else:
                print("   ❌ 編輯設備時設備ID應該是只讀的")
            
            # 關閉模態框
            close_btn = driver.find_element(By.CSS_SELECTOR, "#device-modal .close")
            close_btn.click()
            
            # 等待模態框關閉
            wait.until(EC.invisibility_of_element_located((By.ID, "device-modal")))
            
        else:
            print("   ⚠️ 沒有現有設備，跳過編輯測試")
        
        # 測試3: 再次測試添加設備，確保編輯後添加功能仍正常
        print("\n🧪 測試3: 編輯後再次添加設備")
        
        # 點擊添加設備按鈕
        add_device_btn = driver.find_element(By.ID, "add-device-btn")
        add_device_btn.click()
        
        # 等待模態框出現
        wait.until(EC.visibility_of_element_located((By.ID, "device-modal")))
        
        # 檢查設備ID輸入框
        device_id_input = driver.find_element(By.ID, "deviceId")
        
        # 檢查是否可輸入
        is_readonly = device_id_input.get_attribute("readOnly")
        is_disabled = device_id_input.get_attribute("disabled")
        
        print(f"   再次添加 - 設備ID輸入框 readOnly: {is_readonly}")
        print(f"   再次添加 - 設備ID輸入框 disabled: {is_disabled}")
        
        if not is_readonly and not is_disabled:
            print("   ✅ 編輯後再次添加設備時設備ID仍可輸入")
            
            # 嘗試輸入文字
            device_id_input.clear()
            device_id_input.send_keys("TEST_DEVICE_002")
            
            # 檢查輸入是否成功
            input_value = device_id_input.get_attribute("value")
            if input_value == "TEST_DEVICE_002":
                print("   ✅ 設備ID輸入功能持續正常")
            else:
                print(f"   ❌ 設備ID輸入失敗，實際值: {input_value}")
        else:
            print("   ❌ 編輯後再次添加設備時設備ID不可輸入")
        
        # 關閉模態框
        close_btn = driver.find_element(By.CSS_SELECTOR, "#device-modal .close")
        close_btn.click()
        
        print("\n✅ 設備ID輸入框測試完成")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        if driver:
            driver.quit()

def test_manual_verification():
    """手動驗證指南"""
    print("\n=== 手動驗證指南 ===")
    print("請在瀏覽器中打開 http://127.0.0.1:5000 並執行以下測試:")
    print()
    print("1. 點擊「添加設備」按鈕")
    print("   - 檢查「設備ID」輸入框是否可以輸入文字")
    print("   - 嘗試輸入一些文字，確認可以正常輸入")
    print()
    print("2. 如果有現有設備，點擊設備卡片上的「編輯」按鈕")
    print("   - 檢查「設備ID」輸入框是否變為只讀（灰色背景）")
    print("   - 嘗試輸入文字，確認無法修改")
    print()
    print("3. 關閉編輯模態框，再次點擊「添加設備」")
    print("   - 檢查「設備ID」輸入框是否恢復可輸入狀態")
    print("   - 確認可以正常輸入新的設備ID")
    print()
    print("如果以上測試都通過，說明設備ID輸入框修復成功！")

if __name__ == "__main__":
    print("開始測試設備ID輸入框修復...\n")
    
    try:
        # 嘗試自動化測試
        test_device_id_input()
        
    except Exception as e:
        print(f"自動化測試失敗: {e}")
        print("改為提供手動驗證指南...")
        
    # 提供手動驗證指南
    test_manual_verification()
