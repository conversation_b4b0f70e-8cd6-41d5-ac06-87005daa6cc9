import requests
import json
from datetime import datetime
import time

class PQMApi:
    """PQM API調用模組"""
    
    def __init__(self, device_manager=None):
        # PQM API配置
        self.url_pqm = "http://10.148.192.32:8090/sensordata"
        self.headers_pqm = {
            # 'Content-Type': "application/json"  # 根據參考文件，這裡註釋掉
        }
        self.params_pqm = {
            'sensorId': "UploadMachineData"
        }
        self.device_manager = device_manager
        
        # 記錄設備的時間信息
        self.device_times = {}  # 記錄每個設備的時間信息
        self.program_start_time = datetime.now()  # 程序啟動時間
        
    def _get_device_times(self, device_id):
        """獲取設備的時間信息"""
        if device_id not in self.device_times:
            # 首次記錄該設備
            current_time = datetime.now()
            self.device_times[device_id] = {
                'creation_time': current_time,  # 設備創建時間
                'last_upload_time': None,       # 上次上傳時間
                'last_modification_time': current_time  # 上次修改時間
            }
        return self.device_times[device_id]
    
    def update_device_creation_time(self, device_id):
        """更新設備創建/修改時間（當設備被新增或修改時調用）"""
        current_time = datetime.now()
        if device_id not in self.device_times:
            self.device_times[device_id] = {
                'creation_time': current_time,
                'last_upload_time': None,
                'last_modification_time': current_time
            }
        else:
            self.device_times[device_id]['last_modification_time'] = current_time
        print(f"📋 更新設備 {device_id} 的修改時間: {current_time}")
    
    def _calculate_cycle_time(self, device_id):
        """計算cycleTime（本次生產所使用的時間）"""
        device_times = self._get_device_times(device_id)
        current_time = datetime.now()
        
        if device_times['last_upload_time'] is None:
            # 首次上傳，默認30秒
            cycle_time = 30.0
            print(f"📋 設備 {device_id} 首次上傳PQM，cycleTime默認: {cycle_time}秒")
        else:
            # 計算與上次上傳的時間差
            time_diff = current_time - device_times['last_upload_time']
            cycle_time = time_diff.total_seconds()
            print(f"📋 設備 {device_id} cycleTime計算: {cycle_time}秒 (距離上次上傳)")
        
        # 更新上次上傳時間
        device_times['last_upload_time'] = current_time
        
        return cycle_time
    
    def _calculate_running_time(self, device_id):
        """計算runningTime（設備運行時間）"""
        device_times = self._get_device_times(device_id)
        current_time = datetime.now()
        
        # 使用設備創建時間或最後修改時間作為起始時間
        start_time = max(device_times['creation_time'], device_times['last_modification_time'])
        
        # 如果程序啟動時就有這個設備，使用程序啟動時間
        if device_times['creation_time'] <= self.program_start_time:
            start_time = self.program_start_time
            print(f"📋 設備 {device_id} 使用程序啟動時間作為起始點")
        else:
            print(f"📋 設備 {device_id} 使用設備創建/修改時間作為起始點")
        
        time_diff = current_time - start_time
        running_time = time_diff.total_seconds()
        
        print(f"📋 設備 {device_id} runningTime計算: {running_time}秒 (從 {start_time} 開始)")
        
        return running_time
    
    def upload_production_data(self, device_info, work_order_info, product_data, forwarded_count):
        """
        上傳生產數據到PQM
        
        Args:
            device_info: 設備信息 {deviceId, lineName, sectionName, groupName, stationName}
            work_order_info: 工單信息 {workOrder, model, cavityCount}
            product_data: 產品數據 {unitIdentifier, status}
            forwarded_count: 已轉發數量
            
        Returns:
            dict: {success: bool, message: str, response: dict}
        """
        try:
            device_id = device_info['deviceId']
            
            # 計算時間相關參數
            cycle_time = self._calculate_cycle_time(device_id)
            running_time = self._calculate_running_time(device_id)
            
            # 準備PQM數據
            data_pqm = {
                'interfaceID': device_id,                           # 設備ID
                'status': 0,                                        # 固定為0
                'statusCode': '',                                   # 固定為空字符串
                'passQty': forwarded_count,                         # 已轉發數量
                'failQty': 0,                                       # 固定為0
                'errorCnt': 0,                                      # 固定為0
                'errorTimes': 0.0,                                  # 固定為0.0
                'cycleTime': cycle_time,                            # 本次生產時間
                'runningTime': running_time,                        # 設備運行時間
                'waitingTime': 0.0,                                 # 固定為0.0
                'selfCheck': 1,                                     # 固定為1
                'inputQty': forwarded_count,                        # 已轉發數量
                'barcode': product_data['UnitIdentifier'],          # 產品條碼
                'model': work_order_info['model'],                  # 機種
                'paramList': [                                      # 固定參數列表
                    {'paramCode': 'CT_M', 'paramValue': '0'},
                    {'paramCode': 'CT_Q', 'paramValue': '0'}
                ]
            }
            
            # 將數據包裝成列表（參考uploadtopqm.py的格式）
            testdata = [data_pqm]
            
            # 發送請求
            print(f"📤 發送PQM API請求:")
            print(f"   URL: {self.url_pqm}")
            print(f"   設備ID: {device_id}")
            print(f"   產品條碼: {product_data['UnitIdentifier']}")
            print(f"   機種: {work_order_info['model']}")
            print(f"   已轉發數量: {forwarded_count}")
            print(f"   cycleTime: {cycle_time}秒")
            print(f"   runningTime: {running_time}秒")
            print(f"📋 PQM請求數據:")
            print(f"   {json.dumps(testdata, indent=2, ensure_ascii=False)}")
            
            response = requests.request(
                "POST", 
                self.url_pqm, 
                data=str(testdata).encode('utf-8'), 
                headers=self.headers_pqm, 
                params=self.params_pqm,
                timeout=30
            )
            
            print(f"📋 PQM API響應:")
            print(f"   HTTP狀態碼: {response.status_code}")
            print(f"   響應內容: {response}")
            
            # 檢查響應狀態
            if response.status_code == 201:
                success_msg = f"✅ PQM上傳成功"
                print(f"✅ PQM上傳成功: HTTP {response.status_code}")
                return {
                    'success': True,
                    'message': success_msg,
                    'response': response,
                    'status_code': response.status_code
                }
            else:
                error_msg = f"❌ PQM上傳失敗: HTTP {response.status_code}"
                print(f"❌ PQM上傳失敗: HTTP {response.status_code}")
                return {
                    'success': False,
                    'message': error_msg,
                    'response': response,
                    'status_code': response.status_code
                }
                
        except requests.exceptions.Timeout:
            error_msg = "❌ PQM API請求超時"
            print(f"❌ {error_msg}")
            return {
                'success': False,
                'message': error_msg,
                'response': None,
                'status_code': None
            }
        except requests.exceptions.ConnectionError as e:
            error_msg = "❌ 無法連接到PQM服務器"
            print(f"❌ {error_msg}")
            print(f"❌ 連接錯誤詳情: {str(e)}")
            return {
                'success': False,
                'message': error_msg,
                'response': None,
                'status_code': None
            }
        except Exception as e:
            error_msg = f"❌ PQM API調用異常: {str(e)}"
            print(f"❌ {error_msg}")
            print(f"❌ 異常詳情: {str(e)}")
            print(f"❌ 異常類型: {type(e).__name__}")
            return {
                'success': False,
                'message': error_msg,
                'response': None,
                'status_code': None
            }
    
    def test_connection(self):
        """測試PQM API連接"""
        try:
            # 使用測試數據
            test_device_info = {
                'deviceId': 'TEST_DEVICE_PQM'
            }
            
            test_work_order_info = {
                'model': 'TEST_MODEL_PQM'
            }
            
            test_product_data = {
                'UnitIdentifier': 'TEST_BARCODE_PQM'
            }
            
            result = self.upload_production_data(
                test_device_info,
                test_work_order_info,
                test_product_data,
                1  # 測試轉發數量
            )
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'message': f"PQM連接測試失敗: {str(e)}",
                'response': None
            }
