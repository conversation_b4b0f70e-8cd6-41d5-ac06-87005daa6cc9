{"S022054401": {"deviceId": "S022054401", "lineName": "C06", "sectionName": "S022054401", "groupName": "S022054401", "stationName": "S022054401", "createdAt": "2025-07-07T14:18:45.659908", "updatedAt": "2025-07-07T14:47:19.913828", "isActive": true, "isMonitoring": true, "productCount": 15, "forwardedCount": 4, "unforwardedCount": 11, "lastLog": "1743_1", "lastUpdateTime": "2025-07-07T14:47:19.913828", "recentLogs": [{"message": "1703_1", "timestamp": "2025-07-07T14:31:54.976385", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "1704_1", "timestamp": "2025-07-07T14:32:23.087642", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "1705_1", "timestamp": "2025-07-07T14:32:47.273184", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "1707_1", "timestamp": "2025-07-07T14:33:32.833775", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "1709_1", "timestamp": "2025-07-07T14:34:14.325376", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "1715_1", "timestamp": "2025-07-07T14:36:27.066963", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "1716_1", "timestamp": "2025-07-07T14:37:02.800368", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "1721_1", "timestamp": "2025-07-07T14:38:47.650659", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "1723_1", "timestamp": "2025-07-07T14:39:34.577429", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "1727_1", "timestamp": "2025-07-07T14:41:01.114917", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "1728_1", "timestamp": "2025-07-07T14:41:25.699342", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "1729_1", "timestamp": "2025-07-07T14:41:48.794762", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "1731_1", "timestamp": "2025-07-07T14:42:33.265543", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "1733_1", "timestamp": "2025-07-07T14:43:18.145403", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "1743_1", "timestamp": "2025-07-07T14:47:19.913828", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}], "lastFrontendUpdate": "2025-07-07T14:51:05.934585", "workOrders": [{"cavityCount": 1, "completionRate": 3.6036036036036037, "description": "", "lastMesForwardedTime": "2025-07-07T14:47:19.914831", "mesForwardedCount": 4, "modelName": "456", "startedAt": "2025-07-07T14:20:35.477107", "stationType": "無條碼過站", "status": "active", "targetQuantity": 111, "uploadType": "僅上報PQM", "workOrderNumber": "123"}], "currentWorkOrder": 0, "currentWorkOrderProgress": 4, "mesErrors": [{"message": "❌ 失敗: Unknown error", "full_response": {"success": false, "message": "❌ 失敗: FAIL", "full_response": "HTTP 200 | 原始響應: {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "card_response": "HTTP 200 | {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "request_data": {"url": "http://*************:10101/TDC/DELTA_DEAL_TEST_DATA_I", "method": "POST", "headers": {"tokenID": "894A0F0DF8494799E0530CCA940AC604", "Content-Type": "application/json"}, "params": {"sign": "0F9E43682EC46805ACA4B9576CD2CAF9"}, "body": {"factory": "DG7", "testType": "NO_ROUTE", "routingData": "DEFAULT}123}456}C06}S022054401}S022054401}S022054401}}PASS}S022054401}1}}S022054401}}}}}}", "testData": []}}, "response": {"result": "FAIL", "description": "FAIL:STATION NOT EXIST OR INVALID"}}, "card_response": {"MessageName": "CFX.Production.WorkCompleted", "Version": "1.7", "TimeStamp": "2025-07-07T06:31:54.8051056+00:00", "UniqueID": "df19485d-3273-4d7d-b082-5d1f630aaa3b", "Source": "PLC.B00.S022054401", "Target": "inline-control", "RequestID": "ce3da534-81f7-4733-b5b7-5e8b702058b4", "MessageBody": {"$type": "CFX.Production.WorkCompleted, CFX", "TransactionID": "3b5e94f3-6f08-4e4e-b393-5d163371b111", "Result": "Completed", "PrimaryIdentifier": "598", "HermesIdentifier": "string", "UnitCount": 1, "Units": [{"UnitIdentifier": "1703_1", "PositionNumber": 1, "PositionName": "string", "X": 0.0, "Y": 0.0, "Rotation": 0.0, "FlipX": false, "FlipY": false, "Status": "Pass"}], "PerformanceImpacts": []}}, "request_data": null, "timestamp": "2025-07-07T14:31:54.976385"}, {"message": "❌ 失敗: Unknown error", "full_response": {"success": false, "message": "❌ 失敗: FAIL", "full_response": "HTTP 200 | 原始響應: {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "card_response": "HTTP 200 | {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "request_data": {"url": "http://*************:10101/TDC/DELTA_DEAL_TEST_DATA_I", "method": "POST", "headers": {"tokenID": "894A0F0DF8494799E0530CCA940AC604", "Content-Type": "application/json"}, "params": {"sign": "0F9E43682EC46805ACA4B9576CD2CAF9"}, "body": {"factory": "DG7", "testType": "NO_ROUTE", "routingData": "DEFAULT}123}456}C06}S022054401}S022054401}S022054401}}PASS}S022054401}1}}S022054401}}}}}}", "testData": []}}, "response": {"result": "FAIL", "description": "FAIL:STATION NOT EXIST OR INVALID"}}, "card_response": {"MessageName": "CFX.Production.WorkCompleted", "Version": "1.7", "TimeStamp": "2025-07-07T06:32:22.8936332+00:00", "UniqueID": "762892aa-e7a8-4e58-8ba2-d47bd7381e68", "Source": "PLC.B00.S022054401", "Target": "inline-control", "RequestID": "e59498a6-b2db-4d7c-b917-7946a604c2b2", "MessageBody": {"$type": "CFX.Production.WorkCompleted, CFX", "TransactionID": "3a5c5ae9-be10-4068-a9de-c68e9bb64a17", "Result": "Completed", "PrimaryIdentifier": "599", "HermesIdentifier": "string", "UnitCount": 1, "Units": [{"UnitIdentifier": "1704_1", "PositionNumber": 1, "PositionName": "string", "X": 0.0, "Y": 0.0, "Rotation": 0.0, "FlipX": false, "FlipY": false, "Status": "Pass"}], "PerformanceImpacts": []}}, "request_data": null, "timestamp": "2025-07-07T14:32:23.092959"}, {"message": "❌ 失敗: Unknown error", "full_response": {"success": false, "message": "❌ 失敗: FAIL", "full_response": "HTTP 200 | 原始響應: {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "card_response": "HTTP 200 | {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "request_data": {"url": "http://*************:10101/TDC/DELTA_DEAL_TEST_DATA_I", "method": "POST", "headers": {"tokenID": "894A0F0DF8494799E0530CCA940AC604", "Content-Type": "application/json"}, "params": {"sign": "0F9E43682EC46805ACA4B9576CD2CAF9"}, "body": {"factory": "DG7", "testType": "NO_ROUTE", "routingData": "DEFAULT}123}456}C06}S022054401}S022054401}S022054401}}PASS}S022054401}1}}S022054401}}}}}}", "testData": []}}, "response": {"result": "FAIL", "description": "FAIL:STATION NOT EXIST OR INVALID"}}, "card_response": {"MessageName": "CFX.Production.WorkCompleted", "Version": "1.7", "TimeStamp": "2025-07-07T06:32:47.1110914+00:00", "UniqueID": "33b6e5cd-ac0c-4e1e-90f4-b7d08fe4143e", "Source": "PLC.B00.S022054401", "Target": "inline-control", "RequestID": "8dd758c2-5da2-4051-b100-41abbb4adc97", "MessageBody": {"$type": "CFX.Production.WorkCompleted, CFX", "TransactionID": "********-54d1-4add-8656-a9ed866db0a8", "Result": "Completed", "PrimaryIdentifier": "600", "HermesIdentifier": "string", "UnitCount": 1, "Units": [{"UnitIdentifier": "1705_1", "PositionNumber": 1, "PositionName": "string", "X": 0.0, "Y": 0.0, "Rotation": 0.0, "FlipX": false, "FlipY": false, "Status": "Pass"}], "PerformanceImpacts": []}}, "request_data": null, "timestamp": "2025-07-07T14:32:47.280230"}, {"message": "❌ 失敗: Unknown error", "full_response": {"success": false, "message": "❌ 失敗: FAIL", "full_response": "HTTP 200 | 原始響應: {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "card_response": "HTTP 200 | {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "request_data": {"url": "http://*************:10101/TDC/DELTA_DEAL_TEST_DATA_I", "method": "POST", "headers": {"tokenID": "894A0F0DF8494799E0530CCA940AC604", "Content-Type": "application/json"}, "params": {"sign": "0F9E43682EC46805ACA4B9576CD2CAF9"}, "body": {"factory": "DG7", "testType": "NO_ROUTE", "routingData": "DEFAULT}123}456}C06}S022054401}S022054401}S022054401}}PASS}S022054401}1}}S022054401}}}}}}", "testData": []}}, "response": {"result": "FAIL", "description": "FAIL:STATION NOT EXIST OR INVALID"}}, "card_response": {"MessageName": "CFX.Production.WorkCompleted", "Version": "1.7", "TimeStamp": "2025-07-07T06:33:32.6471617+00:00", "UniqueID": "254c9cc5-37d6-41f5-ba70-2158e8a8744f", "Source": "PLC.B00.S022054401", "Target": "inline-control", "RequestID": "00d8d8a1-84e8-4cbe-b36a-4ca184b9f733", "MessageBody": {"$type": "CFX.Production.WorkCompleted, CFX", "TransactionID": "4dd8c84d-56e4-4c1c-b29c-387697f65edf", "Result": "Completed", "PrimaryIdentifier": "602", "HermesIdentifier": "string", "UnitCount": 1, "Units": [{"UnitIdentifier": "1707_1", "PositionNumber": 1, "PositionName": "string", "X": 0.0, "Y": 0.0, "Rotation": 0.0, "FlipX": false, "FlipY": false, "Status": "Pass"}], "PerformanceImpacts": []}}, "request_data": null, "timestamp": "2025-07-07T14:33:32.840745"}, {"message": "❌ 失敗: Unknown error", "full_response": {"success": false, "message": "❌ 失敗: FAIL", "full_response": "HTTP 200 | 原始響應: {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "card_response": "HTTP 200 | {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "request_data": {"url": "http://*************:10101/TDC/DELTA_DEAL_TEST_DATA_I", "method": "POST", "headers": {"tokenID": "894A0F0DF8494799E0530CCA940AC604", "Content-Type": "application/json"}, "params": {"sign": "0F9E43682EC46805ACA4B9576CD2CAF9"}, "body": {"factory": "DG7", "testType": "NO_ROUTE", "routingData": "DEFAULT}123}456}C06}S022054401}S022054401}S022054401}}PASS}S022054401}1}}S022054401}}}}}}", "testData": []}}, "response": {"result": "FAIL", "description": "FAIL:STATION NOT EXIST OR INVALID"}}, "card_response": {"MessageName": "CFX.Production.WorkCompleted", "Version": "1.7", "TimeStamp": "2025-07-07T06:34:14.1306354+00:00", "UniqueID": "363dda72-ffd9-4304-b1c1-e3942813509a", "Source": "PLC.B00.S022054401", "Target": "inline-control", "RequestID": "38b85cd3-6609-47cf-b1c1-c5d67ca1f4be", "MessageBody": {"$type": "CFX.Production.WorkCompleted, CFX", "TransactionID": "697365e8-b1fa-4d10-8e71-453aae2471ff", "Result": "Completed", "PrimaryIdentifier": "604", "HermesIdentifier": "string", "UnitCount": 1, "Units": [{"UnitIdentifier": "1709_1", "PositionNumber": 1, "PositionName": "string", "X": 0.0, "Y": 0.0, "Rotation": 0.0, "FlipX": false, "FlipY": false, "Status": "Pass"}], "PerformanceImpacts": []}}, "request_data": null, "timestamp": "2025-07-07T14:34:14.332251"}, {"message": "❌ 失敗: Unknown error", "full_response": {"success": false, "message": "❌ 失敗: FAIL", "full_response": "HTTP 200 | 原始響應: {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "card_response": "HTTP 200 | {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "request_data": {"url": "http://*************:10101/TDC/DELTA_DEAL_TEST_DATA_I", "method": "POST", "headers": {"tokenID": "894A0F0DF8494799E0530CCA940AC604", "Content-Type": "application/json"}, "params": {"sign": "0F9E43682EC46805ACA4B9576CD2CAF9"}, "body": {"factory": "DG7", "testType": "NO_ROUTE", "routingData": "DEFAULT}123}456}C06}S022054401}S022054401}S022054401}}PASS}S022054401}1}}S022054401}}}}}}", "testData": []}}, "response": {"result": "FAIL", "description": "FAIL:STATION NOT EXIST OR INVALID"}}, "card_response": {"MessageName": "CFX.Production.WorkCompleted", "Version": "1.7", "TimeStamp": "2025-07-07T06:36:26.9438056+00:00", "UniqueID": "4301a6ed-3d46-4f1b-8975-60f6b6167435", "Source": "PLC.B00.S022054401", "Target": "inline-control", "RequestID": "2015462c-c446-4dc1-8563-3a76685e1b81", "MessageBody": {"$type": "CFX.Production.WorkCompleted, CFX", "TransactionID": "fddad053-f309-46dd-9a79-3a3600cb7898", "Result": "Completed", "PrimaryIdentifier": "610", "HermesIdentifier": "string", "UnitCount": 1, "Units": [{"UnitIdentifier": "1715_1", "PositionNumber": 1, "PositionName": "string", "X": 0.0, "Y": 0.0, "Rotation": 0.0, "FlipX": false, "FlipY": false, "Status": "Pass"}], "PerformanceImpacts": []}}, "request_data": null, "timestamp": "2025-07-07T14:36:27.084249"}, {"message": "❌ 失敗: Unknown error", "full_response": {"success": false, "message": "❌ 失敗: FAIL", "full_response": "HTTP 200 | 原始響應: {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "card_response": "HTTP 200 | {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "request_data": {"url": "http://*************:10101/TDC/DELTA_DEAL_TEST_DATA_I", "method": "POST", "headers": {"tokenID": "894A0F0DF8494799E0530CCA940AC604", "Content-Type": "application/json"}, "params": {"sign": "0F9E43682EC46805ACA4B9576CD2CAF9"}, "body": {"factory": "DG7", "testType": "NO_ROUTE", "routingData": "DEFAULT}123}456}C06}S022054401}S022054401}S022054401}}PASS}S022054401}1}}S022054401}}}}}}", "testData": []}}, "response": {"result": "FAIL", "description": "FAIL:STATION NOT EXIST OR INVALID"}}, "card_response": {"MessageName": "CFX.Production.WorkCompleted", "Version": "1.7", "TimeStamp": "2025-07-07T06:37:02.6266707+00:00", "UniqueID": "b3578ba6-e530-4be1-991a-0e79b28b1aa8", "Source": "PLC.B00.S022054401", "Target": "inline-control", "RequestID": "bfbe3886-7f8a-490a-90a4-982f48bed2e0", "MessageBody": {"$type": "CFX.Production.WorkCompleted, CFX", "TransactionID": "c07abca9-d33e-4c2d-a280-bcb664ca0f04", "Result": "Completed", "PrimaryIdentifier": "612", "HermesIdentifier": "string", "UnitCount": 1, "Units": [{"UnitIdentifier": "1716_1", "PositionNumber": 1, "PositionName": "string", "X": 0.0, "Y": 0.0, "Rotation": 0.0, "FlipX": false, "FlipY": false, "Status": "Pass"}], "PerformanceImpacts": []}}, "request_data": null, "timestamp": "2025-07-07T14:37:02.824889"}, {"message": "❌ 失敗: Unknown error", "full_response": {"success": false, "message": "❌ 失敗: FAIL", "full_response": "HTTP 200 | 原始響應: {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "card_response": "HTTP 200 | {\"result\":\"FAIL\",\"description\":\"FAIL:STATION NOT EXIST OR INVALID\"}", "request_data": {"url": "http://*************:10101/TDC/DELTA_DEAL_TEST_DATA_I", "method": "POST", "headers": {"tokenID": "894A0F0DF8494799E0530CCA940AC604", "Content-Type": "application/json"}, "params": {"sign": "0F9E43682EC46805ACA4B9576CD2CAF9"}, "body": {"factory": "DG7", "testType": "NO_ROUTE", "routingData": "DEFAULT}123}456}C06}S022054401}S022054401}S022054401}}PASS}S022054401}1}}S022054401}}}}}}", "testData": []}}, "response": {"result": "FAIL", "description": "FAIL:STATION NOT EXIST OR INVALID"}}, "card_response": {"MessageName": "CFX.Production.WorkCompleted", "Version": "1.7", "TimeStamp": "2025-07-07T06:38:47.4699208+00:00", "UniqueID": "82fe23fe-f2bc-48b1-b3be-d4dc14d6b3e4", "Source": "PLC.B00.S022054401", "Target": "inline-control", "RequestID": "31c0a821-9c4e-40f5-8a11-b7ecf690292c", "MessageBody": {"$type": "CFX.Production.WorkCompleted, CFX", "TransactionID": "f86d5361-38ca-4df1-9fff-ec77b5b60c31", "Result": "Completed", "PrimaryIdentifier": "616", "HermesIdentifier": "string", "UnitCount": 1, "Units": [{"UnitIdentifier": "1721_1", "PositionNumber": 1, "PositionName": "string", "X": 0.0, "Y": 0.0, "Rotation": 0.0, "FlipX": false, "FlipY": false, "Status": "Pass"}], "PerformanceImpacts": []}}, "request_data": null, "timestamp": "2025-07-07T14:38:47.662797"}, {"message": "✅ PQM上傳成功", "full_response": "✅ PQM上傳成功", "card_response": "✅ PQM上傳成功", "request_data": null, "timestamp": "2025-07-07T14:39:34.568752"}, {"message": "✅ PQM上傳成功", "full_response": "✅ PQM上傳成功", "card_response": "✅ PQM上傳成功", "request_data": null, "timestamp": "2025-07-07T14:41:01.106773"}, {"message": "✅ PQM上傳成功", "full_response": "✅ PQM上傳成功", "card_response": "✅ PQM上傳成功", "request_data": null, "timestamp": "2025-07-07T14:41:25.693226"}], "mesStartTime": "2025-07-07T14:49:16.777279", "lastProductTime": null, "periodData": {"logs": [], "totalCount": 0, "forwardedCount": 0, "unforwardedCount": 0, "sinceTime": "2025-07-07T14:51:00.950491"}, "displayWorkOrder": {"workOrderNumber": "123", "modelName": "456", "isActive": true}, "lastCompletedWorkOrder": null, "recentWorkOrders": [{"workOrderNumber": "123", "modelName": "456", "targetQuantity": 111, "mesForwardedCount": 4, "completionRate": 3.6036036036036037, "lastMesForwardedTime": "2025-07-07T14:47:19.914831", "status": "active"}]}}