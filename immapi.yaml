---
apiVersion: "v1"
kind: "Service"
metadata:
  labels:
    app: "immapi-1"
  name: "immapi-1"
  namespace: "app-immapi-latest"
spec:
  ipFamilies:
  - "IPv4"
  ipFamilyPolicy: "SingleStack"
  ports:
  - name: "tcp-30071"
    nodePort: 30071
    port: 30071
    protocol: "TCP"
    targetPort: "tcp-30071"
  - name: "tcp-30171"
    nodePort: 30171
    port: 30171
    protocol: "TCP"
    targetPort: "tcp-30171"
  selector:
    app: "immapi-1"
  sessionAffinity: "None"
  type: "NodePort"
---
apiVersion: "v1"
kind: "Service"
metadata:
  labels:
    app: "immapi-2"
  name: "immapi-2"
  namespace: "app-immapi-latest"
spec:
  ipFamilies:
  - "IPv4"
  ipFamilyPolicy: "SingleStack"
  ports:
  - name: "tcp-30072"
    nodePort: 30072
    port: 30072
    protocol: "TCP"
    targetPort: "tcp-30072"
  - name: "tcp-30172"
    nodePort: 30172
    port: 30172
    protocol: "TCP"
    targetPort: "tcp-30172"
  selector:
    app: "immapi-2"
  sessionAffinity: "None"
  type: "NodePort"
---
apiVersion: "v1"
kind: "Service"
metadata:
  labels:
    app: "immapi-3"
  name: "immapi-3"
  namespace: "app-immapi-latest"
spec:
  ipFamilies:
  - "IPv4"
  ipFamilyPolicy: "SingleStack"
  ports:
  - name: "tcp-30073"
    nodePort: 30073
    port: 30073
    protocol: "TCP"
    targetPort: "tcp-30073"
  - name: "tcp-30173"
    nodePort: 30173
    port: 30173
    protocol: "TCP"
    targetPort: "tcp-30173"
  selector:
    app: "immapi-3"
  sessionAffinity: "None"
  type: "NodePort"
---
apiVersion: "v1"
kind: "Service"
metadata:
  labels:
    app: "immapi-4"
  name: "immapi-4"
  namespace: "app-immapi-latest"
spec:
  ipFamilies:
  - "IPv4"
  ipFamilyPolicy: "SingleStack"
  ports:
  - name: "tcp-30074"
    nodePort: 30074
    port: 30074
    protocol: "TCP"
    targetPort: "tcp-30074"
  - name: "tcp-30174"
    nodePort: 30174
    port: 30174
    protocol: "TCP"
    targetPort: "tcp-30174"
  selector:
    app: "immapi-4"
  sessionAffinity: "None"
  type: "NodePort"
---
apiVersion: "v1"
kind: "Service"
metadata:
  labels:
    app: "immapi-5"
  name: "immapi-5"
  namespace: "app-immapi-latest"
spec:
  ipFamilies:
  - "IPv4"
  ipFamilyPolicy: "SingleStack"
  ports:
  - name: "tcp-30075"
    nodePort: 30075
    port: 30075
    protocol: "TCP"
    targetPort: "tcp-30075"
  - name: "tcp-30175"
    nodePort: 30175
    port: 30175
    protocol: "TCP"
    targetPort: "tcp-30175"
  selector:
    app: "immapi-5"
  sessionAffinity: "None"
  type: "NodePort"
---
apiVersion: "v1"
kind: "Service"
metadata:
  labels:
    app: "immapi-6"
  name: "immapi-6"
  namespace: "app-immapi-latest"
spec:
  ipFamilies:
  - "IPv4"
  ipFamilyPolicy: "SingleStack"
  ports:
  - name: "tcp-30076"
    nodePort: 30076
    port: 30076
    protocol: "TCP"
    targetPort: "tcp-30076"
  - name: "tcp-30176"
    nodePort: 30176
    port: 30176
    protocol: "TCP"
    targetPort: "tcp-30176"
  selector:
    app: "immapi-6"
  sessionAffinity: "None"
  type: "NodePort"
