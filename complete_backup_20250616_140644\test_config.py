#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試配置文件
用於控制測試模式和性能測試
"""

import os
import json
from test_data_generator import TestDataGenerator

class TestConfig:
    def __init__(self):
        self.test_mode = os.getenv('TEST_MODE', 'false').lower() == 'true'
        self.test_device_count = int(os.getenv('TEST_DEVICE_COUNT', '50'))
        self.enable_fake_data = os.getenv('ENABLE_FAKE_DATA', 'false').lower() == 'true'
        self.enable_fake_mes = os.getenv('ENABLE_FAKE_MES', 'false').lower() == 'true'
        self.fake_data_interval = int(os.getenv('FAKE_DATA_INTERVAL', '5'))  # 秒
        
    def is_test_mode(self):
        """檢查是否為測試模式"""
        return self.test_mode
    
    def get_test_device_count(self):
        """獲取測試設備數量"""
        return self.test_device_count
    
    def should_use_fake_data(self):
        """是否使用模擬數據"""
        return self.enable_fake_data
    
    def should_use_fake_mes(self):
        """是否使用模擬MES"""
        return self.enable_fake_mes
    
    def get_fake_data_interval(self):
        """獲取模擬數據間隔"""
        return self.fake_data_interval

# 全局測試配置實例
test_config = TestConfig()

def setup_test_environment(device_count=50):
    """設置測試環境"""
    print(f"🧪 設置測試環境 - {device_count} 個設備")
    
    # 生成測試數據
    generator = TestDataGenerator()
    test_filename = f"test_devices_{device_count}.json"
    
    # 檢查是否已存在測試數據
    if os.path.exists(test_filename):
        print(f"📁 發現現有測試數據: {test_filename}")
        devices = generator.load_test_devices(test_filename)
    else:
        print(f"🔄 生成新的測試數據: {test_filename}")
        devices = generator.save_test_devices(device_count, test_filename)
    
    return devices

def load_test_devices_to_system(device_manager, device_count=50):
    """將測試設備載入到系統中"""
    try:
        # 生成或載入測試數據
        devices = setup_test_environment(device_count)
        
        # 清空現有設備
        device_manager.devices = {}
        
        # 載入測試設備
        device_manager.devices = devices
        
        # 保存到devices.json
        device_manager.save_devices()
        
        print(f"✅ 已載入 {len(devices)} 個測試設備到系統")
        return True
        
    except Exception as e:
        print(f"❌ 載入測試設備失敗: {e}")
        return False

def create_performance_test_script():
    """創建性能測試腳本"""
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能測試腳本
"""

import time
import psutil
import requests
import threading
from datetime import datetime

class PerformanceMonitor:
    def __init__(self):
        self.start_time = None
        self.memory_usage = []
        self.cpu_usage = []
        self.response_times = []
        self.monitoring = False
        
    def start_monitoring(self):
        """開始性能監控"""
        self.start_time = time.time()
        self.monitoring = True
        
        # 啟動監控線程
        threading.Thread(target=self._monitor_system, daemon=True).start()
        print("🔍 性能監控已啟動")
        
    def stop_monitoring(self):
        """停止性能監控"""
        self.monitoring = False
        print("⏹️ 性能監控已停止")
        
    def _monitor_system(self):
        """監控系統資源"""
        while self.monitoring:
            # 記錄內存使用
            memory = psutil.virtual_memory()
            self.memory_usage.append({
                'timestamp': time.time(),
                'used_mb': memory.used / 1024 / 1024,
                'percent': memory.percent
            })
            
            # 記錄CPU使用
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_usage.append({
                'timestamp': time.time(),
                'percent': cpu_percent
            })
            
            time.sleep(1)
    
    def test_api_response_time(self, url, count=10):
        """測試API響應時間"""
        print(f"🚀 測試API響應時間: {url}")
        
        for i in range(count):
            start_time = time.time()
            try:
                response = requests.get(url, timeout=10)
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000  # 毫秒
                self.response_times.append({
                    'request_id': i + 1,
                    'response_time_ms': response_time,
                    'status_code': response.status_code,
                    'timestamp': datetime.now().isoformat()
                })
                
                print(f"  請求 {i+1}: {response_time:.2f}ms (狀態: {response.status_code})")
                
            except Exception as e:
                print(f"  請求 {i+1}: 失敗 - {e}")
                
            time.sleep(0.5)  # 間隔0.5秒
    
    def generate_report(self):
        """生成性能報告"""
        if not self.memory_usage or not self.cpu_usage:
            print("❌ 沒有性能數據可生成報告")
            return
            
        # 計算統計數據
        avg_memory = sum(m['used_mb'] for m in self.memory_usage) / len(self.memory_usage)
        max_memory = max(m['used_mb'] for m in self.memory_usage)
        avg_cpu = sum(c['percent'] for c in self.cpu_usage) / len(self.cpu_usage)
        max_cpu = max(c['percent'] for c in self.cpu_usage)
        
        # API響應時間統計
        if self.response_times:
            avg_response = sum(r['response_time_ms'] for r in self.response_times) / len(self.response_times)
            max_response = max(r['response_time_ms'] for r in self.response_times)
            min_response = min(r['response_time_ms'] for r in self.response_times)
        else:
            avg_response = max_response = min_response = 0
        
        # 生成報告
        report = f"""
📊 性能測試報告
{'='*50}
測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
測試持續時間: {time.time() - self.start_time:.2f} 秒

💾 內存使用:
  平均: {avg_memory:.2f} MB
  峰值: {max_memory:.2f} MB

🖥️ CPU使用:
  平均: {avg_cpu:.2f}%
  峰值: {max_cpu:.2f}%

🌐 API響應時間:
  平均: {avg_response:.2f} ms
  最快: {min_response:.2f} ms
  最慢: {max_response:.2f} ms
  總請求數: {len(self.response_times)}

{'='*50}
        """
        
        print(report)
        
        # 保存到文件
        with open(f'performance_report_{int(time.time())}.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        
        return report

def run_performance_test(device_count=50, test_duration=60):
    """運行性能測試"""
    print(f"🧪 開始性能測試 - {device_count} 個設備，持續 {test_duration} 秒")
    
    monitor = PerformanceMonitor()
    monitor.start_monitoring()
    
    # 測試API響應時間
    base_url = "http://localhost:5000"
    apis_to_test = [
        f"{base_url}/api/devices",
        f"{base_url}/api/kafka/status",
        f"{base_url}/api/rabbitmq/status"
    ]
    
    for api in apis_to_test:
        monitor.test_api_response_time(api, count=5)
    
    # 等待指定時間
    print(f"⏳ 等待 {test_duration} 秒收集性能數據...")
    time.sleep(test_duration)
    
    monitor.stop_monitoring()
    monitor.generate_report()

if __name__ == "__main__":
    run_performance_test()
'''
    
    with open('performance_test.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ 性能測試腳本已創建: performance_test.py")

if __name__ == "__main__":
    # 創建性能測試腳本
    create_performance_test_script()
    
    # 生成測試數據
    generator = TestDataGenerator()
    for count in [40, 50, 60, 70, 100]:
        generator.save_test_devices(count)
