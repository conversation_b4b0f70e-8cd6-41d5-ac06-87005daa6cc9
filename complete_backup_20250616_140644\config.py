# Kafka配置 - 默認配置
DEFAULT_KAFKA_CONFIG = {
    'bootstrap_servers': '10.148.208.112:9092',
    'group_id': 'mes_forwarder_group',
    'factory': 'CZ',
    'mfg_plant_code': 'MAG',
    'message_type': 'DEVICE_CFX',
    'message_name': 'CFX.Production.UnitsDeparted',
    'auto_offset_reset': 'latest',
    'enable_auto_commit': True
}

# 當前Kafka配置（可通過界面修改）
KAFKA_CONFIG = DEFAULT_KAFKA_CONFIG.copy()

# Kafka配置文件路徑
KAFKA_CONFIG_FILE = 'kafka_config.json'

# 測試數據配置
TEST_DATA = {
    'sample_kafka_message': {
        'Data': {
            'Meta': {
                'MessageName': 'CFX.Production.UnitsDeparted',
                'DeviceID': 'SC21100803',
                'LineID': 'MAG-H71',
                'MfgPlantCode': 'MAG',
                'Factory': 'CZ',
                'Source': 'CFX.A00.SC21100803'
            },
            'ProcessedTime': '2025-06-10T01:37:14.275641+00:00',
            'RawData': {
                'MessageName': 'CFX.Production.UnitsDeparted',
                'Version': '1.7',
                'TimeStamp': '2025-06-10T01:37:14.2737209+00:00',
                'UniqueID': 'c9eac6da-8170-4642-8ba1-48b175816db4',
                'Source': 'CFX.A00.SC21100803',
                'Target': 'inline-control',
                'RequestID': 'bcf384ba-915f-4076-a76c-af339dc0153e',
                'MessageBody': {
                    '$type': 'CFX.Production.UnitsDeparted, CFX',
                    'PrimaryIdentifier': 'string',
                    'HermesIdentifier': 'string',
                    'UnitCount': 1,
                    'Units': [
                        {
                            'UnitIdentifier': '5490',
                            'PositionNumber': 1,
                            'PositionName': '5490_1',
                            'X': 0.0,
                            'Y': 0.0,
                            'Rotation': 0.0,
                            'FlipX': False,
                            'FlipY': False,
                            'Status': 'Pass'
                        }
                    ],
                    'Lane': 1
                }
            }
        },
        'MessageID': '2489573a-a338-4ead-86cb-7bec68c08994',
        'TransactionID': None,
        'MessageType': 'DEVICE_CFX',
        'MessageVersion': '1.0',
        'MessageTime': '2025-06-10T01:37:14.279914+00:00',
        'Sender': 'CZ-SIE',
        'Transmission': [
            {
                'Node': 'CZ-H71-Node',
                'SendTime': '2025-06-10T01:37:14.275715+00:00'
            },
            {
                'Node': 'CZ-SIE',
                'SendTime': '2025-06-10T01:37:14.279881+00:00'
            }
        ]
    }
}
