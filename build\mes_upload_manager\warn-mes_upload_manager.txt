
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named importlib.metadata - imported by importlib (delayed, conditional, optional), click.decorators (delayed, conditional, optional), flask.cli (delayed, conditional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional)
missing module named grp - imported by pathlib (delayed), shutil (optional), tarfile (optional)
missing module named pwd - imported by posixpath (delayed, conditional), pathlib (delayed, conditional, optional), shutil (optional), tarfile (optional), netrc (delayed, conditional), getpass (delayed), http.server (delayed, optional), webbrowser (delayed)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named org - imported by pickle (optional)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named pyimod02_importers - imported by e:\vscode\tomesmo\.venv\lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named _uuid - imported by uuid (optional)
missing module named netbios - imported by uuid (delayed)
missing module named win32wnet - imported by uuid (delayed)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named _dummy_threading - imported by dummy_threading (optional)
missing module named zstandard - imported by urllib3.response (optional), urllib3.util.request (optional), kafka.codec (optional)
missing module named brotli - imported by urllib3.response (optional), urllib3.util.request (optional)
missing module named brotlicffi - imported by urllib3.response (optional), urllib3.util.request (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named cryptography - imported by werkzeug.serving (delayed, conditional, optional), flask.cli (delayed, conditional, optional), urllib3.contrib.pyopenssl (top-level), requests (conditional, optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named 'cryptography.x509' - imported by werkzeug.serving (delayed, conditional, optional), urllib3.contrib.pyopenssl (delayed, optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named chardet - imported by requests.compat (optional), requests (optional), requests.packages (optional)
missing module named urllib3_secure_extra - imported by urllib3 (optional)
missing module named StringIO - imported by kafka.vendor.six (conditional), pika.compat (conditional)
missing module named urlparse - imported by pika.compat (conditional)
missing module named vms_lib - imported by platform (delayed, conditional, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named 'gssapi.raw' - imported by kafka.conn (optional)
missing module named gssapi - imported by kafka.conn (optional)
missing module named crc32c - imported by kafka.record.util (optional)
missing module named xxhash - imported by kafka.codec (optional)
missing module named lz4framed - imported by kafka.codec (optional)
missing module named lz4f - imported by kafka.codec (optional)
missing module named lz4 - imported by kafka.codec (optional)
missing module named snappy - imported by kafka.codec (optional)
missing module named 'kafka.vendor.six.moves' - imported by kafka.codec (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional), tty (top-level), werkzeug._reloader (delayed, optional), click._termui_impl (conditional)
missing module named posix - imported by os (conditional, optional)
missing module named resource - imported by posix (top-level)
missing module named '_typeshed.wsgi' - imported by werkzeug._internal (conditional), werkzeug.exceptions (conditional), werkzeug.http (conditional), werkzeug.wsgi (conditional), werkzeug.utils (conditional), werkzeug.wrappers.response (conditional), werkzeug.test (conditional), werkzeug.formparser (conditional), werkzeug.wrappers.request (conditional), werkzeug.serving (conditional), werkzeug.debug (conditional), werkzeug.middleware.shared_data (conditional), werkzeug.local (conditional), werkzeug.routing.exceptions (conditional), werkzeug.routing.map (conditional), flask.typing (conditional)
missing module named _typeshed - imported by werkzeug._internal (conditional)
missing module named 'watchdog.events' - imported by werkzeug._reloader (delayed)
missing module named watchdog - imported by werkzeug._reloader (delayed)
missing module named readline - imported by code (delayed, conditional, optional), flask.cli (delayed, conditional, optional), rlcompleter (optional)
missing module named 'cryptography.hazmat' - imported by werkzeug.serving (delayed, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named blinker - imported by flask.signals (optional)
missing module named asgiref - imported by flask.app (delayed, optional)
missing module named dotenv - imported by flask.cli (delayed, optional)
