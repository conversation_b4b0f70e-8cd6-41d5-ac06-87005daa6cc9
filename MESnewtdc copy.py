# import random
import requests
import hashlib
import json
# import time

#通用变量
# global tokenID,secretkey,headers
secretkey = '894A0F0DF84A4799E0530CCA940AC604'
tokenID = '894A0F0DF8494799E0530CCA940AC604'
headers = {
    'tokenID': "894A0F0DF8494799E0530CCA940AC604",
    'Content-Type': "application/json"
    }

#routing变量
# global url_routing,params_routing,data_routing,testdata
testdata = []
url_routing = "http://10.148.192.37:10101/TDC/DELTA_DEAL_TEST_DATA_I"
params_routing = {
    'sign': ""
    }
data_routing =  {
    'factory': "",
    'testType': "",
    'routingData': "",
    'testData': testdata
    }


#0. md5加密使用
def keymd5(src):
    str_md5 = hashlib.md5(src.encode("utf-8")).hexdigest()
    str_md5 = str_md5.upper()
    return str_md5

#1. 途程更新及测试数据上传接口
def routing(url_routing,data_routing,headers_routing,params_routing):
    #将body的数据转换为字符串格式
    data_str = json.dumps(data_routing, sort_keys=False)
    #字符串拼接md5加密
    src = secretkey + data_str
    md5 = keymd5(src)
    params_routing['sign'] = md5
    #发送数据并接收返回数据
    receive = requests.request("POST", url_routing, data=data_str, headers=headers_routing, params=params_routing)
    #str转换为字典列表
    receive = eval(receive.text)
    print(receive)
    
    return receive
# # #1.1 Demo
testdata = [["D1(Delay)","2025-06-16 16:25:06","O","Delay Time","","*","*",0,"3000","","2025-06-16 16:25:09",0],["D1(Delay)","2025-06-16 16:25:09","O","Delay Time","","*","*",0,"3000","","2025-06-16 16:25:12",0],["D1(Delay)","2025-06-16 16:25:12","O","Delay Time","","*","*",0,"3000","","2025-06-16 16:25:15",0],["D1(Delay)","2025-06-16 16:25:16","O","Delay Time","","*","*",0,"3000","","2025-06-16 16:25:19",0],["D1(Delay)","2025-06-16 16:25:19","O","Delay Time","","*","*",0,"3000","","2025-06-16 16:25:22",0]]
data_routing['factory'] = 'DG7'
data_routing['testType'] = 'NO_ROUTE'
sncode = '202506174564567897987'
mo = '***********'
model = 'DHS-B093082-00A'
line = 'T212'
section = 'FINAL-TEST'
group = 'THERMAL-TEST'
station = 'THERMAL-TEST'
usr_id = 'NA'
Qty = '1'
DeviceID = 'NA'
data_routing['routingData'] = str(
                                sncode+'}'
                                +mo+'}'
                                +model+'}'
                                +line+'}'
                                +section+'}'
                                +group+'}'
                                +station+'}'
                                +''+'}'
                                +'PASS'+'}'
                                +usr_id+'}'
                                +Qty+'}'
                                +''+'}'
                                +DeviceID+'}'
                                +''+'}'
                                +''+'}'
                                +''+'}'
                                +''+'}'
                                +''+'}'
                                +''+'}'
                                )
data_routing['testData'] = testdata

routing_post = routing(url_routing, data_routing, headers, params_routing)
if routing_post['result'].find('OK') != -1:
    print('上传成功')
else:
    print('上传失败')
