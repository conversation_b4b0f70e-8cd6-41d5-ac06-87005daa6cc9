@echo off
title MES Upload Manager v0.0.1-beta-1
echo ========================================
echo    MES Upload Manager v0.0.1-beta-1
echo ========================================
echo.
echo Starting system...
echo.

REM Start the main MES Upload Manager
start /b "MES Upload Manager" "MES_Upload_Manager.exe"

REM Wait for system to initialize
echo Waiting for system to start...
ping 127.0.0.1 -n 5 > nul

REM Open browser - try only one method at a time
echo Opening web interface...

REM Try default browser first
start http://localhost:5000

echo.
echo ========================================
echo   SYSTEM IS RUNNING
echo ========================================
echo.
echo Web Interface: http://localhost:5000
echo.
echo If browser didn't open automatically:
echo Please manually open your browser and visit the above URL
echo.
echo Quick Start:
echo 1. Configure EAP or SIE settings
echo 2. Add production devices
echo 3. Set up work orders
echo 4. Start monitoring
echo.
echo Press any key to STOP the system
echo ========================================
pause > nul

echo.
echo Stopping system...
taskkill /f /im "MES_Upload_Manager.exe" > nul 2>&1
echo System stopped.
echo.
pause
