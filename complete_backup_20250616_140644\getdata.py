from kafka import KafkaConsumer
import json
import time
import datetime

def consume_kafka_messages(bootstrap_servers, topic_name, group_id=None):
    """
    从Kafka消费数据
    
    参数:
    bootstrap_servers (str): Kafka服务器地址，格式为'host:port'
    topic_name (str): 要消费的主题名称
    group_id (str, optional): 消费者组ID
    
    返回:
    None
    """
    # 记录程序启动时的时间戳（毫秒）
    start_time_ms = int(time.time() * 1000)
    print(f"程序启动时间: {datetime.datetime.fromtimestamp(start_time_ms/1000).strftime('%Y-%m-%d %H:%M:%S.%f')}")
    
    # 创建Kafka消费者
    consumer = KafkaConsumer(
        bootstrap_servers=bootstrap_servers,
        group_id=group_id,             # 消费者组ID
        value_deserializer=lambda x: json.loads(x.decode('utf-8'))  # 假设消息是JSON格式
    )
    
    # 订阅主题
    consumer.subscribe([topic_name])
    
    # 等待分配分区
    while not consumer.assignment():
        consumer.poll(0)
        time.sleep(0.1)
    
    # 获取分配的分区
    partitions = consumer.assignment()
    
    # 为每个分区创建一个时间戳-偏移量映射
    time_map = {}
    for partition in partitions:
        time_map[partition] = start_time_ms
    
    # 获取每个分区在给定时间戳的偏移量
    offsets_for_times = consumer.offsets_for_times(time_map)
    
    # 对每个分区设置从指定时间开始的偏移量
    for partition, offset_and_timestamp in offsets_for_times.items():
        if offset_and_timestamp is None:
            # 如果在给定时间后没有消息，设置为最新偏移量
            consumer.seek_to_end(partition)
        else:
            # 设置为给定时间的偏移量
            consumer.seek(partition, offset_and_timestamp.offset)
    
    print(f"开始从主题 {topic_name} 消费消息...")
    print(f"注意: 只会消费从 {datetime.datetime.fromtimestamp(start_time_ms/1000).strftime('%Y-%m-%d %H:%M:%S.%f')} 之后的消息")
    
    # 循环接收消息
    try:
        for message in consumer:
            msg_time = datetime.datetime.fromtimestamp(message.timestamp/1000).strftime('%Y-%m-%d %H:%M:%S.%f')
            print(f"消息时间: {msg_time}")
            print(f"接收到消息: {message.value}")
            print(f"主题: {message.topic}, 分区: {message.partition}, 偏移量: {message.offset}")
            print("="*50)
    except KeyboardInterrupt:
        print("消费者已停止")
    finally:
        consumer.close()
        print("Kafka消费者已关闭")

if __name__ == "__main__":
    # Kafka服务器配置
    bootstrap_servers = '10.148.208.112:9092'
    
    # 根据命名规则构建主题名称
    # 命名规则: EAP.{Factory}.{MfgPlantCode}.{LINE}.{MessageType}.{MessageName}
    factory = "CZ"
    mfg_plant_code = "MAG"
    line = "MAG-H71"
    message_type = "DEVICE_CFX"
    message_name = "CFX.Production.UnitsDeparted"
    
    topic_name = f"EAP.{factory}.{mfg_plant_code}.{line}.{message_type}.{message_name}"
    print(f"使用主题: {topic_name}")
    
    group_id = 'python_consumer_group'  # 消费者组ID
    
    # 开始消费消息
    consume_kafka_messages(bootstrap_servers, topic_name, group_id)
