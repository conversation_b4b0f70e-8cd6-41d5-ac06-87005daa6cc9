#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試消息解析功能
驗證Kafka和RabbitMQ監控器對UnitsDeparted和WorkCompleted消息的解析能力
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from kafka_monitor import KafkaMonitor
from rabbitmq_monitor import RabbitMQMonitor
from device_manager import DeviceManager

def create_test_device_manager():
    """創建測試用的設備管理器"""
    device_manager = DeviceManager()
    
    # 添加測試設備
    test_device = {
        'deviceId': 'S720050063',
        'lineName': 'A00',
        'stationName': 'TEST_STATION',
        'isActive': True,
        'isMonitoring': False,
        'cavityCount': 2
    }
    
    device_manager.devices['S720050063'] = test_device
    return device_manager

def test_kafka_units_departed_parsing():
    """測試Kafka UnitsDeparted消息解析"""
    print("=== 測試Kafka UnitsDeparted消息解析 ===")
    
    device_manager = create_test_device_manager()
    kafka_monitor = KafkaMonitor(device_manager)
    
    # 模擬UnitsDeparted消息
    units_departed_data = {
        'Data': {
            'Meta': {
                'MessageName': 'CFX.Production.UnitsDeparted',
                'DeviceID': 'S720050063',
                'LineID': 'MAG-A00',
                'MfgPlantCode': 'MAG',
                'Factory': 'CZ',
                'Source': 'CFX.A00.S720050063'
            },
            'ProcessedTime': '2025-06-19T09:44:14.275641+00:00',
            'RawData': {
                'MessageName': 'CFX.Production.UnitsDeparted',
                'Version': '1.7',
                'TimeStamp': '2025-06-19T09:44:14.2737209+00:00',
                'UniqueID': 'c9eac6da-8170-4642-8ba1-48b175816db4',
                'Source': 'CFX.A00.S720050063',
                'Target': 'inline-control',
                'RequestID': 'bcf384ba-915f-4076-a76c-af339dc0153e',
                'MessageBody': {
                    '$type': 'CFX.Production.UnitsDeparted, CFX',
                    'PrimaryIdentifier': 'string',
                    'HermesIdentifier': 'string',
                    'UnitCount': 1,
                    'Units': [
                        {
                            'UnitIdentifier': '5490',
                            'PositionNumber': 1,
                            'PositionName': '5490_1',
                            'X': 0.0,
                            'Y': 0.0,
                            'Rotation': 0.0,
                            'FlipX': False,
                            'FlipY': False,
                            'Status': 'Pass'
                        }
                    ],
                    'Lane': 1
                }
            }
        },
        'MessageID': '2489573a-a338-4ead-86cb-7bec68c08994',
        'TransactionID': None,
        'MessageType': 'DEVICE_CFX',
        'MessageVersion': '1.0',
        'MessageTime': '2025-06-19T09:44:14.279914+00:00',
        'Sender': 'CZ-SIE'
    }
    
    # 測試設備ID提取
    device_id = kafka_monitor._extract_device_id(units_departed_data)
    print(f"提取的設備ID: {device_id}")
    
    # 測試消息名稱提取
    message_name = kafka_monitor._extract_message_name(units_departed_data)
    print(f"提取的消息名稱: {message_name}")
    
    # 測試產品數據提取
    units_data = kafka_monitor._extract_units_data(units_departed_data, message_name)
    print(f"提取的產品數據: {len(units_data)}個")
    for i, unit in enumerate(units_data):
        print(f"  產品 {i+1}: 條碼={unit['UnitIdentifier']}, 狀態={unit['Status']}")
    
    print()

def test_kafka_work_completed_parsing():
    """測試Kafka WorkCompleted消息解析"""
    print("=== 測試Kafka WorkCompleted消息解析 ===")
    
    device_manager = create_test_device_manager()
    kafka_monitor = KafkaMonitor(device_manager)
    
    # 模擬WorkCompleted消息
    work_completed_data = {
        'Data': {
            'Meta': {
                'MessageName': 'CFX.Production.WorkCompleted',
                'DeviceID': 'S720050063',
                'LineID': 'MAG-A00',
                'MfgPlantCode': 'MAG',
                'Factory': 'CZ',
                'Source': 'CFX.A00.S720050063'
            },
            'ProcessedTime': '2025-06-19T09:44:14.275641+00:00',
            'RawData': {
                'MessageName': 'CFX.Production.WorkCompleted',
                'Version': '1.7',
                'TimeStamp': '2025-06-19T09:44:14.3266137+08:00',
                'UniqueID': '2914e905-6a70-4f1b-90fa-0f9b27fb8873',
                'Source': 'CFX.A00.S720050063',
                'Target': 'inline-control',
                'RequestID': None,
                'MessageBody': {
                    '$type': 'CFX.Production.WorkCompleted, CFX',
                    'TransactionID': '5c4e9ded-9643-4092-a5f4-922e3474feb9',
                    'Result': 'Completed',
                    'PrimaryIdentifier': None,
                    'HermesIdentifier': None,
                    'UnitCount': 2,
                    'Units': [
                        {
                            'UnitIdentifier': '0',
                            'PositionNumber': 0,
                            'PositionName': None,
                            'X': 0.0,
                            'Y': 0.0,
                            'Rotation': 0.0,
                            'FlipX': False,
                            'FlipY': False,
                            'Status': 'Pass'
                        },
                        {
                            'UnitIdentifier': '1',
                            'PositionNumber': 0,
                            'PositionName': None,
                            'X': 0.0,
                            'Y': 0.0,
                            'Rotation': 0.0,
                            'FlipX': False,
                            'FlipY': False,
                            'Status': 'Pass'
                        }
                    ],
                    'PerformanceImpacts': None
                }
            }
        },
        'MessageID': '2914e905-6a70-4f1b-90fa-0f9b27fb8873',
        'TransactionID': None,
        'MessageType': 'DEVICE_CFX',
        'MessageVersion': '1.0',
        'MessageTime': '2025-06-19T09:44:14.329914+00:00',
        'Sender': 'CZ-SIE'
    }
    
    # 測試設備ID提取
    device_id = kafka_monitor._extract_device_id(work_completed_data)
    print(f"提取的設備ID: {device_id}")
    
    # 測試消息名稱提取
    message_name = kafka_monitor._extract_message_name(work_completed_data)
    print(f"提取的消息名稱: {message_name}")
    
    # 測試產品數據提取
    units_data = kafka_monitor._extract_units_data(work_completed_data, message_name)
    print(f"提取的產品數據: {len(units_data)}個")
    for i, unit in enumerate(units_data):
        print(f"  產品 {i+1}: 條碼={unit['UnitIdentifier']}, 狀態={unit['Status']}")
    
    print()

def test_rabbitmq_work_completed_parsing():
    """測試RabbitMQ WorkCompleted消息解析"""
    print("=== 測試RabbitMQ WorkCompleted消息解析 ===")
    
    device_manager = create_test_device_manager()
    
    # 模擬WorkCompleted消息（直接的消息體格式）
    work_completed_message = {
        "MessageName": "CFX.Production.WorkCompleted",
        "Version": "1.7",
        "TimeStamp": "2025-06-19T09:44:14.3266137+08:00",
        "UniqueID": "2914e905-6a70-4f1b-90fa-0f9b27fb8873",
        "Source": "CFX.A00.S720050063",
        "Target": "inline-control",
        "RequestID": None,
        "MessageBody": {
            "$type": "CFX.Production.WorkCompleted, CFX",
            "TransactionID": "5c4e9ded-9643-4092-a5f4-922e3474feb9",
            "Result": "Completed",
            "PrimaryIdentifier": None,
            "HermesIdentifier": None,
            "UnitCount": 2,
            "Units": [
                {
                    "UnitIdentifier": "0",
                    "PositionNumber": 0,
                    "PositionName": None,
                    "X": 0.0,
                    "Y": 0.0,
                    "Rotation": 0.0,
                    "FlipX": False,
                    "FlipY": False,
                    "Status": "Pass"
                },
                {
                    "UnitIdentifier": "1",
                    "PositionNumber": 0,
                    "PositionName": None,
                    "X": 0.0,
                    "Y": 0.0,
                    "Rotation": 0.0,
                    "FlipX": False,
                    "FlipY": False,
                    "Status": "Pass"
                }
            ],
            "PerformanceImpacts": None
        }
    }
    
    # 創建RabbitMQ監控器（不需要實際連接）
    try:
        from mes_api import MESApi
        mes_api = MESApi(device_manager)
        rabbitmq_monitor = RabbitMQMonitor(device_manager, mes_api)
        
        # 測試消息處理
        device_id = 'S720050063'
        routing_key = 'edadata.cfx.test.S720050063.CFX.Production.WorkCompleted'
        
        print(f"測試設備ID: {device_id}")
        print(f"測試Routing Key: {routing_key}")
        print(f"消息類型: {work_completed_message['MessageName']}")
        
        # 測試WorkCompleted消息處理
        message_body = work_completed_message.get('MessageBody', {})
        units = message_body.get('Units', [])
        unit_count = message_body.get('UnitCount', len(units))
        result = message_body.get('Result', 'Unknown')
        
        print(f"UnitCount: {unit_count}")
        print(f"Result: {result}")
        print(f"提取的產品數據: {len(units)}個")
        
        for i, unit in enumerate(units):
            unit_identifier = unit.get('UnitIdentifier', '')
            status = unit.get('Status', 'Unknown')
            print(f"  產品 {i+1}: 條碼={unit_identifier}, 狀態={status}")
        
    except ImportError:
        print("⚠️ MESApi模組未找到，跳過RabbitMQ測試")
    
    print()

if __name__ == "__main__":
    print("開始測試消息解析功能...\n")
    
    try:
        test_kafka_units_departed_parsing()
        test_kafka_work_completed_parsing()
        test_rabbitmq_work_completed_parsing()
        
        print("✅ 所有消息解析測試完成！")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
