# import random
import requests
import hashlib
import json
# import time

#通用变量
# global tokenID,secretkey,headers
secretkey = '894A0F0DF84A4799E0530CCA940AC604'
tokenID = '894A0F0DF8494799E0530CCA940AC604'
headers = {
    'tokenID': "894A0F0DF8494799E0530CCA940AC604",
    'Content-Type': "application/json"
    }

#routing变量
# global url_routing,params_routing,data_routing,testdata
testdata = []
url_routing = "http://10.148.192.37:10101/TDC/DELTA_DEAL_TEST_DATA_I"
params_routing = {
    'sign': ""
    }
data_routing =  {
    'factory': "",
    'testType': "",
    'routingData': "",
    'testData': testdata
    }


#0. md5加密使用
def keymd5(src):
    str_md5 = hashlib.md5(src.encode("utf-8")).hexdigest()
    str_md5 = str_md5.upper()
    return str_md5

#1. 途程更新及测试数据上传接口
def routing(url_routing,data_routing,headers_routing,params_routing):
    #将body的数据转换为字符串格式
    data_str = json.dumps(data_routing, sort_keys=False)
    #字符串拼接md5加密
    src = secretkey + data_str
    md5 = keymd5(src)
    params_routing['sign'] = md5
    #发送数据并接收返回数据
    receive = requests.request("POST", url_routing, data=data_str, headers=headers_routing, params=params_routing)
    #str转换为字典列表
    receive = eval(receive.text)
    
    return receive
# # #1.1 Demo
testdata = []
data_routing['factory'] = 'DG7'
data_routing['testType'] = 'NO_ROUTE'
# data_routing['routingData'] = SN(內部序號/CASE序號,產品條碼)}MO Number(工單)}Model Name(機種)}Line Name(線別)}Section Name(段別)}Group Name(組別)}Station Name(站別)} 不良代碼 1:元件位置 1:引腳 1(沒有可為空):連片編號 1(沒有可為空);不良代碼1:元件位置 2:引腳 1:連片編號 2; …}Result(測試結果)}Emp(測試人)}Qty(批量更新數量)} 預留 1}Machine_No(測試機器編號)}測試子程式(不需要可為空)}測試子程式版本(不需要可為空)} 載具(跟著產品流線，多個用；隔開，暫時存在一個欄位中)}機台治具(按機種更換，多個用；隔開，暫時存在一個欄位中)}機台耗材(按使用壽命更換，多個用；隔開，暫時存在一個欄位中)} Error Mark }
sncode = '2025061213500757045'
mo = '***********'
model = 'DPS-AB800-A'
line = 'A2-01'
section = 'INJECTION'
group = 'INJECTION'
station = 'INJECTION'
usr_id = 'S32000405'
Qty = '1'
DeviceID = 'S32000405'
data_routing['routingData'] = str(
                                sncode+'}'
                                +mo+'}'
                                +model+'}'
                                +line+'}'
                                +section+'}'
                                +group+'}'
                                +station+'}'
                                +''+'}'
                                +'PASS'+'}'
                                +usr_id+'}'
                                +Qty+'}'
                                +''+'}'
                                +DeviceID+'}'
                                +''+'}'
                                +''+'}'
                                +''+'}'
                                +''+'}'
                                +''+'}'
                                +''+'}'
                                )
data_routing['testData'] = testdata

routing_post = routing(url_routing, data_routing, headers, params_routing)
if routing_post['result'].find('OK') != -1:
    print('上传成功')
else:
    print('上传失败')
