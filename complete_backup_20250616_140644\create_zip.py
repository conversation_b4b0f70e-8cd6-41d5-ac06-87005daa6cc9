#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
創建部署包壓縮文件
"""

import zipfile
import os
from pathlib import Path

def create_deployment_zip():
    """創建部署包壓縮文件"""
    deploy_dir = Path('MES_System_Deploy')
    zip_name = '代理MES上報系統_v1.0.zip'
    
    if not deploy_dir.exists():
        print("❌ 部署目錄不存在")
        return False
    
    print("📦 正在創建壓縮包...")
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in deploy_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(deploy_dir.parent)
                zipf.write(file_path, arcname)
                print(f"  ✅ 添加: {arcname}")
    
    # 檢查壓縮包大小
    zip_size = os.path.getsize(zip_name) / (1024 * 1024)  # MB
    print(f"\n🎉 壓縮包創建完成!")
    print(f"📁 文件名: {zip_name}")
    print(f"📏 大小: {zip_size:.1f} MB")
    
    return True

if __name__ == '__main__':
    create_deployment_zip()
