import requests
import hashlib
import json
from datetime import datetime
from pqm_api import PQMApi

class MESApi:
    """MES API調用模組"""
    
    def __init__(self, device_manager=None):
        # MES API配置
        self.secret_key = '894A0F0DF84A4799E0530CCA940AC604'
        self.token_id = '894A0F0DF8494799E0530CCA940AC604'
        self.headers = {
            'tokenID': "894A0F0DF8494799E0530CCA940AC604",
            'Content-Type': "application/json"
        }
        self.url_routing = "http://10.148.192.37:10101/TDC/DELTA_DEAL_TEST_DATA_I"
        self.device_manager = device_manager

        # 初始化PQM API
        self.pqm_api = PQMApi(device_manager)
        
    def _keymd5(self, src):
        """MD5加密"""
        str_md5 = hashlib.md5(src.encode("utf-8")).hexdigest()
        str_md5 = str_md5.upper()
        return str_md5

    def _upload_to_pqm_after_mes_success(self, device_info, work_order_info, product_data):
        """
        MES上傳成功後上傳到PQM

        Args:
            device_info: 設備信息
            work_order_info: 工單信息
            product_data: 產品數據

        Returns:
            dict: PQM上傳結果
        """
        try:
            # 獲取設備的已轉發數量
            if self.device_manager:
                device = self.device_manager.get_device(device_info['deviceId'])
                if device:
                    forwarded_count = device.get('forwardedCount', 0)
                else:
                    forwarded_count = 1  # 默認值
            else:
                forwarded_count = 1  # 默認值

            # 調用PQM API上傳
            pqm_result = self.pqm_api.upload_production_data(
                device_info,
                work_order_info,
                product_data,
                forwarded_count
            )

            return pqm_result

        except Exception as e:
            error_msg = f"PQM上傳異常: {str(e)}"
            print(f"❌ {error_msg}")
            return {
                'success': False,
                'message': error_msg,
                'response': None,
                'status_code': None
            }
    
    def upload_production_data(self, device_info, work_order_info, product_data):
        """
        上傳生產數據到MES

        Args:
            device_info: 設備信息 {deviceId, lineName, sectionName, groupName, stationName}
            work_order_info: 工單信息 {workOrder, model, cavityCount}
            product_data: 產品數據 {unitIdentifier, status}

        Returns:
            dict: {success: bool, message: str, response: dict}
        """
        try:
            # 準備數據
            original_barcode = product_data['UnitIdentifier']  # 原始產品條碼
            station_type = work_order_info.get('stationType', '無條碼過站')  # 過站類型

            # 根據過站類型處理條碼
            if station_type == '無條碼過站':
                sncode = 'DEFAULT'  # 無條碼過站使用DEFAULT
                print(f"📋 過站類型: {station_type} - 使用DEFAULT條碼 (原始條碼: {original_barcode})")
            else:
                sncode = original_barcode  # 其他類型使用原始條碼
                print(f"📋 過站類型: {station_type} - 使用原始條碼: {original_barcode}")

            mo = work_order_info['workOrder']  # 工單
            model = work_order_info['model']  # 機種
            line = device_info['lineName']  # 線別
            section = device_info['sectionName']  # 段別
            group = device_info['groupName']  # 組別
            station = device_info['stationName']  # 站別
            usr_id = device_info['deviceId']  # 測試人員號碼（使用設備ID）
            cavity_count = work_order_info.get('cavityCount', 1)  # 模穴數，默認為1
            qty = str(cavity_count)  # 本次產量 = 模穴數
            device_id = device_info['deviceId']  # 設備ID
            
            # 根據產品狀態設置測試結果
            test_result = 'PASS' if product_data['Status'] == 'Pass' else 'FAIL'
            
            # 構建routing數據
            routing_data = str(
                sncode + '}' +
                mo + '}' +
                model + '}' +
                line + '}' +
                section + '}' +
                group + '}' +
                station + '}' +
                '' + '}' +  # 不良代碼（空）
                test_result + '}' +
                usr_id + '}' +
                qty + '}' +
                '' + '}' +  # 預留1（空）
                device_id + '}' +
                '' + '}' +  # 測試子程式（空）
                '' + '}' +  # 測試子程式版本（空）
                '' + '}' +  # 載具（空）
                '' + '}' +  # 機台治具（空）
                '' + '}' +  # 機台耗材（空）
                ''          # Error Mark（空）
            )
            
            # 構建請求數據
            data_routing = {
                'factory': 'DG7',
                'testType': 'NO_ROUTE',
                'routingData': routing_data,
                'testData': []
            }
            
            # 生成簽名
            data_str = json.dumps(data_routing, sort_keys=False)
            src = self.secret_key + data_str
            md5_sign = self._keymd5(src)
            
            params = {
                'sign': md5_sign
            }
            
            # 發送請求
            print(f"📤 發送MES API請求:")
            print(f"   URL: {self.url_routing}")
            print(f"   產品條碼: {sncode}")
            print(f"   工單: {mo}")
            print(f"   機種: {model}")
            print(f"   設備: {device_id}")
            print(f"   模穴數: {cavity_count}")
            print(f"   產量: {qty}")
            print(f"   測試結果: {test_result}")
            print(f"📋 請求Body內容:")
            print(f"   {json.dumps(data_routing, indent=2, ensure_ascii=False)}")
            
            response = requests.post(
                self.url_routing,
                data=data_str,
                headers=self.headers,
                params=params,
                timeout=30
            )
            
            # 解析響應
            response_data = eval(response.text)

            # 打印完整的原始響應用於調試
            print(f"📋 MES API完整響應:")
            print(f"   HTTP狀態碼: {response.status_code}")
            print(f"   原始響應文本: {response.text}")
            print(f"   解析後數據: {response_data}")

            # 格式化完整響應信息用於前端顯示（包含請求和響應數據）
            full_response = f"HTTP {response.status_code} | 原始響應: {response.text}"
            # 格式化簡潔響應信息用於設備卡片顯示
            card_response = f"HTTP {response.status_code} | {response.text}"

            # 準備請求數據用於記錄
            request_data = {
                'url': self.url_routing,
                'method': 'POST',
                'headers': self.headers,
                'params': params,
                'body': data_routing
            }

            # 檢查結果
            if response_data.get('result', '').find('OK') != -1:
                success_msg = f"✅ MES成功: {response_data.get('result', 'OK')}"
                print(f"✅ MES上傳成功: {response_data.get('result', '')}")

                # 根據上傳類型決定是否調用PQM
                upload_type = work_order_info.get('uploadType', '同時上報PQM和MES')

                if upload_type == '僅上報MES':
                    # 僅上報MES，不調用PQM
                    return {
                        'success': True,
                        'message': success_msg,
                        'full_response': full_response,
                        'card_response': card_response,
                        'request_data': request_data,
                        'response': response_data
                    }
                else:
                    # 同時上報PQM和MES（默認行為）
                    # MES上傳成功後，嘗試上傳到PQM
                    pqm_result = self._upload_to_pqm_after_mes_success(
                        device_info, work_order_info, product_data
                    )

                    # 合併MES和PQM的結果信息
                    if pqm_result['success']:
                        combined_msg = f"{success_msg} | ✅ PQM成功"
                        combined_card_response = f"{card_response} | PQM: HTTP {pqm_result.get('status_code', 'OK')}"
                    else:
                        combined_msg = f"{success_msg} | ❌ PQM失敗: {pqm_result['message']}"
                        combined_card_response = f"{card_response} | PQM: {pqm_result['message']}"

                    return {
                        'success': True,
                        'message': combined_msg,
                        'full_response': full_response,
                        'card_response': combined_card_response,
                        'request_data': request_data,
                        'response': response_data,
                        'pqm_result': pqm_result
                    }
            else:
                error_msg = f"❌ 失敗: {response_data.get('result', '未知錯誤')}"
                print(f"❌ MES上傳失敗: {response_data.get('result', '未知錯誤')}")
                print(f"❌ 完整錯誤信息: {response_data}")
                return {
                    'success': False,
                    'message': error_msg,
                    'full_response': full_response,
                    'card_response': card_response,
                    'request_data': request_data,
                    'response': response_data
                }
                
        except requests.exceptions.Timeout:
            error_msg = "❌ MES API請求超時"
            full_response = "HTTP Timeout | 請求超時，無響應"
            card_response = "HTTP Timeout | 請求超時"
            print(f"❌ {error_msg}")
            print(f"❌ 請求URL: {self.url_routing}")
            print(f"❌ 請求數據: {data_str}")

            # 準備請求數據用於記錄
            request_data = {
                'url': self.url_routing,
                'method': 'POST',
                'headers': self.headers,
                'params': params,
                'body': data_routing
            }

            return {
                'success': False,
                'message': error_msg,
                'full_response': full_response,
                'card_response': card_response,
                'request_data': request_data,
                'response': None
            }
        except requests.exceptions.ConnectionError as e:
            error_msg = "❌ 無法連接到MES服務器"
            full_response = f"HTTP Connection Error | 連接錯誤: {str(e)}"
            card_response = "HTTP Connection Error | 無法連接"
            print(f"❌ {error_msg}")
            print(f"❌ 連接錯誤詳情: {str(e)}")
            print(f"❌ 請求URL: {self.url_routing}")

            # 準備請求數據用於記錄
            request_data = {
                'url': self.url_routing,
                'method': 'POST',
                'headers': self.headers,
                'params': params,
                'body': data_routing
            }

            return {
                'success': False,
                'message': error_msg,
                'full_response': full_response,
                'card_response': card_response,
                'request_data': request_data,
                'response': None
            }
        except Exception as e:
            error_msg = f"❌ MES API調用異常: {str(e)}"
            full_response = f"HTTP Exception | 異常詳情: {str(e)} | 異常類型: {type(e).__name__}"
            card_response = f"HTTP Exception | {str(e)}"
            print(f"❌ {error_msg}")
            print(f"❌ 異常詳情: {str(e)}")
            print(f"❌ 異常類型: {type(e).__name__}")
            print(f"❌ 請求URL: {self.url_routing}")
            print(f"❌ 請求數據: {data_str}")

            # 準備請求數據用於記錄
            request_data = {
                'url': self.url_routing,
                'method': 'POST',
                'headers': self.headers,
                'params': params if 'params' in locals() else {},
                'body': data_routing if 'data_routing' in locals() else {}
            }

            return {
                'success': False,
                'message': error_msg,
                'full_response': full_response,
                'card_response': card_response,
                'request_data': request_data,
                'response': None
            }

    def send_production_data(self, device_id, barcode):
        """
        發送生產數據到MES（簡化版本，用於RabbitMQ監控）

        Args:
            device_id: 設備ID
            barcode: 產品條碼

        Returns:
            tuple: (success: bool, response: dict)
        """
        try:
            # 使用傳入的device_manager實例，如果沒有則創建新的
            if self.device_manager:
                device_manager = self.device_manager
            else:
                from device_manager import DeviceManager
                device_manager = DeviceManager()

            # 獲取設備信息
            device = device_manager.get_device(device_id)
            if not device:
                return False, {'error': f'設備 {device_id} 不存在'}

            # 獲取當前工單信息
            current_work_order = device_manager.get_current_work_order(device_id)

            if not current_work_order:
                return False, {'error': f'設備 {device_id} 沒有可用的工單'}

            # 準備設備信息
            device_info = {
                'deviceId': device['deviceId'],
                'lineName': device['lineName'],
                'sectionName': device['sectionName'],
                'groupName': device['groupName'],
                'stationName': device['stationName']
            }

            # 準備工單信息
            work_order_info = {
                'workOrder': current_work_order['workOrderNumber'],
                'model': current_work_order.get('modelName', ''),
                'cavityCount': current_work_order.get('cavityCount', 1)
            }

            # 準備產品數據（假設都是Pass狀態）
            product_data = {
                'UnitIdentifier': barcode,
                'Status': 'Pass'
            }

            # 調用完整的上傳方法
            result = self.upload_production_data(device_info, work_order_info, product_data)

            # 返回簡化的結果格式
            return result['success'], result

        except Exception as e:
            error_msg = f"發送生產數據失敗: {str(e)}"
            print(f"❌ {error_msg}")
            return False, {'error': error_msg, 'exception': str(e)}

    def test_connection(self):
        """測試MES API連接"""
        try:
            # 使用測試數據
            test_device_info = {
                'deviceId': 'TEST_DEVICE',
                'lineName': 'TEST_LINE',
                'sectionName': 'TEST_SECTION',
                'groupName': 'TEST_GROUP',
                'stationName': 'TEST_STATION'
            }
            
            test_work_order_info = {
                'workOrder': 'TEST_WO',
                'model': 'TEST_MODEL'
            }
            
            test_product_data = {
                'UnitIdentifier': 'TEST_BARCODE',
                'Status': 'Pass'
            }
            
            result = self.upload_production_data(
                test_device_info,
                test_work_order_info,
                test_product_data
            )
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'message': f"連接測試失敗: {str(e)}",
                'response': None
            }
