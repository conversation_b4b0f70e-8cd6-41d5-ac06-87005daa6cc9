(['E:\\vscode\\tomesmo\\open_browser.py'],
 ['E:\\vscode\\tomesmo'],
 ['webbrowser',
  'time',
  'sys',
  'subprocess',
  'os',
  'urllib.request',
  'urllib.error',
  'codecs'],
 ['e:\\python\\lib\\site-packages\\pygame\\__pyinstaller',
  'e:\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'e:\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks'],
 {},
 [],
 [],
 False,
 False,
 False,
 {},
 [],
 [],
 '3.7.9 (tags/v3.7.9:13c94747c7, Aug 17 2020, 18:01:55) [MSC v.1900 32 bit '
 '(Intel)]',
 [('open_browser', 'E:\\vscode\\tomesmo\\open_browser.py', 'PYSOURCE')],
 [('copy', 'e:\\python\\lib\\copy.py', 'PYMODULE'),
  ('_py_abc', 'e:\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('quopri', 'e:\\python\\lib\\quopri.py', 'PYMODULE'),
  ('getopt', 'e:\\python\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'e:\\python\\lib\\gettext.py', 'PYMODULE'),
  ('struct', 'e:\\python\\lib\\struct.py', 'PYMODULE'),
  ('stringprep', 'e:\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('bz2', 'e:\\python\\lib\\bz2.py', 'PYMODULE'),
  ('threading', 'e:\\python\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'e:\\python\\lib\\_threading_local.py', 'PYMODULE'),
  ('contextlib', 'e:\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('_compression', 'e:\\python\\lib\\_compression.py', 'PYMODULE'),
  ('base64', 'e:\\python\\lib\\base64.py', 'PYMODULE'),
  ('argparse', 'e:\\python\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'e:\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('tempfile', 'e:\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('random', 'e:\\python\\lib\\random.py', 'PYMODULE'),
  ('bisect', 'e:\\python\\lib\\bisect.py', 'PYMODULE'),
  ('hashlib', 'e:\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'e:\\python\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'e:\\python\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'e:\\python\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle', 'e:\\python\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('string', 'e:\\python\\lib\\string.py', 'PYMODULE'),
  ('shutil', 'e:\\python\\lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'e:\\python\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'e:\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'e:\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib', 'e:\\python\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib.abc', 'e:\\python\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'e:\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'e:\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.util', 'e:\\python\\lib\\importlib\\util.py', 'PYMODULE'),
  ('tarfile', 'e:\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'e:\\python\\lib\\gzip.py', 'PYMODULE'),
  ('lzma', 'e:\\python\\lib\\lzma.py', 'PYMODULE'),
  ('typing', 'e:\\python\\lib\\typing.py', 'PYMODULE'),
  ('tracemalloc', 'e:\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('getpass', 'e:\\python\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'e:\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'e:\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'e:\\python\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'e:\\python\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'e:\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('email.utils', 'e:\\python\\lib\\email\\utils.py', 'PYMODULE'),
  ('email.charset', 'e:\\python\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'e:\\python\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'e:\\python\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.quoprimime', 'e:\\python\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.base64mime', 'e:\\python\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._parseaddr', 'e:\\python\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'e:\\python\\lib\\calendar.py', 'PYMODULE'),
  ('datetime', 'e:\\python\\lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'e:\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('http.cookiejar', 'e:\\python\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'e:\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'e:\\python\\lib\\ssl.py', 'PYMODULE'),
  ('socket', 'e:\\python\\lib\\socket.py', 'PYMODULE'),
  ('selectors', 'e:\\python\\lib\\selectors.py', 'PYMODULE'),
  ('http.client', 'e:\\python\\lib\\http\\client.py', 'PYMODULE'),
  ('email.message', 'e:\\python\\lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'e:\\python\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'e:\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'e:\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'e:\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators', 'e:\\python\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'e:\\python\\lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'e:\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._policybase', 'e:\\python\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.header', 'e:\\python\\lib\\email\\header.py', 'PYMODULE'),
  ('uu', 'e:\\python\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'e:\\python\\lib\\optparse.py', 'PYMODULE'),
  ('email.parser', 'e:\\python\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'e:\\python\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email', 'e:\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('subprocess', 'e:\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'e:\\python\\lib\\signal.py', 'PYMODULE'),
  ('webbrowser', 'e:\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('glob', 'e:\\python\\lib\\glob.py', 'PYMODULE')],
 [('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('python37.dll', 'e:\\python\\python37.dll', 'BINARY'),
  ('VCRUNTIME140.dll', 'e:\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('ucrtbase.dll', 'C:\\WINDOWS\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('unicodedata.pyd', 'e:\\python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'e:\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'e:\\python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'e:\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'e:\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('select.pyd', 'e:\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'e:\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('libcrypto-1_1.dll', 'e:\\python\\DLLs\\libcrypto-1_1.dll', 'BINARY'),
  ('libssl-1_1.dll', 'e:\\python\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'E:\\vscode\\tomesmo\\build\\open_browser_english\\base_library.zip',
   'DATA')],
 [])
