(['E:\\vscode\\tomesmo\\open_browser.py'],
 ['E:\\vscode\\tomesmo'],
 ['webbrowser',
  'time',
  'sys',
  'subprocess',
  'os',
  'urllib.request',
  'urllib.error',
  'codecs'],
 ['e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'e:\\vscode\\tomesmo\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks'],
 {},
 [],
 [],
 False,
 False,
 False,
 {},
 [],
 [],
 '3.7.9 (tags/v3.7.9:13c94747c7, Aug 17 2020, 18:01:55) [MSC v.1900 32 bit '
 '(Intel)]',
 [('open_browser', 'E:\\vscode\\tomesmo\\open_browser.py', 'PYSOURCE')],
 [('copy', 'E:\\python\\lib\\copy.py', 'PYMODULE'),
  ('typing', 'E:\\python\\lib\\typing.py', 'PYMODULE'),
  ('contextlib', 'E:\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('quopri', 'E:\\python\\lib\\quopri.py', 'PYMODULE'),
  ('getopt', 'E:\\python\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'E:\\python\\lib\\gettext.py', 'PYMODULE'),
  ('struct', 'E:\\python\\lib\\struct.py', 'PYMODULE'),
  ('stringprep', 'E:\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('bz2', 'E:\\python\\lib\\bz2.py', 'PYMODULE'),
  ('threading', 'E:\\python\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'E:\\python\\lib\\_threading_local.py', 'PYMODULE'),
  ('_compression', 'E:\\python\\lib\\_compression.py', 'PYMODULE'),
  ('base64', 'E:\\python\\lib\\base64.py', 'PYMODULE'),
  ('_py_abc', 'E:\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('string', 'E:\\python\\lib\\string.py', 'PYMODULE'),
  ('tracemalloc', 'E:\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('pickle', 'E:\\python\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'E:\\python\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle', 'E:\\python\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('tempfile', 'E:\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('random', 'E:\\python\\lib\\random.py', 'PYMODULE'),
  ('bisect', 'E:\\python\\lib\\bisect.py', 'PYMODULE'),
  ('hashlib', 'E:\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'E:\\python\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('shutil', 'E:\\python\\lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'E:\\python\\lib\\zipfile.py', 'PYMODULE'),
  ('argparse', 'E:\\python\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'E:\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('py_compile', 'E:\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'E:\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib', 'E:\\python\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib.abc', 'E:\\python\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'E:\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'E:\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.util', 'E:\\python\\lib\\importlib\\util.py', 'PYMODULE'),
  ('tarfile', 'E:\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'E:\\python\\lib\\gzip.py', 'PYMODULE'),
  ('lzma', 'E:\\python\\lib\\lzma.py', 'PYMODULE'),
  ('getpass', 'E:\\python\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'E:\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'E:\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'E:\\python\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'E:\\python\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'E:\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('email.utils', 'E:\\python\\lib\\email\\utils.py', 'PYMODULE'),
  ('email.charset', 'E:\\python\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'E:\\python\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'E:\\python\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.quoprimime', 'E:\\python\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.base64mime', 'E:\\python\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._parseaddr', 'E:\\python\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'E:\\python\\lib\\calendar.py', 'PYMODULE'),
  ('datetime', 'E:\\python\\lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'E:\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('http.cookiejar', 'E:\\python\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'E:\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'E:\\python\\lib\\ssl.py', 'PYMODULE'),
  ('socket', 'E:\\python\\lib\\socket.py', 'PYMODULE'),
  ('selectors', 'E:\\python\\lib\\selectors.py', 'PYMODULE'),
  ('http.client', 'E:\\python\\lib\\http\\client.py', 'PYMODULE'),
  ('email.message', 'E:\\python\\lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'E:\\python\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'E:\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'E:\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'E:\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators', 'E:\\python\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'E:\\python\\lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'E:\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._policybase', 'E:\\python\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.header', 'E:\\python\\lib\\email\\header.py', 'PYMODULE'),
  ('uu', 'E:\\python\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'E:\\python\\lib\\optparse.py', 'PYMODULE'),
  ('email.parser', 'E:\\python\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'E:\\python\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email', 'E:\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('subprocess', 'E:\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'E:\\python\\lib\\signal.py', 'PYMODULE'),
  ('webbrowser', 'E:\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('glob', 'E:\\python\\lib\\glob.py', 'PYMODULE')],
 [('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'E:\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('python37.dll', 'E:\\python\\python37.dll', 'BINARY'),
  ('ucrtbase.dll', 'C:\\WINDOWS\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('unicodedata.pyd', 'E:\\python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'E:\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'E:\\python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'E:\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'E:\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('select.pyd', 'E:\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'E:\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('libcrypto-1_1.dll', 'E:\\python\\DLLs\\libcrypto-1_1.dll', 'BINARY'),
  ('libssl-1_1.dll', 'E:\\python\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'E:\\vscode\\tomesmo\\build\\open_browser_english\\base_library.zip',
   'DATA')],
 [])
