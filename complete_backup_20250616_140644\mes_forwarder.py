import json
from datetime import datetime

class MESForwarder:
    def __init__(self, device_manager=None):
        self.forwarded_count = 0
        self.device_manager = device_manager

    def forward_to_mes(self, device_config, units_data, raw_data):
        """轉發數據到MES過工單途程"""
        try:
            # 構建MES轉發數據
            mes_data = self._build_mes_data(device_config, units_data, raw_data)

            # 目前只進行print輸出，後續可替換為實際的API調用
            self._print_mes_data(mes_data)

            self.forwarded_count += 1

        except Exception as e:
            print(f"轉發到MES時發生錯誤: {e}")
            raise
    
    def _build_mes_data(self, device_config, units_data, raw_data):
        """構建MES數據格式"""
        timestamp = datetime.now().isoformat()
        
        mes_data = {
            'timestamp': timestamp,
            'deviceInfo': {
                'deviceId': device_config['deviceId'],
                'lineName': device_config['lineName'],
                'sectionName': device_config['sectionName'],
                'groupName': device_config['groupName'],
                'stationName': device_config['stationName'],
                'workOrder': device_config['workOrder'],
                'model': device_config['model']
            },
            'productionData': {
                'unitCount': len(units_data),
                'units': []
            },
            'rawKafkaData': {
                'messageTime': raw_data.get('MessageTime'),
                'messageId': raw_data.get('MessageID'),
                'source': raw_data.get('Data', {}).get('Meta', {}).get('Source')
            }
        }
        
        # 處理每個產品的數據
        for unit in units_data:
            unit_mes_data = {
                'unitIdentifier': unit['UnitIdentifier'],  # 產品條碼
                'status': unit['Status'],  # 生產結果 (Pass/Fail)
                'positionNumber': unit.get('PositionNumber'),
                'positionName': unit.get('PositionName'),
                'processedTime': timestamp
            }
            mes_data['productionData']['units'].append(unit_mes_data)
        
        return mes_data
    
    def _print_mes_data(self, mes_data):
        """打印MES轉發數據（模擬API調用）"""
        print("\n" + "="*80)
        print("🚀 轉發到MES過工單途程")
        print("="*80)
        
        device_info = mes_data['deviceInfo']
        print(f"📍 設備信息:")
        print(f"   設備ID: {device_info['deviceId']}")
        print(f"   線名: {device_info['lineName']}")
        print(f"   段名: {device_info['sectionName']}")
        print(f"   組名: {device_info['groupName']}")
        print(f"   站名: {device_info['stationName']}")
        print(f"   工單: {device_info['workOrder']}")
        print(f"   機種: {device_info['model']}")
        
        production_data = mes_data['productionData']
        print(f"\n📦 生產數據:")
        print(f"   產品數量: {production_data['unitCount']}")
        
        for i, unit in enumerate(production_data['units'], 1):
            status_emoji = "✅" if unit['status'] == 'Pass' else "❌"
            print(f"   產品 {i}:")
            print(f"     條碼: {unit['unitIdentifier']}")
            print(f"     結果: {unit['status']} {status_emoji}")
            print(f"     位置: {unit['positionName']}")
        
        print(f"\n⏰ 處理時間: {mes_data['timestamp']}")
        print(f"📊 累計轉發次數: {self.forwarded_count}")
        
        # 模擬MES API調用的JSON數據
        print(f"\n📤 MES API數據 (JSON):")
        print(json.dumps(mes_data, ensure_ascii=False, indent=2))
        
        print("="*80)
        print("✅ MES轉發完成\n")
    
    def get_forwarded_count(self):
        """獲取已轉發次數"""
        return self.forwarded_count
