#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PQM集成測試腳本
測試MES上傳成功後自動上傳到PQM的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from device_manager import <PERSON><PERSON><PERSON>anager
from mes_api import MESApi
from pqm_api import PQMApi

def test_pqm_integration():
    """測試PQM集成功能"""
    print("🧪 開始PQM集成測試...")
    
    # 創建設備管理器
    device_manager = DeviceManager('test_devices.json')
    
    # 創建MES API（包含PQM API）
    mes_api = MESApi(device_manager)
    
    # 創建測試設備
    test_device_id = 'TEST_PQM_DEVICE'
    try:
        device_manager.add_device(
            device_id=test_device_id,
            line_name='TEST_LINE',
            section_name='TEST_SECTION', 
            group_name='TEST_GROUP',
            station_name='TEST_STATION'
        )
        print(f"✅ 創建測試設備: {test_device_id}")
    except ValueError:
        print(f"⚠️ 測試設備已存在: {test_device_id}")
    
    # 添加測試工單
    device_manager.add_work_order(
        device_id=test_device_id,
        work_order_number='TEST_WO_PQM',
        target_quantity=10,
        model_name='TEST_MODEL_PQM',
        description='PQM測試工單',
        cavity_count=1,
        station_type='無條碼過站'
    )
    print(f"✅ 添加測試工單")
    
    # 啟用設備監控
    device_manager.toggle_device_monitoring(test_device_id, True)
    print(f"✅ 啟用設備監控")
    
    # 準備測試數據
    device_info = {
        'deviceId': test_device_id,
        'lineName': 'TEST_LINE',
        'sectionName': 'TEST_SECTION',
        'groupName': 'TEST_GROUP',
        'stationName': 'TEST_STATION'
    }
    
    work_order_info = {
        'workOrder': 'TEST_WO_PQM',
        'model': 'TEST_MODEL_PQM',
        'cavityCount': 1,
        'stationType': '無條碼過站'
    }
    
    product_data = {
        'UnitIdentifier': 'TEST_BARCODE_PQM_001',
        'Status': 'Pass'
    }
    
    print(f"\n📤 測試MES+PQM上傳...")
    print(f"   設備ID: {test_device_id}")
    print(f"   工單: {work_order_info['workOrder']}")
    print(f"   機種: {work_order_info['model']}")
    print(f"   產品條碼: {product_data['UnitIdentifier']}")
    
    # 調用MES API（應該自動觸發PQM上傳）
    result = mes_api.upload_production_data(
        device_info,
        work_order_info,
        product_data
    )
    
    print(f"\n📋 測試結果:")
    print(f"   MES上傳成功: {result['success']}")
    print(f"   響應消息: {result['message']}")
    
    if 'pqm_result' in result:
        pqm_result = result['pqm_result']
        print(f"   PQM上傳成功: {pqm_result['success']}")
        print(f"   PQM響應消息: {pqm_result['message']}")
        if 'status_code' in pqm_result:
            print(f"   PQM狀態碼: {pqm_result['status_code']}")
    else:
        print(f"   ⚠️ 沒有PQM結果信息")
    
    # 清理測試數據
    try:
        device_manager.delete_device(test_device_id)
        print(f"\n🧹 清理測試設備: {test_device_id}")
    except:
        pass
    
    # 刪除測試文件
    try:
        os.remove('test_devices.json')
        print(f"🧹 清理測試文件")
    except:
        pass
    
    print(f"\n🎉 PQM集成測試完成!")
    return result['success']

def test_time_calculation():
    """測試時間計算邏輯"""
    print("\n🧪 開始時間計算測試...")

    # 創建設備管理器
    device_manager = DeviceManager('test_time_devices.json')

    # 創建測試設備
    test_device_id = 'TEST_TIME_DEVICE'
    try:
        device_manager.add_device(
            device_id=test_device_id,
            line_name='TEST_LINE',
            section_name='TEST_SECTION',
            group_name='TEST_GROUP',
            station_name='TEST_STATION'
        )
        print(f"✅ 創建測試設備: {test_device_id}")
    except ValueError:
        print(f"⚠️ 測試設備已存在: {test_device_id}")

    # 添加測試工單
    device_manager.add_work_order(
        device_id=test_device_id,
        work_order_number='TEST_WO_TIME',
        target_quantity=5,
        model_name='TEST_MODEL_TIME',
        description='時間測試工單',
        cavity_count=1,
        station_type='無條碼過站'
    )

    print(f"\n📊 測試時間計算邏輯:")

    # 測試1: 未啟動MES時的時間
    running_time = device_manager.get_device_running_time(test_device_id)
    cycle_time = device_manager.get_and_update_cycle_time(test_device_id)
    print(f"   未啟動MES - runningTime: {running_time}秒, cycleTime: {cycle_time}秒")

    # 測試2: 啟動MES轉發
    device_manager.toggle_device_monitoring(test_device_id, True)
    print(f"   ✅ 啟動MES轉發")

    # 等待1秒
    import time
    time.sleep(1)

    # 測試3: 啟動後的時間
    running_time = device_manager.get_device_running_time(test_device_id)
    cycle_time = device_manager.get_and_update_cycle_time(test_device_id)
    print(f"   啟動後1秒 - runningTime: {running_time:.1f}秒, cycleTime: {cycle_time}秒 (首次應為0)")

    # 等待2秒
    time.sleep(2)

    # 測試4: 第二個產品
    running_time = device_manager.get_device_running_time(test_device_id)
    cycle_time = device_manager.get_and_update_cycle_time(test_device_id)
    print(f"   第二個產品 - runningTime: {running_time:.1f}秒, cycleTime: {cycle_time:.1f}秒 (應約為2秒)")

    # 測試5: 停止並重新啟動MES
    device_manager.toggle_device_monitoring(test_device_id, False)
    print(f"   🛑 停止MES轉發")

    time.sleep(1)
    device_manager.toggle_device_monitoring(test_device_id, True)
    print(f"   🚀 重新啟動MES轉發")

    # 測試6: 重新啟動後的時間
    running_time = device_manager.get_device_running_time(test_device_id)
    cycle_time = device_manager.get_and_update_cycle_time(test_device_id)
    print(f"   重新啟動後 - runningTime: {running_time:.1f}秒, cycleTime: {cycle_time}秒 (應重置為0)")

    # 清理測試數據
    try:
        device_manager.delete_device(test_device_id)
        print(f"\n🧹 清理測試設備: {test_device_id}")
    except:
        pass

    try:
        import os
        os.remove('test_time_devices.json')
        print(f"🧹 清理測試文件")
    except:
        pass

    print(f"🎉 時間計算測試完成!")
    return True

if __name__ == '__main__':
    print("=" * 60)
    print("🏭 MES Upload Manager - PQM集成測試")
    print("=" * 60)
    
    try:
        # 測試時間計算
        time_success = test_time_calculation()

        # 測試完整集成
        integration_success = test_pqm_integration()

        print(f"\n" + "=" * 60)
        print(f"📊 測試總結:")
        print(f"   時間計算測試: {'✅ 通過' if time_success else '❌ 失敗'}")
        print(f"   集成測試: {'✅ 通過' if integration_success else '❌ 失敗'}")
        print(f"=" * 60)
        
    except Exception as e:
        print(f"❌ 測試過程中出現異常: {e}")
        import traceback
        traceback.print_exc()
