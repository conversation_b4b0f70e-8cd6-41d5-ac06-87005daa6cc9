from kafka import KafkaConsumer
import time
import datetime
from mes_forwarder import MESForwarder
from kafka_config_manager import kafka_config_manager

class KafkaMonitor:
    def __init__(self, device_manager):
        self.device_manager = device_manager
        self.mes_forwarder = MESForwarder(device_manager)
        self.consumer = None
        self.running = False
        self.processed_products = {}  # 用於去重的產品數據 {device_id: {product_id: timestamp}}
        self.monitor_thread = None
        self.start_time_ms = None  # 監控啟動時間戳（毫秒）
    
    def start_monitoring(self):
        """開始監控Kafka消息"""
        if self.running:
            print("Kafka監控已在運行中")
            return

        self.running = True

        # 記錄監控啟動時間戳（毫秒）
        self.start_time_ms = int(time.time() * 1000)
        start_time_str = datetime.datetime.fromtimestamp(self.start_time_ms/1000).strftime('%Y-%m-%d %H:%M:%S.%f')
        print(f"開始Kafka監控...")
        print(f"監控啟動時間: {start_time_str}")

        try:
            # 獲取當前Kafka配置
            consumer_config = kafka_config_manager.get_consumer_config()

            # 創建Kafka消費者
            self.consumer = KafkaConsumer(**consumer_config)

            # 獲取所有啟用設備的線名
            devices = self.device_manager.get_all_devices()
            active_lines = set()
            for device in devices.values():
                if device.get('isActive', True):
                    active_lines.add(device['lineName'])

            if not active_lines:
                print("⚠️  沒有啟用的設備，無法生成監控主題")
                return

            # 根據線名生成主題列表
            topics = kafka_config_manager.generate_topic_patterns(list(active_lines))

            # 訂閱主題
            self.consumer.subscribe(topics)

            print(f"已訂閱 {len(topics)} 個主題:")
            for topic in topics:
                print(f"  - {topic}")

            # 等待分配分區
            print("等待分區分配...")
            while not self.consumer.assignment():
                self.consumer.poll(0)
                time.sleep(0.1)

            # 獲取分配的分區
            partitions = self.consumer.assignment()
            print(f"已分配 {len(partitions)} 個分區")

            # 為每個分區設置從啟動時間開始的偏移量
            self._set_offset_from_start_time(partitions)

            print(f"注意: 只會消費從 {start_time_str} 之後的消息")
            print("開始接收消息...")

            # 循環接收消息
            for message in self.consumer:
                if not self.running:
                    break

                try:
                    self._process_message(message)
                except Exception as e:
                    print(f"處理消息時發生錯誤: {e}")
                    print(f"消息內容: {message.value}")

        except Exception as e:
            print(f"Kafka監控發生錯誤: {e}")
        finally:
            self._cleanup()

    def _set_offset_from_start_time(self, partitions):
        """為每個分區設置從啟動時間開始的偏移量"""
        try:
            # 為每個分區創建一個時間戳-偏移量映射
            time_map = {}
            for partition in partitions:
                time_map[partition] = self.start_time_ms

            # 獲取每個分區在給定時間戳的偏移量
            offsets_for_times = self.consumer.offsets_for_times(time_map)

            # 對每個分區設置從指定時間開始的偏移量
            for partition, offset_and_timestamp in offsets_for_times.items():
                if offset_and_timestamp is None:
                    # 如果在給定時間後沒有消息，設置為最新偏移量
                    print(f"分區 {partition.partition} 在啟動時間後沒有新消息，設置為最新偏移量")
                    self.consumer.seek_to_end(partition)
                else:
                    # 設置為給定時間的偏移量
                    print(f"分區 {partition.partition} 設置偏移量為: {offset_and_timestamp.offset}")
                    self.consumer.seek(partition, offset_and_timestamp.offset)
        except Exception as e:
            print(f"設置偏移量時發生錯誤: {e}")

    def _process_message(self, message):
        """處理Kafka消息"""
        msg_time = datetime.datetime.fromtimestamp(message.timestamp/1000).strftime('%Y-%m-%d %H:%M:%S.%f')
        print(f"\n消息時間: {msg_time}")
        print(f"主題: {message.topic}, 分區: {message.partition}, 偏移量: {message.offset}")

        try:
            data = message.value

            # 檢查消息類型
            message_name = self._extract_message_name(data)
            print(f"消息類型: {message_name}")

            # 提取設備ID
            device_id = self._extract_device_id(data)
            if not device_id:
                print("無法提取設備ID，跳過此消息")
                return

            # 提取產品數據用於去重
            units_data = self._extract_units_data(data, message_name)
            if not units_data:
                print("無法提取產品數據，跳過此消息")
                return

            # 檢查產品去重
            message_time = data.get('MessageTime', '')
            if self._is_duplicate_product(device_id, units_data, message_time):
                print(f"⚠️  重複產品數據已跳過: 設備{device_id}, 產品: {[u.get('UnitIdentifier') for u in units_data]}")
                return

            print(f"設備ID: {device_id}")

            # 檢查設備是否存在且啟用
            device_config = self.device_manager.get_device(device_id)
            if not device_config or not device_config.get('isActive', True):
                print(f"設備 {device_id} 未配置或未啟用，跳過")
                return

            print(f"找到設備配置: {device_config['lineName']} - {device_config['stationName']}")

            # 檢查是否啟用MES轉發
            is_mes_forwarding = device_config.get('isMonitoring', False)
            print(f"MES轉發狀態: {'啟用' if is_mes_forwarding else '停用'}")

            print(f"提取到 {len(units_data)} 個產品數據")

            # 詳細打印產品信息用於調試
            for i, unit in enumerate(units_data):
                print(f"  產品 {i+1}: 條碼={unit.get('UnitIdentifier')}, 狀態={unit.get('Status')}")

            # 處理MES轉發
            forwarded_count = 0
            if is_mes_forwarding:
                print("✅ 執行MES轉發")
                # 使用真實MES API轉發每個產品
                for unit in units_data:
                    if self.device_manager.forward_to_mes(device_id, unit):
                        forwarded_count += 1

                print(f"📤 MES轉發結果: {forwarded_count}/{len(units_data)} 成功")
            else:
                print("⚠️  MES轉發已停用，僅記錄數據")

            # 更新設備日誌（根據實際轉發結果）
            print(f"🔄 更新設備統計: 設備={device_id}, 產品數量={len(units_data)}, 轉發成功={forwarded_count}")
            self._update_device_log(device_config, units_data, forwarded_count)

            # 記錄已處理的產品數據（防止重複處理）
            self._record_processed_products(device_id, units_data, message_time)

        except Exception as e:
            print(f"處理消息時發生錯誤: {e}")
            print(f"原始消息: {data}")
    
    def _is_duplicate_product(self, device_id, units_data, message_time):
        """檢查是否為重複的產品數據"""
        current_time = time.time()

        # 初始化設備記錄
        if device_id not in self.processed_products:
            self.processed_products[device_id] = {}

        device_products = self.processed_products[device_id]

        # 檢查每個產品是否重複
        for unit in units_data:
            product_id = unit.get('UnitIdentifier', '')
            if not product_id:
                continue

            # 如果產品在最近5秒內已處理過，視為重複
            if product_id in device_products:
                last_time = device_products[product_id]
                if current_time - last_time < 5:  # 5秒內的重複
                    return True

        return False

    def _record_processed_products(self, device_id, units_data, message_time):
        """記錄已處理的產品數據"""
        current_time = time.time()

        # 初始化設備記錄
        if device_id not in self.processed_products:
            self.processed_products[device_id] = {}

        device_products = self.processed_products[device_id]

        # 記錄每個產品的處理時間
        for unit in units_data:
            product_id = unit.get('UnitIdentifier', '')
            if product_id:
                device_products[product_id] = current_time

        # 清理超過1分鐘的舊記錄，避免內存無限增長
        expired_products = []
        for product_id, timestamp in device_products.items():
            if current_time - timestamp > 60:  # 1分鐘
                expired_products.append(product_id)

        for product_id in expired_products:
            del device_products[product_id]

    def _extract_device_id(self, data):
        """從消息中提取設備ID"""
        try:
            return data.get('Data', {}).get('Meta', {}).get('DeviceID')
        except Exception:
            return None
    
    def _extract_message_name(self, data):
        """從消息中提取消息名稱"""
        try:
            return data.get('Data', {}).get('RawData', {}).get('MessageName', '')
        except Exception:
            return ''

    def _extract_units_data(self, data, message_name=None):
        """從消息中提取產品數據，支持UnitsDeparted和WorkCompleted兩種消息類型"""
        try:
            message_body = data.get('Data', {}).get('RawData', {}).get('MessageBody', {})

            # 檢查消息類型
            if not message_name:
                message_name = self._extract_message_name(data)

            units = message_body.get('Units', [])

            # 提取每個產品的關鍵信息
            units_data = []
            for unit in units:
                unit_data = {
                    'UnitIdentifier': unit.get('UnitIdentifier'),
                    'Status': unit.get('Status'),
                    'PositionNumber': unit.get('PositionNumber'),
                    'PositionName': unit.get('PositionName')
                }
                units_data.append(unit_data)

            # 對於WorkCompleted消息，打印額外的調試信息
            if 'WorkCompleted' in message_name:
                print(f"📋 WorkCompleted消息 - UnitCount: {message_body.get('UnitCount', 0)}")
                print(f"📋 WorkCompleted消息 - Result: {message_body.get('Result', 'Unknown')}")

            return units_data
        except Exception as e:
            print(f"❌ 提取產品數據失敗: {e}")
            return []
    
    def stop_monitoring(self):
        """停止監控"""
        print("正在停止Kafka監控...")
        self.running = False
        if self.consumer:
            self.consumer.close()
    
    def _cleanup(self):
        """清理資源"""
        if self.consumer:
            self.consumer.close()
        self.running = False
        print("Kafka監控已停止")
    
    def _update_device_log(self, device_config, units_data, forwarded_count):
        """更新設備日誌"""
        unit_count = len(units_data)

        # 根據實際轉發結果選擇符號
        if unit_count == 1:
            unit = units_data[0]
            if forwarded_count > 0:
                # 轉發成功，使用Pass/Fail符號
                status_emoji = "✅" if unit['Status'] == 'Pass' else "❌"
                log_message = f"產品條碼: {unit['UnitIdentifier']} {status_emoji}"
            else:
                # 轉發失敗或未轉發，使用問號
                log_message = f"產品條碼: {unit['UnitIdentifier']} ❓"
        else:
            barcodes = []
            for i, unit in enumerate(units_data):
                if i < forwarded_count:
                    # 轉發成功的產品
                    status_emoji = "✅" if unit['Status'] == 'Pass' else "❌"
                    barcodes.append(f"{unit['UnitIdentifier']}{status_emoji}")
                else:
                    # 轉發失敗的產品
                    barcodes.append(f"{unit['UnitIdentifier']}❓")
            log_message = f"產品條碼: {', '.join(barcodes)}"

        # 分別更新已轉發和未轉發的產品
        if forwarded_count > 0:
            # 更新已轉發的產品
            self.device_manager.update_device_production(
                device_config['deviceId'],
                forwarded_count,
                log_message,
                is_forwarded=True
            )

        if forwarded_count < unit_count:
            # 更新未轉發的產品
            unforwarded_count = unit_count - forwarded_count
            self.device_manager.update_device_production(
                device_config['deviceId'],
                unforwarded_count,
                log_message,
                is_forwarded=False
            )

    def is_running(self):
        """檢查是否正在運行"""
        return self.running
