---
apiVersion: "v1"
kind: "ConfigMap"
metadata:
  name: "immapi-config-1"
  namespace: "app-immapi-latest"
data:
  appsettings.json: "{ \"Logging\": { \"LogLevel\": { \"Default\": \"Information\"\
    , \"Microsoft.AspNetCore\": \"Warning\" } }, \"AllowedHosts\": \"*\", \"urls\"\
    : \"http://0.0.0.0:30071\", \"EQPCfgIP\": \"opc.tcp://************:4840\", \"\
    EQPID\": \"S720050039\", \"EQPIDBK\": \"S720050039\",  \"EQPName\": \"A2-01\", \"EQPType\": \"IMM\", \"RemoteRecipeManagementUri\"\
    : \"http://************:30063/recipe-management/v1/t3/device/model\", \"EQPVonder\"\
    : \"opcua\", \"ConfigDBUri\": \"mongodb://************:30018/\", \"CFXLocalCfgUri\"\
    : \"amqp://0.0.0.0:28881\", \"CFXLocalEndpointHandle\" : \"CFX.A00.S720050039\"\
    , \"CFXRemoteCfgUri\": \"amqp://************:30031\", \"CFXRemoteEndpointHandle\"\
    : \"inline-control\", \"HSetTransmitChannels\": [ \"amqp://************:30031,event\"\
    \ ], \"HSetReceiveChannels\": []}"
---
apiVersion: "v1"
kind: "ConfigMap"
metadata:
  name: "immapi-config-2"
  namespace: "app-immapi-latest"
data:
  appsettings.json: "{ \"Logging\": { \"LogLevel\": { \"Default\": \"Information\"\
    , \"Microsoft.AspNetCore\": \"Warning\" } }, \"AllowedHosts\": \"*\", \"urls\"\‘’
    : \"http://0.0.0.0:30072\", \"EQPCfgIP\": \"opc.tcp://************:4840\", \"\
    EQPID\": \"S720051501\", \"EQPIDBK\": \"S720051501\",  \"EQPName\": \"A2-25\", \"EQPType\": \"IMM\", \"RemoteRecipeManagementUri\"\
    : \"http://************:30063/recipe-management/v1/t3/device/model\", \"EQPVonder\"\
    : \"opcua\", \"ConfigDBUri\": \"mongodb://************:30018/\", \"CFXLocalCfgUri\"\
    : \"amqp://0.0.0.0:28882\", \"CFXLocalEndpointHandle\" : \"CFX.A00.S720051501\"\
    , \"CFXRemoteCfgUri\": \"amqp://************:30031\", \"CFXRemoteEndpointHandle\"\
    : \"inline-control\", \"HSetTransmitChannels\": [ \"amqp://************:30031,event\"\
    \ ], \"HSetReceiveChannels\": []}"
---
apiVersion: "v1"
kind: "ConfigMap"
metadata:
  name: "immapi-config-3"
  namespace: "app-immapi-latest"
data:
  appsettings.json: "{ \"Logging\": { \"LogLevel\": { \"Default\": \"Information\"\
    , \"Microsoft.AspNetCore\": \"Warning\" } }, \"AllowedHosts\": \"*\", \"urls\"\
    : \"http://0.0.0.0:30073\", \"EQPCfgIP\": \"opc.tcp://*************:4840\", \"\
    EQPID\": \"S720050053\", \"EQPIDBK\": \"S720050053\", \"EQPName\": \"A2-15\",\
    \ \"EQPType\": \"IMM\", \"RemoteRecipeManagementUri\": \"http://************:30063/recipe-management/v1/t3/device/model\"\
    , \"EQPVonder\": \"opcua\", \"ConfigDBUri\": \"mongodb://************:30018/\"\
    , \"CFXLocalCfgUri\": \"amqp://0.0.0.0:28883\", \"CFXLocalEndpointHandle\" : \"\
    CFX.A00.S720050053\", \"CFXRemoteCfgUri\": \"amqp://************:30031\", \"CFXRemoteEndpointHandle\"\
    : \"inline-control\", \"HSetTransmitChannels\": [ \"amqp://************:30031,event\"\
    \ ], \"HSetReceiveChannels\": []}"
---
apiVersion: "v1"
kind: "ConfigMap"
metadata:
  name: "immapi-config-4"
  namespace: "app-immapi-latest"
data:
  appsettings.json: "{ \"Logging\": { \"LogLevel\": { \"Default\": \"Information\"\
    , \"Microsoft.AspNetCore\": \"Warning\" } }, \"AllowedHosts\": \"*\", \"urls\"\
    : \"http://0.0.0.0:30074\", \"EQPCfgIP\": \"opc.tcp://*************:4840\", \"\
    EQPID\": \"S720050063\", \"EQPIDBK\": \"S720050063\", \"EQPName\": \"A2-26\",\
    \ \"EQPType\": \"IMM\", \"RemoteRecipeManagementUri\": \"http://************:30063/recipe-management/v1/t3/device/model\"\
    , \"EQPVonder\": \"opcua\", \"ConfigDBUri\": \"mongodb://************:30018/\"\
    , \"CFXLocalCfgUri\": \"amqp://0.0.0.0:28884\", \"CFXLocalEndpointHandle\" : \"\
    CFX.A00.S720050063\", \"CFXRemoteCfgUri\": \"amqp://************:30031\", \"CFXRemoteEndpointHandle\"\
    : \"inline-control\", \"HSetTransmitChannels\": [ \"amqp://************:30031,event\"\
    \ ], \"HSetReceiveChannels\": []}"
---
apiVersion: "v1"
kind: "ConfigMap"
metadata:
  name: "immapi-config-5"
  namespace: "app-immapi-latest"
data:
  appsettings.json: "{ \"Logging\": { \"LogLevel\": { \"Default\": \"Information\"\
    , \"Microsoft.AspNetCore\": \"Warning\" } }, \"AllowedHosts\": \"*\", \"urls\"\
    : \"http://0.0.0.0:30075\", \"EQPCfgIP\": \"opc.tcp://*************:4840\", \"\
    EQPID\": \"S720050064\", \"EQPIDBK\": \"S720050064\", \"EQPName\": \"A2-27\",\
    \ \"EQPType\": \"IMM\", \"RemoteRecipeManagementUri\": \"http://************:30063/recipe-management/v1/t3/device/model\"\
    , \"EQPVonder\": \"opcua\", \"ConfigDBUri\": \"mongodb://************:30018/\"\
    , \"CFXLocalCfgUri\": \"amqp://0.0.0.0:28885\", \"CFXLocalEndpointHandle\" : \"\
    CFX.A00.S720050064\", \"CFXRemoteCfgUri\": \"amqp://************:30031\", \"CFXRemoteEndpointHandle\"\
    : \"inline-control\", \"HSetTransmitChannels\": [ \"amqp://************:30031,event\"\
    \ ], \"HSetReceiveChannels\": []}"
---
apiVersion: "v1"
kind: "ConfigMap"
metadata:
  name: "immapi-config-6"
  namespace: "app-immapi-latest"
data:
  appsettings.json: "{ \"Logging\": { \"LogLevel\": { \"Default\": \"Information\"\
    , \"Microsoft.AspNetCore\": \"Warning\" } }, \"AllowedHosts\": \"*\", \"urls\"\
    : \"http://0.0.0.0:30076\", \"EQPCfgIP\": \"opc.tcp://*************:4840\", \"\
    EQPID\": \"S720050065\", \"EQPIDBK\": \"S720050065\", \"EQPName\": \"A2-28\",\
    \ \"EQPType\": \"IMM\", \"RemoteRecipeManagementUri\": \"http://************:30063/recipe-management/v1/t3/device/model\"\
    , \"EQPVonder\": \"opcua\", \"ConfigDBUri\": \"mongodb://************:30018/\"\
    , \"CFXLocalCfgUri\": \"amqp://0.0.0.0:28886\", \"CFXLocalEndpointHandle\" : \"\
    CFX.A00.S720050065\", \"CFXRemoteCfgUri\": \"amqp://************:30031\", \"CFXRemoteEndpointHandle\"\
    : \"inline-control\", \"HSetTransmitChannels\": [ \"amqp://************:30031,event\"\
    \ ], \"HSetReceiveChannels\": []}"