# 設備數據轉發管理系統

這是一個用於批量管理設備數據轉發到MES過工單途程功能的網頁應用系統。

## 功能特點

- 🏭 **設備管理**: 可任意添加設備卡片，配置設備ID、線名、段名、組名、站名、工單、機種等信息
- 📡 **實時監控**: 從Kafka服務器實時監控設備對應的生產數據
- 🔄 **自動轉發**: 自動轉發匹配的設備生產資料到MES過工單途程
- 🎯 **智能匹配**: 根據設備ID自動匹配需要轉發的設備生產資料
- 📊 **數據處理**: 提取產品條碼(UnitIdentifier)和生產結果(Status)等關鍵信息

## 系統架構

- **前端**: HTML + CSS + JavaScript (原生)
- **後端**: Python Flask
- **數據存儲**: JSON文件 (devices.json)
- **消息隊列**: Apache Kafka
- **MES集成**: 預留API接口 (目前為print輸出)

## 安裝和運行

### 1. 安裝依賴

```bash
pip install -r requirements.txt
```

### 2. 啟動應用

```bash
python app.py
```

應用將在 http://localhost:5000 啟動

### 3. 測試功能

運行測試數據模擬器：

```bash
python test_data_simulator.py
```

## 使用說明

### 1. 添加設備

1. 點擊"添加設備"按鈕
2. 填寫設備信息：
   - 設備ID (必須與Kafka消息中的DeviceID匹配)
   - 線名、段名、組名、站名
   - 工單、機種
3. 點擊"保存"

### 2. 管理設備

- **編輯**: 點擊設備卡片的"編輯"按鈕
- **啟用/停用**: 控制設備是否參與監控
- **刪除**: 永久刪除設備配置

### 3. Kafka監控

1. 點擊"啟動監控"按鈕開始監控Kafka消息
2. 系統會自動匹配設備ID並轉發相關數據
3. 點擊"停止監控"停止監控

## 數據流程

1. **Kafka消息接收**: 監控指定主題的Kafka消息
2. **設備ID匹配**: 提取消息中的`Data.Meta.DeviceID`與配置的設備進行匹配
3. **數據提取**: 從`MessageBody.Units`中提取產品數據
4. **MES轉發**: 將處理後的數據轉發到MES系統 (目前為控制台輸出)

## Kafka消息格式

系統處理的Kafka消息格式示例：

```json
{
  "Data": {
    "Meta": {
      "DeviceID": "SC21100803",
      "LineID": "MAG-H71",
      ...
    },
    "RawData": {
      "MessageBody": {
        "Units": [
          {
            "UnitIdentifier": "5490",
            "Status": "Pass",
            "PositionNumber": 1,
            "PositionName": "5490_1"
          }
        ]
      }
    }
  }
}
```

## 配置說明

### Kafka配置 (config.py)

```python
KAFKA_CONFIG = {
    'bootstrap_servers': '10.148.208.112:9092',
    'group_id': 'mes_forwarder_group',
    'topic_pattern': 'EAP.CZ.MAG.MAG-H71.DEVICE_CFX.CFX.Production.UnitsDeparted'
}
```

## 文件結構

```
├── app.py                    # Flask主應用
├── device_manager.py         # 設備管理模塊
├── kafka_monitor.py          # Kafka監控模塊
├── mes_forwarder.py          # MES轉發模塊
├── config.py                 # 配置文件
├── test_data_simulator.py    # 測試數據模擬器
├── requirements.txt          # Python依賴
├── devices.json              # 設備配置數據 (自動生成)
├── templates/
│   └── index.html           # 前端HTML模板
└── static/
    ├── style.css            # 樣式文件
    └── script.js            # 前端JavaScript
```

## 後續開發

- [ ] 集成真實的MES API
- [ ] 添加數據庫支持
- [ ] 實現用戶認證和權限管理
- [ ] 添加數據統計和報表功能
- [ ] 支持更多Kafka主題和消息格式
- [ ] 添加日誌記錄和錯誤處理

## 注意事項

1. 確保Kafka服務器可訪問
2. 設備ID必須與Kafka消息中的DeviceID完全匹配
3. 目前MES轉發功能為模擬輸出，實際部署時需要替換為真實API
4. 建議在生產環境中使用數據庫替代JSON文件存儲
