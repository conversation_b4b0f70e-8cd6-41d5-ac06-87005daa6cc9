{"os_name": "nt", "platform": "win32", "python_version": "3.7.9 (tags/v3.7.9:13c94747c7, Aug 17 2020, 18:01:55) [MSC v.1900 32 bit (Intel)]", "python_executable": "e:\\vscode\\tomesmo\\.venv\\Scripts\\python.exe", "working_directory": "E:\\vscode\\tomesmo", "environment_variables": {"PATH": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.python-2025.6.1-win32-x64\\python_files\\deactivate\\powershell;e:\\vscode\\tomesmo\\.venv\\Scripts;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.python-2025.6.1-win32-x64\\python_files\\deactivate\\powershell;e:\\vscode\\tomesmo\\.venv\\Scripts;E:\\python\\;E:\\python\\Scripts\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;D:\\Program Files\\PuTTY\\;C:\\Program Files\\dotnet\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;D:\\Program Files\\JetBrains\\PyCharm Community Edition 2023.1.1\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Program Files\\nodejs\\;E:\\Program Files\\PuTTY\\;%SystemRoot%\\system32;%SystemRoot%;%SystemRoot%\\System32\\Wbem;%SYSTEMROOT%\\System32\\WindowsPowerShell\\v1.0\\;%SYSTEMROOT%\\System32\\OpenSSH\\;E:\\python\\Scripts\\;E:\\python\\;D:\\Program Files (x86)\\Scripts\\;D:\\Program Files (x86)\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;D:\\Program Files\\JetBrains\\PyCharm Community Edition 2023.1.1\\bin;;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;E:\\Microsoft VS Code\\bin", "PYTHONPATH": "", "VIRTUAL_ENV": "e:\\vscode\\tomesmo\\.venv"}}