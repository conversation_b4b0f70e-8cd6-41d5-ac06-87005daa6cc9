#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
import zipfile

def restore_system():
    """恢復系統到備份狀態"""
    
    backup_zip = "backup_20250611_134658.zip"
    
    if not os.path.exists(backup_zip):
        print(f"❌ 備份文件不存在: {backup_zip}")
        return False
    
    print("🔄 開始恢復系統...")
    
    # 解壓備份文件
    with zipfile.ZipFile(backup_zip, 'r') as zipf:
        zipf.extractall("temp_restore")
    
    # 恢復文件
    restore_items = [
        'app.py',
        'device_manager.py', 
        'kafka_monitor.py',
        'kafka_config_manager.py',
        'static/',
        'templates/',
        'devices.json',
        'kafka_config.json',
        'requirements.txt'
    ]
    
    for item in restore_items:
        src = os.path.join("temp_restore", item)
        if os.path.exists(src):
            if os.path.isfile(src):
                shutil.copy2(src, item)
                print(f"✅ 已恢復文件: {item}")
            elif os.path.isdir(src):
                if os.path.exists(item):
                    shutil.rmtree(item)
                shutil.copytree(src, item)
                print(f"✅ 已恢復目錄: {item}")
    
    # 清理臨時文件
    shutil.rmtree("temp_restore")
    
    print("🎉 系統恢復完成！")
    print("請重新啟動應用: python app.py")
    
    return True

if __name__ == "__main__":
    restore_system()
