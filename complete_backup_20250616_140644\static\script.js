class DeviceManager {
    constructor() {
        this.devices = {};
        this.currentEditingDevice = null;
        this.deviceDataCache = {}; // 添加數據緩存
        this.marqueeStates = {}; // 保存滾動條狀態
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkTestMode();
        this.loadDevices();
    }

    bindEvents() {
        // 添加設備按鈕
        document.getElementById('add-device-btn').addEventListener('click', () => {
            this.showDeviceModal();
        });

        // Kafka控制按鈕
        document.getElementById('kafka-toggle-btn').addEventListener('click', () => {
            this.toggleKafkaMonitoring();
        });

        // Kafka設置按鈕
        document.getElementById('kafka-settings-btn').addEventListener('click', () => {
            this.showKafkaSettingsModal();
        });

        // RabbitMQ監控按鈕
        document.getElementById('rabbitmq-toggle-btn').addEventListener('click', () => {
            this.toggleRabbitMQMonitoring();
        });

        // RabbitMQ設置按鈕
        document.getElementById('rabbitmq-settings-btn').addEventListener('click', () => {
            this.showRabbitMQSettingsModal();
        });

        // 測試模式按鈕
        document.getElementById('test-mode-btn').addEventListener('click', () => {
            this.showTestModeModal();
        });

        // 導出配置按鈕
        document.getElementById('export-config-btn').addEventListener('click', () => {
            this.exportDevicesConfig();
        });

        // 導入配置按鈕
        document.getElementById('import-config-btn').addEventListener('click', () => {
            this.showImportModal();
        });

        // 模態框關閉
        document.querySelector('#device-modal .close').addEventListener('click', () => {
            this.hideDeviceModal();
        });

        document.querySelector('#kafka-settings-modal .close').addEventListener('click', () => {
            this.hideKafkaSettingsModal();
        });

        document.querySelector('#rabbitmq-settings-modal .close').addEventListener('click', () => {
            this.hideRabbitMQSettingsModal();
        });

        document.querySelector('#work-order-modal .close').addEventListener('click', () => {
            this.hideWorkOrderModal();
        });

        document.querySelector('#mes-errors-modal .close').addEventListener('click', () => {
            this.hideMesErrorsModal();
        });

        document.querySelector('#test-mode-modal .close').addEventListener('click', () => {
            this.hideTestModeModal();
        });

        document.querySelector('#import-config-modal .close').addEventListener('click', () => {
            this.hideImportModal();
        });

        // 取消按鈕
        document.getElementById('cancel-btn').addEventListener('click', () => {
            this.hideDeviceModal();
        });

        document.getElementById('kafka-cancel-btn').addEventListener('click', () => {
            this.hideKafkaSettingsModal();
        });

        document.getElementById('rabbitmq-cancel-btn').addEventListener('click', () => {
            this.hideRabbitMQSettingsModal();
        });

        // 表單提交
        document.getElementById('device-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveDevice();
        });

        document.getElementById('kafka-settings-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveKafkaSettings();
        });

        // RabbitMQ設置表單提交
        document.getElementById('rabbitmq-settings-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveRabbitMQSettings();
        });

        // 配置文件選擇
        document.getElementById('config-file-input').addEventListener('change', (e) => {
            this.handleConfigFileSelect(e);
        });

        // 點擊模態框外部關閉
        window.addEventListener('click', (e) => {
            const deviceModal = document.getElementById('device-modal');
            const kafkaModal = document.getElementById('kafka-settings-modal');
            const workOrderModal = document.getElementById('work-order-modal');
            const mesErrorsModal = document.getElementById('mes-errors-modal');
            const testModeModal = document.getElementById('test-mode-modal');
            const importModal = document.getElementById('import-config-modal');

            if (e.target === deviceModal) {
                this.hideDeviceModal();
            }
            if (e.target === kafkaModal) {
                this.hideKafkaSettingsModal();
            }
            if (e.target === workOrderModal) {
                this.hideWorkOrderModal();
            }
            if (e.target === mesErrorsModal) {
                this.hideMesErrorsModal();
            }
            if (e.target === testModeModal) {
                this.hideTestModeModal();
            }
            if (e.target === importModal) {
                this.hideImportModal();
            }
        });

        // 初始加載狀態
        this.loadKafkaStatus();
        this.loadRabbitMQStatus();

        // 定期更新狀態和設備數據
        setInterval(() => {
            this.loadKafkaStatus();
            this.loadRabbitMQStatus();
            this.loadDevices(); // 實時更新設備數據
        }, 5000);
    }

    async loadDevices(retryCount = 0) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超時

            const response = await fetch('/api/devices', {
                signal: controller.signal,
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const devices = await response.json();

            // 在檢查變化前，保存當前滾動狀態
            this.saveAllMarqueeStates();

            // 檢查數據是否有變化
            const hasChanges = this.hasDeviceDataChanged(devices);

            this.devices = devices;

            // 只有數據有變化時才重新渲染
            if (hasChanges) {
                this.renderDevices();
            } else {
                // 數據無變化時，只更新動態數據
                this.updateDynamicData(devices);
            }

            // 成功後重置錯誤計數
            this.consecutiveErrors = 0;

        } catch (error) {
            console.error('載入設備失敗:', error);

            // 記錄連續錯誤次數
            this.consecutiveErrors = (this.consecutiveErrors || 0) + 1;

            // 如果是網絡錯誤且重試次數少於3次，則自動重試
            if (retryCount < 3 && (error.name === 'AbortError' || error.message.includes('fetch'))) {
                console.log(`重試載入設備 (${retryCount + 1}/3)...`);
                setTimeout(() => {
                    this.loadDevices(retryCount + 1);
                }, 2000 * (retryCount + 1)); // 遞增延遲
                return;
            }

            // 只在連續錯誤超過3次時顯示錯誤消息
            if (this.consecutiveErrors >= 3) {
                this.showMessage(`載入設備失敗: ${error.message} (連續失敗 ${this.consecutiveErrors} 次)`, 'error');
            }
        }
    }

    // 檢查設備數據是否有變化
    hasDeviceDataChanged(newDevices) {
        // 如果是第一次載入
        if (Object.keys(this.deviceDataCache).length === 0) {
            this.deviceDataCache = JSON.parse(JSON.stringify(newDevices));
            return true;
        }

        // 檢查設備數量是否變化
        if (Object.keys(newDevices).length !== Object.keys(this.deviceDataCache).length) {
            this.deviceDataCache = JSON.parse(JSON.stringify(newDevices));
            return true;
        }

        // 檢查每個設備的關鍵信息是否變化
        for (const deviceId in newDevices) {
            const newDevice = newDevices[deviceId];
            const cachedDevice = this.deviceDataCache[deviceId];

            if (!cachedDevice) {
                this.deviceDataCache = JSON.parse(JSON.stringify(newDevices));
                return true;
            }

            // 檢查影響佈局的關鍵字段
            const keyFields = ['deviceId', 'lineName', 'sectionName', 'groupName', 'stationName',
                             'isActive', 'isMonitoring', 'displayWorkOrder'];

            for (const field of keyFields) {
                if (JSON.stringify(newDevice[field]) !== JSON.stringify(cachedDevice[field])) {
                    this.deviceDataCache = JSON.parse(JSON.stringify(newDevices));
                    return true;
                }
            }
        }

        return false;
    }

    // 只更新動態數據，不重新渲染整個卡片
    updateDynamicData(devices) {
        for (const deviceId in devices) {
            const device = devices[deviceId];

            // 更新統計數據
            this.updateStatistics(deviceId, device);

            // 更新生產日誌
            this.updateProductionLog(deviceId, device);

            // 更新工單進度
            this.loadWorkOrderInfo(deviceId);
        }

        // 更新緩存中的動態數據
        this.deviceDataCache = JSON.parse(JSON.stringify(devices));
    }

    // 更新統計數據
    updateStatistics(deviceId, device) {
        const productCount = device.productCount || 0;
        const forwardedCount = device.forwardedCount || 0;
        const unforwardedCount = device.unforwardedCount || 0;

        // 使用動畫更新統計數據
        this.animateStatUpdate(deviceId, 'total', productCount);
        this.animateStatUpdate(deviceId, 'forwarded', forwardedCount);
        this.animateStatUpdate(deviceId, 'unforwarded', unforwardedCount);
    }

    // 更新生產日誌
    updateProductionLog(deviceId, device) {
        const logContentElement = document.querySelector(`#devices-container .device-card:has(#stat-total-${deviceId}) .log-content`);
        const logTimeElement = document.querySelector(`#devices-container .device-card:has(#stat-total-${deviceId}) .log-time`);

        if (logContentElement && logTimeElement) {
            const periodData = device.periodData || {};
            const periodLogs = periodData.logs || [];

            let latestProductionDisplay = '';
            let lastUpdateTime = '';

            if (periodLogs.length > 0) {
                latestProductionDisplay = periodLogs.map(log => log.message).join('\n');
                lastUpdateTime = new Date(periodLogs[periodLogs.length - 1].timestamp).toLocaleString('zh-TW');
            } else {
                const lastLog = device.lastLog;
                if (lastLog && lastLog !== '暫無數據') {
                    latestProductionDisplay = lastLog;
                    lastUpdateTime = device.lastUpdateTime ?
                        new Date(device.lastUpdateTime).toLocaleString('zh-TW') : '從未更新';
                } else {
                    latestProductionDisplay = '暫無生產數據';
                    lastUpdateTime = '從未更新';
                }
            }

            logContentElement.textContent = latestProductionDisplay;
            logTimeElement.textContent = `最後更新: ${lastUpdateTime}`;
        }
    }

    renderDevices() {
        const container = document.getElementById('devices-container');
        container.innerHTML = '';

        if (Object.keys(this.devices).length === 0) {
            container.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #666;">
                    <h3>尚未添加任何設備</h3>
                    <p>點擊"添加設備"按鈕開始配置您的第一台設備</p>
                </div>
            `;
            return;
        }

        Object.values(this.devices).forEach(device => {
            const deviceCard = this.createDeviceCard(device);
            container.appendChild(deviceCard);

            // 載入工單信息
            this.loadWorkOrderInfo(device.deviceId);

            // 恢復滾動橫幅狀態
            this.restoreMarqueeState(device.deviceId);
        });
    }

    createDeviceCard(device) {
        const card = document.createElement('div');

        // 調試：檢查設備數據中是否包含MES錯誤
        console.log(`創建設備卡片 ${device.deviceId}:`, device);
        if (device.mesErrors && device.mesErrors.length > 0) {
            console.log(`設備 ${device.deviceId} 的MES錯誤:`, device.mesErrors);
        }

        // 獲取工單顯示信息
        const workOrderInfo = device.displayWorkOrder || {};
        const workOrderDisplay = workOrderInfo.workOrderNumber || '未設定';
        const modelDisplay = workOrderInfo.modelName || '未設定';
        const isMonitoring = device.isMonitoring || false;

        // 動態設置卡片樣式類
        let cardClasses = 'device-card';
        if (!device.isActive) {
            cardClasses += ' inactive';
        } else if (isMonitoring && workOrderDisplay !== '未設定') {
            cardClasses += ' mes-active';
        } else if (workOrderDisplay === '未設定') {
            cardClasses += ' no-work-order';
        }

        card.className = cardClasses;

        const productCount = device.productCount || 0;
        const forwardedCount = device.forwardedCount || 0;
        const unforwardedCount = device.unforwardedCount || 0;

        // 處理期間內的生產數據
        const periodData = device.periodData || {};
        const periodLogs = periodData.logs || [];
        const periodTotal = periodData.totalCount || 0;
        const periodForwarded = periodData.forwardedCount || 0;
        const periodUnforwarded = periodData.unforwardedCount || 0;

        // 生成最新生產數據顯示
        let latestProductionDisplay = '';
        let lastUpdateTime = '';

        if (periodLogs.length > 0) {
            // 如果本次更新期間有新數據，直接顯示所有條碼
            latestProductionDisplay = periodLogs.map(log => log.message).join('\n');
            lastUpdateTime = new Date(periodLogs[periodLogs.length - 1].timestamp).toLocaleString('zh-TW');
        } else {
            // 如果本次更新期間無新數據，顯示最後一次有數據的內容
            const lastLog = device.lastLog;
            if (lastLog && lastLog !== '暫無數據') {
                latestProductionDisplay = lastLog;
                lastUpdateTime = device.lastUpdateTime ?
                    new Date(device.lastUpdateTime).toLocaleString('zh-TW') : '從未更新';
            } else {
                latestProductionDisplay = '暫無生產數據';
                lastUpdateTime = '從未更新';
            }
        }

        card.innerHTML = `
            <div class="device-header">
                <div class="device-id">${device.lineName} 📍 ${device.stationName}（${device.deviceId}）</div>
                <div class="device-status ${isMonitoring ? 'status-active' : 'status-inactive'}">
                    ${isMonitoring ? '啟用' : '停用'}
                </div>
            </div>

            <!-- 設備基本信息 - 滾動橫幅佈局 -->
            <div class="device-info-compact">
                <!-- 線段組站滾動橫幅 -->
                <div class="info-marquee-container">
                    <div class="info-marquee">
                        <span class="marquee-content">
                            線: ${device.lineName} | 段: ${device.sectionName} | 組: ${device.groupName} | 站: ${device.stationName}
                        </span>
                    </div>
                </div>



                <!-- 工單信息固定顯示 -->
                <div class="work-order-fixed">
                    <div class="work-order-line">
                        <span class="info-label">工單:</span>
                        <span class="info-value work-order-highlight">${workOrderDisplay}</span>
                        <span class="info-label" style="margin-left: 10px;">機種:</span>
                        <span class="info-value model-highlight">${modelDisplay}</span>
                    </div>
                </div>
            </div>

            <!-- 產量統計 -->
            <div class="device-stats">
                <div class="stat-item">
                    <div class="stat-value" id="stat-total-${device.deviceId}">${productCount}</div>
                    <div class="stat-label">📊 累計產量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="stat-forwarded-${device.deviceId}">${forwardedCount}</div>
                    <div class="stat-label">✅ 已轉發</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="stat-unforwarded-${device.deviceId}">${unforwardedCount}</div>
                    <div class="stat-label">⏳ 未轉發</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="stat-completion-${device.deviceId}">0%</div>
                    <div class="stat-label">🎯 工單達成率</div>
                </div>
            </div>

            <!-- 工單進度 - 簡化版 -->
            <div class="work-order-progress-compact" id="work-order-${device.deviceId}">
                <div class="progress-content">載入中...</div>
            </div>

            <!-- 最新生產數據 -->
            <div class="device-log">
                <div class="log-header">📋 最新生產數據</div>
                <div class="log-content" style="white-space: pre-line;">${latestProductionDisplay || '暫無生產數據'}</div>
                <div class="log-time">最後更新: ${lastUpdateTime}</div>


            </div>

            <!-- 操作按鈕 - 重新排列 -->
            <div class="device-actions">
                <button class="btn btn-custom" onclick="deviceManager.manageWorkOrders('${device.deviceId}')">
                    工單管理
                </button>
                <button class="btn ${isMonitoring ? 'btn-secondary' : 'btn-success'}"
                        onclick="deviceManager.toggleDeviceMonitoring('${device.deviceId}', ${!isMonitoring})">
                    ${isMonitoring ? '停止MES' : '開始MES'}
                </button>
                <button class="btn btn-secondary" onclick="deviceManager.editDevice('${device.deviceId}')">
                    編輯設備
                </button>
                <button class="btn btn-secondary" onclick="deviceManager.deleteDevice('${device.deviceId}')">
                    刪除設備
                </button>
                <button class="btn btn-warning" onclick="deviceManager.showMesErrors('${device.deviceId}')" data-device-id="${device.deviceId}">
                    報錯信息
                </button>
            </div>
        `;

        // 優化滾動橫幅動畫
        this.optimizeMarqueeAnimation(card, device.deviceId);



        return card;
    }

    optimizeMarqueeAnimation(card, deviceId) {
        // 等待DOM渲染完成後調整動畫
        setTimeout(() => {
            const marqueeContainer = card.querySelector('.info-marquee-container');
            const marqueeContent = card.querySelector('.marquee-content');

            if (marqueeContainer && marqueeContent) {
                const containerWidth = marqueeContainer.offsetWidth;
                const contentWidth = marqueeContent.scrollWidth;

                // 統一都滾動，不管內容長短
                let duration;
                if (contentWidth <= containerWidth) {
                    // 短內容使用固定速度
                    duration = 15; // 15秒固定速度
                } else {
                    // 長內容根據長度調整速度
                    duration = Math.max(10, contentWidth / 50); // 最少10秒
                }

                marqueeContent.style.animationDuration = `${duration}s`;
                marqueeContent.style.textAlign = 'left'; // 統一左對齊

                // 保存狀態
                this.marqueeStates[deviceId] = {
                    needsScroll: true,
                    duration: duration
                };

                // 如果之前有保存的動畫進度，嘗試恢復
                const savedState = this.marqueeStates[deviceId];
                if (savedState && savedState.animationTime) {
                    marqueeContent.style.animationDelay = `-${savedState.animationTime}s`;
                }
            }
        }, 100);
    }

    // 保存滾動橫幅當前狀態
    saveMarqueeState(deviceId) {
        const marqueeContent = document.querySelector(`#devices-container .device-card:has(#stat-total-${deviceId}) .marquee-content`);
        if (marqueeContent && this.marqueeStates[deviceId] && this.marqueeStates[deviceId].needsScroll) {
            const computedStyle = window.getComputedStyle(marqueeContent);
            const animationDuration = parseFloat(computedStyle.animationDuration);
            const animationDelay = parseFloat(computedStyle.animationDelay) || 0;

            // 計算當前動畫進度
            const currentTime = (Date.now() / 1000) % animationDuration;
            this.marqueeStates[deviceId].animationTime = currentTime;
        }
    }

    // 恢復滾動橫幅狀態
    restoreMarqueeState(deviceId) {
        const savedState = this.marqueeStates[deviceId];
        if (!savedState) return;

        setTimeout(() => {
            const marqueeContent = document.querySelector(`#devices-container .device-card:has(#stat-total-${deviceId}) .marquee-content`);
            if (marqueeContent && savedState.needsScroll) {
                marqueeContent.style.animationDuration = `${savedState.duration}s`;
                if (savedState.animationTime) {
                    marqueeContent.style.animationDelay = `-${savedState.animationTime}s`;
                }
            }
        }, 150);
    }



    // 保存所有設備的滾動狀態
    saveAllMarqueeStates() {
        for (const deviceId in this.devices) {
            this.saveMarqueeState(deviceId);
        }
    }

    // 動畫更新統計數據
    animateStatUpdate(deviceId, statType, newValue) {
        const element = document.getElementById(`stat-${statType}-${deviceId}`);
        if (!element) return;

        const currentValue = parseInt(element.textContent) || 0;
        if (currentValue === newValue) return; // 數值沒變化就不更新

        // 添加更新動畫
        element.style.transform = 'scale(1.1)';
        element.style.transition = 'transform 0.3s ease';

        // 更新數值
        element.textContent = newValue;

        // 恢復原始大小
        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, 300);

        // 清除transition
        setTimeout(() => {
            element.style.transition = '';
        }, 600);
    }

    showDeviceModal(device = null) {
        this.currentEditingDevice = device;
        const modal = document.getElementById('device-modal');
        const title = document.getElementById('modal-title');
        const form = document.getElementById('device-form');

        if (device) {
            title.textContent = '編輯設備';
            this.fillForm(device);
        } else {
            title.textContent = '添加設備';
            form.reset();
        }

        modal.style.display = 'block';
    }

    hideDeviceModal() {
        document.getElementById('device-modal').style.display = 'none';
        this.currentEditingDevice = null;
    }

    fillForm(device) {
        document.getElementById('deviceId').value = device.deviceId;
        document.getElementById('lineName').value = device.lineName;
        document.getElementById('sectionName').value = device.sectionName;
        document.getElementById('groupName').value = device.groupName;
        document.getElementById('stationName').value = device.stationName;

        // 編輯時設備ID不可修改
        document.getElementById('deviceId').readOnly = !!device;
    }

    async saveDevice() {
        const formData = new FormData(document.getElementById('device-form'));
        const deviceData = Object.fromEntries(formData);

        try {
            let response;
            if (this.currentEditingDevice) {
                // 更新設備
                response = await fetch(`/api/devices/${this.currentEditingDevice.deviceId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(deviceData)
                });
            } else {
                // 添加新設備
                response = await fetch('/api/devices', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(deviceData)
                });
            }

            const result = await response.json();
            
            if (result.success) {
                this.showMessage(
                    this.currentEditingDevice ? '設備更新成功' : '設備添加成功', 
                    'success'
                );
                this.hideDeviceModal();
                this.loadDevices();
            } else {
                this.showMessage('操作失敗: ' + result.error, 'error');
            }
        } catch (error) {
            this.showMessage('操作失敗: ' + error.message, 'error');
        }
    }

    editDevice(deviceId) {
        const device = this.devices[deviceId];
        if (device) {
            this.showDeviceModal(device);
        }
    }

    async toggleDevice(deviceId) {
        const device = this.devices[deviceId];
        if (!device) return;

        try {
            const response = await fetch(`/api/devices/${deviceId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ isActive: !device.isActive })
            });

            const result = await response.json();
            
            if (result.success) {
                this.showMessage(
                    device.isActive ? '設備已停用' : '設備已啟用', 
                    'success'
                );
                this.loadDevices();
            } else {
                this.showMessage('操作失敗: ' + result.error, 'error');
            }
        } catch (error) {
            this.showMessage('操作失敗: ' + error.message, 'error');
        }
    }

    async deleteDevice(deviceId) {
        if (!confirm('確定要刪除此設備嗎？此操作無法撤銷。')) {
            return;
        }

        try {
            const response = await fetch(`/api/devices/${deviceId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('設備刪除成功', 'success');
                this.loadDevices();
            } else {
                this.showMessage('刪除失敗: ' + result.error, 'error');
            }
        } catch (error) {
            this.showMessage('刪除失敗: ' + error.message, 'error');
        }
    }

    // 顯示MES錯誤信息彈窗
    showMesErrors(deviceId) {
        const device = this.devices[deviceId];
        if (!device) {
            this.showMessage('設備不存在', 'error');
            return;
        }

        // 設置設備信息
        document.getElementById('mes-errors-device-title').textContent = `設備: ${deviceId}`;
        document.getElementById('mes-errors-device-details').textContent =
            `線名: ${device.lineName} | 段名: ${device.sectionName} | 組名: ${device.groupName} | 站名: ${device.stationName}`;

        // 渲染錯誤列表
        this.renderMesErrorsList(device.mesErrors || []);

        // 顯示彈窗
        document.getElementById('mes-errors-modal').style.display = 'block';
    }

    // 渲染MES錯誤列表
    renderMesErrorsList(mesErrors) {
        const listContainer = document.getElementById('mes-errors-list');

        if (!mesErrors || mesErrors.length === 0) {
            listContainer.innerHTML = `
                <div class="mes-errors-empty">
                    暫無MES轉發錯誤記錄
                </div>
            `;
            return;
        }

        // 按時間倒序排列（最新的在前）
        const sortedErrors = [...mesErrors].sort((a, b) =>
            new Date(b.timestamp) - new Date(a.timestamp)
        );

        listContainer.innerHTML = sortedErrors.map(error => {
            const time = new Date(error.timestamp).toLocaleString('zh-TW');
            const errorMessage = typeof error.message === 'string' ? error.message : JSON.stringify(error.message || '');
            const isSuccess = errorMessage && errorMessage.includes('✅');

            // 提取響應內容
            let responseContent = error.full_response || errorMessage || '未知錯誤';

            // 確保responseContent是字符串
            if (typeof responseContent !== 'string') {
                responseContent = JSON.stringify(responseContent);
            }

            // 如果是完整響應，提取關鍵信息
            if (responseContent.includes('原始響應:')) {
                const match = responseContent.match(/HTTP (\d+) \| 原始響應: ([^|]+)/);
                if (match) {
                    responseContent = `HTTP狀態碼: ${match[1]}\n原始響應: ${match[2].trim()}`;
                }
            }

            // 格式化請求數據
            let requestContent = '';
            if (error.request_data) {
                const requestData = error.request_data;
                requestContent = `
📤 請求信息:
URL: ${requestData.url || 'N/A'}
方法: ${requestData.method || 'N/A'}
參數: ${JSON.stringify(requestData.params || {}, null, 2)}
請求體: ${JSON.stringify(requestData.body || {}, null, 2)}

📥 響應信息:
${responseContent}`;
            } else {
                requestContent = `📥 響應信息:\n${responseContent}`;
            }

            return `
                <div class="mes-error-item">
                    <div class="mes-error-time">
                        ${time}
                        <span class="mes-error-status ${isSuccess ? 'success' : 'error'}">
                            ${isSuccess ? '成功' : '失敗'}
                        </span>
                    </div>
                    <div class="mes-error-content">${requestContent}</div>
                </div>
            `;
        }).join('');
    }



    // 隱藏MES錯誤彈窗
    hideMesErrorsModal() {
        document.getElementById('mes-errors-modal').style.display = 'none';
    }

    // 測試模式相關方法
    async checkTestMode() {
        try {
            const response = await fetch('/api/test/status');
            if (response.ok) {
                const status = await response.json();
                if (status.testModeAvailable) {
                    document.getElementById('test-mode-btn').style.display = 'inline-block';
                }
            }
        } catch (error) {
            console.log('測試模式不可用:', error);
        }
    }

    showTestModeModal() {
        this.updateTestModeStatus();
        document.getElementById('test-mode-modal').style.display = 'block';
    }

    hideTestModeModal() {
        document.getElementById('test-mode-modal').style.display = 'none';
    }

    async updateTestModeStatus() {
        try {
            const response = await fetch('/api/test/status');
            if (response.ok) {
                const status = await response.json();
                document.getElementById('current-device-count').textContent = status.currentDeviceCount;
                document.getElementById('test-mode-status').textContent =
                    status.testModeAvailable ? '可用' : '不可用';

                // 更新模擬數據生成狀態
                if (status.fakeDataAvailable) {
                    document.getElementById('fake-data-status').textContent = '可用';
                    document.getElementById('fake-data-controls').style.display = 'block';

                    const fakeDataStatus = status.fakeDataStatus || {};
                    document.getElementById('active-devices-count').textContent = fakeDataStatus.active_devices_count || 0;
                    document.getElementById('generation-status').textContent =
                        fakeDataStatus.is_running ? '運行中' : '已停止';

                    // 更新按鈕狀態
                    const startBtn = document.getElementById('start-fake-data-btn');
                    const stopBtn = document.getElementById('stop-fake-data-btn');

                    if (fakeDataStatus.is_running) {
                        startBtn.style.display = 'none';
                        stopBtn.style.display = 'inline-block';
                    } else {
                        startBtn.style.display = 'inline-block';
                        stopBtn.style.display = 'none';
                    }
                } else {
                    document.getElementById('fake-data-status').textContent = '不可用';
                    document.getElementById('fake-data-controls').style.display = 'none';
                }
            }
        } catch (error) {
            document.getElementById('test-mode-status').textContent = '檢查失敗';
            document.getElementById('fake-data-status').textContent = '檢查失敗';
        }
    }

    async loadTestDevices() {
        const deviceCount = parseInt(document.getElementById('test-device-count').value);

        if (!confirm(`確定要載入 ${deviceCount} 個測試設備嗎？這將清空現有的所有設備數據。`)) {
            return;
        }

        try {
            const response = await fetch('/api/test/load-devices', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ deviceCount })
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage(`✅ ${result.message}`, 'success');
                this.hideTestModeModal();
                // 重新載入設備數據
                setTimeout(() => {
                    this.loadDevices();
                }, 1000);
            } else {
                this.showMessage(`❌ ${result.error}`, 'error');
            }
        } catch (error) {
            this.showMessage(`❌ 載入測試設備失敗: ${error.message}`, 'error');
        }
    }

    async clearAllDevices() {
        if (!confirm('確定要清空所有設備嗎？此操作不可恢復。')) {
            return;
        }

        try {
            const response = await fetch('/api/test/clear-devices', {
                method: 'POST'
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('✅ 已清空所有設備', 'success');
                this.hideTestModeModal();
                // 重新載入設備數據
                setTimeout(() => {
                    this.loadDevices();
                }, 1000);
            } else {
                this.showMessage(`❌ ${result.error}`, 'error');
            }
        } catch (error) {
            this.showMessage(`❌ 清空設備失敗: ${error.message}`, 'error');
        }
    }

    async startFakeDataGeneration() {
        const interval = parseInt(document.getElementById('fake-data-interval').value);

        try {
            const response = await fetch('/api/test/fake-data/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ interval })
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage(`✅ ${result.message}`, 'success');
                // 更新狀態
                setTimeout(() => {
                    this.updateTestModeStatus();
                }, 500);
            } else {
                this.showMessage(`❌ ${result.error}`, 'error');
            }
        } catch (error) {
            this.showMessage(`❌ 啟動模擬數據生成失敗: ${error.message}`, 'error');
        }
    }

    async stopFakeDataGeneration() {
        try {
            const response = await fetch('/api/test/fake-data/stop', {
                method: 'POST'
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('✅ 模擬數據生成已停止', 'success');
                // 更新狀態
                setTimeout(() => {
                    this.updateTestModeStatus();
                }, 500);
            } else {
                this.showMessage(`❌ ${result.error}`, 'error');
            }
        } catch (error) {
            this.showMessage(`❌ 停止模擬數據生成失敗: ${error.message}`, 'error');
        }
    }



    async toggleKafkaMonitoring() {
        const toggleBtn = document.getElementById('kafka-toggle-btn');
        const isRunning = toggleBtn.textContent === '停止監控';

        try {
            toggleBtn.disabled = true;

            let enableAllDevices = false;

            // 如果是啟動監控，詢問是否同時啟用所有設備的MES轉發
            if (!isRunning) {
                enableAllDevices = confirm('是否一起啟用所有設備的MES上報功能？\n\n點擊"確定"將同時啟用所有有可用工單的設備的MES轉發功能\n點擊"取消"僅啟動Kafka監控');
            }

            const endpoint = isRunning ? '/api/kafka/stop' : '/api/kafka/start';
            const requestBody = isRunning ? {} : { enableAllDevices: enableAllDevices };

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            const result = await response.json();

            if (result.success) {
                let message = result.message;
                if (!isRunning && enableAllDevices) {
                    if (result.enabledDevicesCount > 0) {
                        message += `，已同時啟用 ${result.enabledDevicesCount} 個設備的MES轉發功能`;
                    } else {
                        message += '，但沒有設備可以啟用MES轉發（無可用工單）';
                    }
                }
                this.showMessage(message, 'success');
                this.loadKafkaStatus();
                // 重新載入設備列表以更新狀態
                this.loadDevices();
            } else {
                this.showMessage('操作失敗: ' + result.error, 'error');
            }
        } catch (error) {
            this.showMessage('操作失敗: ' + error.message, 'error');
        } finally {
            toggleBtn.disabled = false;
        }
    }

    async toggleDeviceMonitoring(deviceId, isMonitoring) {
        try {
            // 如果要啟用監控，先檢查是否有可用工單
            if (isMonitoring) {
                const checkResponse = await fetch(`/api/devices/${deviceId}/can-start-mes`);
                const checkResult = await checkResponse.json();
                if (!checkResult.canStart) {
                    this.showMessage(`無法開啟MES轉發: ${checkResult.message}`, 'error');
                    return;
                }
            }

            const response = await fetch(`/api/devices/${deviceId}/monitoring`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ isMonitoring })
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage(result.message, 'success');
                this.loadDevices(); // 重新載入設備數據
            } else {
                this.showMessage('操作失敗: ' + result.error, 'error');
            }
        } catch (error) {
            this.showMessage('操作失敗: ' + error.message, 'error');
        }
    }

    async showKafkaSettingsModal() {
        const modal = document.getElementById('kafka-settings-modal');

        // 載入當前配置
        try {
            const response = await fetch('/api/kafka/config');
            const config = await response.json();

            document.getElementById('bootstrap_servers').value = config.bootstrap_servers || '';
            document.getElementById('group_id').value = config.group_id || '';
            document.getElementById('factory').value = config.factory || '';
            document.getElementById('mfg_plant_code').value = config.mfg_plant_code || '';
            document.getElementById('message_type').value = config.message_type || 'DEVICE_CFX';
            document.getElementById('message_name').value = config.message_name || 'CFX.Production.UnitsDeparted';
        } catch (error) {
            this.showMessage('載入Kafka配置失敗: ' + error.message, 'error');
        }

        modal.style.display = 'block';
    }

    hideKafkaSettingsModal() {
        document.getElementById('kafka-settings-modal').style.display = 'none';
    }

    async saveKafkaSettings() {
        const formData = new FormData(document.getElementById('kafka-settings-form'));
        const configData = Object.fromEntries(formData);

        try {
            const response = await fetch('/api/kafka/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(configData)
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('Kafka配置保存成功', 'success');
                this.hideKafkaSettingsModal();
            } else {
                this.showMessage('保存失敗: ' + result.error, 'error');
            }
        } catch (error) {
            this.showMessage('保存失敗: ' + error.message, 'error');
        }
    }

    showMessage(text, type = 'info') {
        const messageEl = document.getElementById('message');
        messageEl.textContent = text;
        messageEl.className = `message ${type}`;
        messageEl.classList.add('show');

        // 清除之前的定時器
        if (this.messageTimer) {
            clearTimeout(this.messageTimer);
        }

        // 3秒後開始淡出
        this.messageTimer = setTimeout(() => {
            messageEl.classList.remove('show');

            // 再等待動畫完成後完全隱藏
            setTimeout(() => {
                messageEl.style.display = 'none';
                messageEl.textContent = '';
            }, 300); // 等待CSS過渡動畫完成
        }, 3000);

        // 確保消息框可見
        messageEl.style.display = 'block';
    }

    // 添加統計數據更新動畫
    animateStatUpdate(deviceId, statType, newValue) {
        const statElement = document.getElementById(`stat-${statType}-${deviceId}`);
        if (statElement) {
            const oldValue = parseInt(statElement.textContent) || 0;
            if (newValue !== oldValue) {
                statElement.classList.add('updated');
                statElement.textContent = newValue;

                setTimeout(() => {
                    statElement.classList.remove('updated');
                }, 600);
            }
        }
    }

    // 添加工單進度更新動畫
    animateProgressUpdate(deviceId, progress, targetQuantity) {
        const progressElement = document.querySelector(`#work-order-${deviceId} .progress-fill`);
        if (progressElement) {
            const completionRate = Math.round((progress / targetQuantity) * 100);
            progressElement.style.width = `${Math.min(completionRate, 100)}%`;

            if (completionRate >= 100) {
                progressElement.classList.add('completed');
            }
        }
    }

    // 工單管理方法
    async loadWorkOrderInfo(deviceId) {
        try {
            const response = await fetch(`/api/devices/${deviceId}/work-orders`);
            const data = await response.json();

            const workOrderElement = document.getElementById(`work-order-${deviceId}`);
            if (!workOrderElement) return;

            const contentElement = workOrderElement.querySelector('.progress-content');

            // 更新工单达成率显示
            const completionElement = document.getElementById(`stat-completion-${deviceId}`);

            if (data.currentWorkOrder) {
                const workOrder = data.currentWorkOrder;
                const completionRate = Math.round(workOrder.completionRate || 0);
                const isCompleted = completionRate >= 100;
                const progressFillClass = isCompleted ? 'progress-fill completed' : 'progress-fill';

                // 更新工单达成率
                if (completionElement) {
                    completionElement.textContent = `${completionRate}%`;
                    completionElement.className = 'stat-value';
                    if (isCompleted) {
                        completionElement.classList.add('updated');
                    }
                }

                // 添加進度條動畫效果
                const progressEmoji = completionRate >= 100 ? '🎉' :
                                    completionRate >= 75 ? '🚀' :
                                    completionRate >= 50 ? '⚡' :
                                    completionRate >= 25 ? '📈' : '🔄';

                contentElement.innerHTML = `
                    <div class="progress-bar-compact">
                        <div class="${progressFillClass}" style="width: ${Math.min(completionRate, 100)}%"></div>
                    </div>
                    <div class="progress-text-compact">
                        ${progressEmoji} ${workOrder.progress}/${workOrder.targetQuantity} (${completionRate}%)
                        ${isCompleted ? ' ✅' : ''}
                    </div>
                `;
            } else {
                // 无工单时显示0%
                if (completionElement) {
                    completionElement.textContent = '0%';
                    completionElement.className = 'stat-value';
                }

                contentElement.innerHTML = `
                    <div class="progress-empty">
                        <span class="empty-message">暫無工單</span>
                    </div>
                `;
            }
        } catch (error) {
            console.error('載入工單信息失敗:', error);
            const workOrderElement = document.getElementById(`work-order-${deviceId}`);
            if (workOrderElement) {
                const contentElement = workOrderElement.querySelector('.work-order-content');
                contentElement.innerHTML = '<div class="work-order-error">載入失敗</div>';
            }
        }
    }

    async manageWorkOrders(deviceId) {
        this.currentManagingDevice = deviceId;
        const modal = document.getElementById('work-order-modal');

        // 載入當前工單列表
        try {
            const response = await fetch(`/api/devices/${deviceId}/work-orders`);
            const data = await response.json();

            this.currentWorkOrders = data.workOrders || [];
            this.renderWorkOrderList();

        } catch (error) {
            this.showMessage('載入工單列表失敗: ' + error.message, 'error');
            this.currentWorkOrders = [];
        }

        modal.style.display = 'block';
    }

    hideWorkOrderModal() {
        document.getElementById('work-order-modal').style.display = 'none';
        this.currentManagingDevice = null;
        this.currentWorkOrders = [];
    }

    renderWorkOrderList() {
        const container = document.getElementById('work-order-list');
        container.innerHTML = '';

        if (this.currentWorkOrders.length === 0) {
            container.innerHTML = `
                <div class="work-order-empty-list">
                    <p>暫無工單</p>
                    <p>點擊"添加工單"開始配置</p>
                </div>
            `;
            return;
        }

        this.currentWorkOrders.forEach((workOrder, index) => {
            const item = document.createElement('div');
            item.className = `work-order-item ${workOrder.status}`;
            item.setAttribute('data-index', index);

            const statusText = {
                'pending': '等待中',
                'active': '進行中',
                'completed': '已完成'
            }[workOrder.status] || workOrder.status;

            const priorityText = index === 0 ? '🔥 優先' : `${index + 1}`;

            item.innerHTML = `
                <div class="work-order-item-header">
                    <div class="work-order-priority">${priorityText}</div>
                    <span class="work-order-number">${workOrder.workOrderNumber}</span>
                    <div class="drag-handle">⋮⋮</div>
                </div>
                <div class="work-order-item-body">
                    <span class="work-order-model">機種: ${workOrder.modelName || 'N/A'}</span>
                    <span class="work-order-target">目標: ${workOrder.targetQuantity}</span>
                    <span class="work-order-cavity">模穴數: ${workOrder.cavityCount || 1}</span>
                    <span class="work-order-forwarded">已轉發: ${workOrder.mesForwardedCount || 0}</span>
                    <span class="work-order-completion">達成率: ${(workOrder.completionRate || 0).toFixed(1)}%</span>
                    ${workOrder.description ? `<span class="work-order-description">${workOrder.description}</span>` : ''}
                    <div class="work-order-item-actions">
                        <button class="btn btn-sm btn-secondary" onclick="deviceManager.removeWorkOrder(${index})">×</button>
                    </div>
                </div>
            `;

            container.appendChild(item);
        });

        // 初始化拖拽排序
        this.initSortable();
    }

    addWorkOrder() {
        const workOrderNumber = document.getElementById('new-work-order-number').value.trim();
        const modelName = document.getElementById('new-model-name').value.trim();
        const targetQuantity = parseInt(document.getElementById('new-target-quantity').value);
        const description = document.getElementById('new-description').value.trim();
        const cavityCount = parseInt(document.getElementById('new-cavity-count').value) || 1;

        if (!workOrderNumber) {
            this.showMessage('請輸入工單號', 'error');
            return;
        }

        if (!modelName) {
            this.showMessage('請輸入機種名', 'error');
            return;
        }

        if (!targetQuantity || targetQuantity <= 0) {
            this.showMessage('請輸入有效的目標數量', 'error');
            return;
        }

        // 檢查工單號是否重複
        if (this.currentWorkOrders.some(wo => wo.workOrderNumber === workOrderNumber)) {
            this.showMessage('工單號已存在', 'error');
            return;
        }

        const newWorkOrder = {
            workOrderNumber,
            modelName,
            targetQuantity,
            description,
            cavityCount,
            status: 'pending'
        };

        // 新工單添加到列表末尾
        this.currentWorkOrders.push(newWorkOrder);
        this.renderWorkOrderList();

        // 清空表單
        document.getElementById('new-work-order-number').value = '';
        document.getElementById('new-model-name').value = '';
        document.getElementById('new-target-quantity').value = '';
        document.getElementById('new-description').value = '';
        document.getElementById('new-cavity-count').value = '1';
    }

    removeWorkOrder(index) {
        if (confirm('確定要刪除此工單嗎？')) {
            this.currentWorkOrders.splice(index, 1);
            this.renderWorkOrderList();
        }
    }



    initSortable() {
        const container = document.getElementById('work-order-list');
        if (!container || this.currentWorkOrders.length === 0) return;

        let draggedElement = null;
        let draggedIndex = null;

        // 為每個工單項目添加拖拽事件
        container.querySelectorAll('.work-order-item').forEach((item, index) => {
            item.draggable = true;

            item.addEventListener('dragstart', (e) => {
                draggedElement = item;
                draggedIndex = index;
                item.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
            });

            item.addEventListener('dragend', (e) => {
                item.classList.remove('dragging');
                draggedElement = null;
                draggedIndex = null;
            });

            item.addEventListener('dragover', (e) => {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
            });

            item.addEventListener('drop', (e) => {
                e.preventDefault();
                if (draggedElement && draggedIndex !== null) {
                    const targetIndex = index;
                    if (draggedIndex !== targetIndex) {
                        // 重新排序數組
                        const draggedItem = this.currentWorkOrders.splice(draggedIndex, 1)[0];
                        this.currentWorkOrders.splice(targetIndex, 0, draggedItem);

                        // 重新渲染列表
                        this.renderWorkOrderList();
                    }
                }
            });
        });
    }

    async saveWorkOrders() {
        if (!this.currentManagingDevice) return;

        try {
            const response = await fetch(`/api/devices/${this.currentManagingDevice}/work-orders`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ workOrders: this.currentWorkOrders })
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('工單保存成功', 'success');
                this.hideWorkOrderModal();
                this.loadDevices(); // 重新載入設備數據
            } else {
                this.showMessage('保存失敗: ' + result.error, 'error');
            }
        } catch (error) {
            this.showMessage('保存失敗: ' + error.message, 'error');
        }
    }

    // 清除MES錯誤記錄
    async clearMesErrors(deviceId) {
        try {
            const response = await fetch(`/api/devices/${deviceId}/mes-errors`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                // 更新本地設備數據
                const device = this.devices[deviceId];
                if (device) {
                    device.mesErrors = [];
                }

                // 立即更新UI
                this.updateMesErrors(deviceId, []);
                this.updateMesResponse(deviceId, []);

                this.showMessage('MES響應記錄已清除', 'success');
            } else {
                this.showMessage('清除失敗: ' + result.error, 'error');
            }
        } catch (error) {
            this.showMessage('清除失敗: ' + error.message, 'error');
        }
    }

    // 切換設備監控狀態（別名方法）
    async toggleMonitoring(deviceId) {
        const device = this.devices[deviceId];
        if (!device) return;

        const newStatus = !device.isMonitoring;
        await this.toggleDeviceMonitoring(deviceId, newStatus);
    }

    // Kafka監控管理
    async loadKafkaStatus() {
        try {
            const response = await fetch('/api/kafka/status');
            const status = await response.json();

            const statusText = document.getElementById('kafka-status-text');
            const toggleBtn = document.getElementById('kafka-toggle-btn');
            const rabbitmqBtn = document.getElementById('rabbitmq-toggle-btn');

            if (status.isRunning) {
                statusText.textContent = 'EAP監控狀態: 運行中';
                statusText.style.color = '#28a745';
                toggleBtn.textContent = '停止EAP監控';
                toggleBtn.className = 'btn btn-secondary';
                // 禁用RabbitMQ按钮
                rabbitmqBtn.disabled = true;
                rabbitmqBtn.className = 'btn btn-monitor';
                rabbitmqBtn.style.opacity = '0.65';
            } else {
                statusText.textContent = 'EAP監控狀態: 已停止';
                statusText.style.color = '#6c757d';
                toggleBtn.textContent = '啟動EAP監控';
                toggleBtn.className = 'btn btn-monitor';
                // 检查RabbitMQ状态来决定是否启用按钮
                this.checkAndUpdateButtonStates();
            }
        } catch (error) {
            console.error('載入Kafka狀態失敗:', error);
            const statusText = document.getElementById('kafka-status-text');
            statusText.textContent = 'EAP監控狀態: 連接失敗';
            statusText.style.color = '#dc3545';
        }
    }

    // 检查并更新按钮状态，确保互斥
    async checkAndUpdateButtonStates() {
        try {
            const [kafkaResponse, rabbitmqResponse] = await Promise.all([
                fetch('/api/kafka/status'),
                fetch('/api/rabbitmq/status')
            ]);

            const kafkaStatus = await kafkaResponse.json();
            const rabbitmqStatus = await rabbitmqResponse.json();

            const kafkaBtn = document.getElementById('kafka-toggle-btn');
            const rabbitmqBtn = document.getElementById('rabbitmq-toggle-btn');

            // 如果两个都没有运行，恢复初始状态
            if (!kafkaStatus.is_monitoring && !rabbitmqStatus.is_monitoring) {
                kafkaBtn.disabled = false;
                kafkaBtn.style.opacity = '1';
                rabbitmqBtn.disabled = false;
                rabbitmqBtn.style.opacity = '1';
            }
        } catch (error) {
            console.error('检查按钮状态失败:', error);
        }
    }

    async toggleKafkaMonitoring() {
        const toggleBtn = document.getElementById('kafka-toggle-btn');
        const isCurrentlyRunning = toggleBtn.textContent.includes('停止');

        try {
            toggleBtn.disabled = true;

            if (isCurrentlyRunning) {
                // 停止監控
                const response = await fetch('/api/kafka/stop', {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    this.showMessage(result.message, 'success');
                } else {
                    this.showMessage(result.error || '停止EAP監控失敗', 'error');
                }
            } else {
                // 啟動監控 - 顯示確認對話框
                const enableAllDevices = confirm('是否同時啟用所有有可用工單的設備的MES轉發？');

                const response = await fetch('/api/kafka/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        enableAllDevices: enableAllDevices
                    })
                });
                const result = await response.json();

                if (result.success) {
                    let message = result.message;
                    if (enableAllDevices && result.enabledDevicesCount > 0) {
                        message += `，同時啟用了 ${result.enabledDevicesCount} 個設備的MES轉發`;
                    }
                    this.showMessage(message, 'success');
                    this.loadDevices(); // 重新載入設備狀態
                } else {
                    this.showMessage(result.error || '啟動Kafka監控失敗', 'error');
                }
            }

            // 更新狀態
            this.loadKafkaStatus();
            this.loadRabbitMQStatus(); // 同时更新RabbitMQ状态以处理互斥

        } catch (error) {
            console.error('切換Kafka監控狀態失敗:', error);
            this.showMessage('操作失敗: ' + error.message, 'error');
        } finally {
            toggleBtn.disabled = false;
        }
    }

    // Kafka設置管理
    async showKafkaSettingsModal() {
        try {
            // 載入當前配置
            const response = await fetch('/api/kafka/config');
            const config = await response.json();

            // 填充表單
            document.getElementById('bootstrap_servers').value = config.bootstrap_servers || '';
            document.getElementById('group_id').value = config.group_id || '';
            document.getElementById('factory').value = config.factory || '';
            document.getElementById('mfg_plant_code').value = config.mfg_plant_code || '';
            document.getElementById('message_type').value = config.message_type || '';
            document.getElementById('message_name').value = config.message_name || '';

            // 顯示模態框
            document.getElementById('kafka-settings-modal').style.display = 'block';

        } catch (error) {
            console.error('載入Kafka配置失敗:', error);
            this.showMessage('載入Kafka配置失敗', 'error');
        }
    }

    hideKafkaSettingsModal() {
        document.getElementById('kafka-settings-modal').style.display = 'none';
    }

    async saveKafkaSettings() {
        const formData = new FormData(document.getElementById('kafka-settings-form'));
        const config = Object.fromEntries(formData);

        try {
            const response = await fetch('/api/kafka/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage(result.message, 'success');
                this.hideKafkaSettingsModal();
            } else {
                this.showMessage(result.error || '保存Kafka配置失敗', 'error');
            }
        } catch (error) {
            console.error('保存Kafka配置失敗:', error);
            this.showMessage('保存Kafka配置失敗', 'error');
        }
    }

    // RabbitMQ監控管理
    async loadRabbitMQStatus() {
        try {
            const response = await fetch('/api/rabbitmq/status');
            const status = await response.json();

            const statusText = document.getElementById('rabbitmq-status-text');
            const toggleBtn = document.getElementById('rabbitmq-toggle-btn');
            const kafkaBtn = document.getElementById('kafka-toggle-btn');

            if (status.is_monitoring) {
                statusText.textContent = `SIE監控狀態: 運行中 (${status.device_count}個設備)`;
                statusText.style.color = '#28a745';
                toggleBtn.textContent = '停止SIE監控';
                toggleBtn.className = 'btn btn-secondary';
                // 禁用Kafka按钮
                kafkaBtn.disabled = true;
                kafkaBtn.className = 'btn btn-monitor';
                kafkaBtn.style.opacity = '0.65';
            } else {
                statusText.textContent = 'SIE監控狀態: 已停止';
                statusText.style.color = '#6c757d';
                toggleBtn.textContent = '啟動SIE監控';
                toggleBtn.className = 'btn btn-monitor';
                // 检查Kafka状态来决定是否启用按钮
                this.checkAndUpdateButtonStates();
            }
        } catch (error) {
            console.error('載入RabbitMQ狀態失敗:', error);
            const statusText = document.getElementById('rabbitmq-status-text');
            statusText.textContent = 'SIE監控狀態: 連接失敗';
            statusText.style.color = '#dc3545';
        }
    }

    async toggleRabbitMQMonitoring() {
        const toggleBtn = document.getElementById('rabbitmq-toggle-btn');
        const isCurrentlyRunning = toggleBtn.textContent.includes('停止');

        try {
            toggleBtn.disabled = true;

            if (isCurrentlyRunning) {
                // 停止監控
                const response = await fetch('/api/rabbitmq/stop', {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    this.showMessage(result.message, 'success');
                } else {
                    this.showMessage(result.error || '停止SIE監控失敗', 'error');
                }
            } else {
                // 啟動監控 - 顯示確認對話框
                const enableAllDevices = confirm('是否同時啟用所有有可用工單的設備的MES轉發？');

                const response = await fetch('/api/rabbitmq/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        enableAllDevices: enableAllDevices
                    })
                });
                const result = await response.json();

                if (result.success) {
                    let message = result.message;
                    if (enableAllDevices && result.enabledDevicesCount > 0) {
                        message += `，同時啟用了 ${result.enabledDevicesCount} 個設備的MES轉發`;
                    }
                    this.showMessage(message, 'success');
                    this.loadDevices(); // 重新載入設備狀態
                } else {
                    this.showMessage(result.error || '啟動RabbitMQ監控失敗', 'error');
                }
            }

            // 更新狀態
            this.loadRabbitMQStatus();
            this.loadKafkaStatus(); // 同时更新Kafka状态以处理互斥

        } catch (error) {
            console.error('切換RabbitMQ監控狀態失敗:', error);
            this.showMessage('操作失敗: ' + error.message, 'error');
        } finally {
            toggleBtn.disabled = false;
        }
    }

    // RabbitMQ設置管理
    async showRabbitMQSettingsModal() {
        try {
            // 載入當前配置
            const response = await fetch('/api/rabbitmq/config');
            const config = await response.json();

            // 填充表單
            document.getElementById('rabbitmq_host').value = config.host || '';
            document.getElementById('rabbitmq_port').value = config.port || '';
            document.getElementById('rabbitmq_username').value = config.username || '';
            document.getElementById('rabbitmq_password').value = config.password || '';
            document.getElementById('rabbitmq_exchange').value = config.exchange_name || '';
            document.getElementById('rabbitmq_exchange_type').value = config.exchange_type || '';
            document.getElementById('rabbitmq_routing_pattern').value = config.routing_key_pattern || '';

            // 顯示模態框
            document.getElementById('rabbitmq-settings-modal').style.display = 'block';

        } catch (error) {
            console.error('載入RabbitMQ配置失敗:', error);
            this.showMessage('載入RabbitMQ配置失敗', 'error');
        }
    }

    hideRabbitMQSettingsModal() {
        document.getElementById('rabbitmq-settings-modal').style.display = 'none';
    }

    async saveRabbitMQSettings() {
        const formData = new FormData(document.getElementById('rabbitmq-settings-form'));
        const config = Object.fromEntries(formData);

        // 轉換端口為數字
        config.port = parseInt(config.port);

        try {
            const response = await fetch('/api/rabbitmq/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage(result.message, 'success');
                this.hideRabbitMQSettingsModal();
            } else {
                this.showMessage(result.error || '保存RabbitMQ配置失敗', 'error');
            }
        } catch (error) {
            console.error('保存RabbitMQ配置失敗:', error);
            this.showMessage('保存RabbitMQ配置失敗', 'error');
        }
    }

    // 導出設備配置
    exportDevicesConfig() {
        try {
            const config = {
                exportTime: new Date().toISOString(),
                version: '0.0.1-beta-1',
                deviceCount: Object.keys(this.devices).length,
                devices: this.devices
            };

            const dataStr = JSON.stringify(config, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `device-config-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            link.click();

            this.showMessage(`✅ 設備配置已導出 (${config.deviceCount} 台設備)`, 'success');
        } catch (error) {
            this.showMessage(`❌ 導出配置失敗: ${error.message}`, 'error');
        }
    }

    // 顯示導入配置模態框
    showImportModal() {
        document.getElementById('import-config-modal').style.display = 'block';
        // 重置表單
        document.getElementById('config-file-input').value = '';
        document.getElementById('backup-before-import').checked = true;
        document.getElementById('import-preview').style.display = 'none';
        document.getElementById('confirm-import-btn').disabled = true;
    }

    // 隱藏導入配置模態框
    hideImportModal() {
        document.getElementById('import-config-modal').style.display = 'none';
    }

    // 處理配置文件選擇
    handleConfigFileSelect(event) {
        const file = event.target.files[0];
        if (!file) {
            document.getElementById('import-preview').style.display = 'none';
            document.getElementById('confirm-import-btn').disabled = true;
            return;
        }

        if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
            this.showMessage('❌ 請選擇JSON格式的配置文件', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const config = JSON.parse(e.target.result);
                this.validateAndPreviewConfig(config);
            } catch (error) {
                this.showMessage('❌ 配置文件格式錯誤，請檢查JSON格式', 'error');
                document.getElementById('import-preview').style.display = 'none';
                document.getElementById('confirm-import-btn').disabled = true;
            }
        };
        reader.readAsText(file);
    }

    // 驗證並預覽配置
    validateAndPreviewConfig(config) {
        try {
            // 驗證配置結構
            if (!config.devices || typeof config.devices !== 'object') {
                throw new Error('配置文件缺少有效的設備數據');
            }

            const deviceCount = Object.keys(config.devices).length;
            if (deviceCount === 0) {
                throw new Error('配置文件中沒有設備數據');
            }

            // 顯示預覽
            const previewContent = document.getElementById('import-preview-content');
            previewContent.innerHTML = `
                <p><strong>導出時間:</strong> ${config.exportTime || '未知'}</p>
                <p><strong>版本:</strong> ${config.version || '未知'}</p>
                <p><strong>設備數量:</strong> ${deviceCount} 台</p>
                <p><strong>設備列表:</strong></p>
                <ul style="margin: 5px 0; padding-left: 20px;">
                    ${Object.keys(config.devices).slice(0, 10).map(id =>
                        `<li>${id} (${config.devices[id].line || '未知'}-${config.devices[id].station || '未知'})</li>`
                    ).join('')}
                    ${deviceCount > 10 ? `<li>... 還有 ${deviceCount - 10} 台設備</li>` : ''}
                </ul>
            `;

            document.getElementById('import-preview').style.display = 'block';
            document.getElementById('confirm-import-btn').disabled = false;
            this.importConfig = config;

        } catch (error) {
            this.showMessage(`❌ 配置驗證失敗: ${error.message}`, 'error');
            document.getElementById('import-preview').style.display = 'none';
            document.getElementById('confirm-import-btn').disabled = true;
        }
    }

    // 確認導入配置
    async confirmImport() {
        if (!this.importConfig) {
            this.showMessage('❌ 沒有可導入的配置', 'error');
            return;
        }

        try {
            // 檢查是否需要備份
            const shouldBackup = document.getElementById('backup-before-import').checked;
            if (shouldBackup && Object.keys(this.devices).length > 0) {
                this.exportDevicesConfig();
                await new Promise(resolve => setTimeout(resolve, 1000)); // 等待下載完成
            }

            // 導入配置
            const response = await fetch('/api/devices/import', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(this.importConfig.devices)
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage(`✅ 配置導入成功 (${Object.keys(this.importConfig.devices).length} 台設備)`, 'success');
                this.hideImportModal();
                // 重新載入設備數據
                setTimeout(() => {
                    this.loadDevices();
                }, 1000);
            } else {
                this.showMessage(`❌ ${result.error}`, 'error');
            }
        } catch (error) {
            this.showMessage(`❌ 導入配置失敗: ${error.message}`, 'error');
        }
    }
}



// 初始化設備管理器
const deviceManager = new DeviceManager();

// 全局函數
function editDevice(deviceId) {
    deviceManager.editDevice(deviceId);
}

function deleteDevice(deviceId) {
    deviceManager.deleteDevice(deviceId);
}

function toggleMonitoring(deviceId) {
    deviceManager.toggleMonitoring(deviceId);
}

function manageWorkOrders(deviceId) {
    workOrderManager.openWorkOrderModal(deviceId);
}

function clearMesErrors(deviceId) {
    deviceManager.clearMesErrors(deviceId);
}
