#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試數據生成器
用於生成大量設備數據進行性能測試
"""

import json
import random
import uuid
from datetime import datetime, timedelta

class TestDataGenerator:
    def __init__(self):
        self.lines = ['A1-01', 'A1-02', 'A2-01', 'A2-02', 'B1-01', 'B1-02', 'B2-01', 'B2-02']
        self.sections = ['INJECTION', 'ASSEMBLY', 'TESTING', 'PACKAGING']
        self.groups = ['INJECTION', 'ASSEMBLY', 'TEST', 'PACK']
        self.stations = ['STATION_01', 'STATION_02', 'STATION_03', 'STATION_04']
        self.models = ['4102161400', '4102161401', '4102161402', '4102161403', '4102161404']
        
    def generate_device_id(self, index):
        """生成設備ID"""
        return f"S72005{index:04d}"
    
    def generate_work_orders(self, device_id, count=3):
        """為設備生成工單"""
        work_orders = []
        for i in range(count):
            work_order = {
                'workOrderNumber': f"WO{random.randint(100000, 999999)}",
                'targetQuantity': random.randint(50, 500),
                'modelName': random.choice(self.models),
                'description': f"測試工單 {i+1}",
                'cavityCount': random.randint(1, 4),  # 模穴數1-4
                'createdAt': (datetime.now() - timedelta(days=random.randint(0, 30))).isoformat(),
                'status': random.choice(['pending', 'active', 'completed']),
                'mesForwardedCount': random.randint(0, 100),
                'completionRate': round(random.uniform(0, 100), 1)
            }
            work_orders.append(work_order)
        return work_orders
    
    def generate_device(self, index):
        """生成單個設備數據"""
        device_id = self.generate_device_id(index)
        line = random.choice(self.lines)
        section = random.choice(self.sections)
        group = random.choice(self.groups)
        station = random.choice(self.stations)
        
        # 生成統計數據
        product_count = random.randint(0, 1000)
        forwarded_count = random.randint(0, product_count)
        unforwarded_count = product_count - forwarded_count
        
        # 生成工單
        work_orders = self.generate_work_orders(device_id)
        current_work_order_index = random.randint(0, len(work_orders)-1) if work_orders else 0
        
        device = {
            'deviceId': device_id,
            'lineName': line,
            'sectionName': section,
            'groupName': group,
            'stationName': station,
            'isActive': True,  # 測試環境中所有設備都應該是活躍的
            'isMonitoring': random.choice([True, False]),
            'productCount': product_count,
            'forwardedCount': forwarded_count,
            'unforwardedCount': unforwarded_count,
            'lastLog': f"測試產品條碼-{uuid.uuid4().hex[:8]}",
            'lastUpdateTime': datetime.now().isoformat(),
            'createdAt': (datetime.now() - timedelta(days=random.randint(1, 365))).isoformat(),
            'workOrders': work_orders,
            'currentWorkOrder': current_work_order_index,
            'currentWorkOrderProgress': random.randint(0, 100),
            'displayWorkOrder': work_orders[current_work_order_index] if work_orders else {},
            'mesErrors': self.generate_mes_errors(device_id),
            'recentLogs': self.generate_recent_logs(device_id)
        }
        
        return device
    
    def generate_mes_errors(self, device_id, count=None):
        """生成MES錯誤記錄"""
        if count is None:
            count = random.randint(0, 5)  # 0-5個錯誤
            
        errors = []
        error_types = [
            "❌ 失敗: STATION NOT EXIST OR INVALID",
            "❌ 失敗: MO_HAS_FULL", 
            "❌ 失敗: NETWORK_TIMEOUT",
            "✅ 成功: OK"
        ]
        
        for i in range(count):
            error = {
                'timestamp': (datetime.now() - timedelta(minutes=random.randint(1, 1440))).isoformat(),
                'message': random.choice(error_types),
                'full_response': f"HTTP 200 | 測試響應數據 {i+1}",
                'card_response': f"HTTP 200 | 測試響應 {i+1}",
                'request_data': {
                    'url': 'http://test.mes.api/endpoint',
                    'method': 'POST',
                    'device_id': device_id
                }
            }
            errors.append(error)
            
        return errors
    
    def generate_recent_logs(self, device_id, count=None):
        """生成最近日誌"""
        if count is None:
            count = random.randint(5, 20)
            
        logs = []
        for i in range(count):
            log = {
                'message': f"測試條碼-{uuid.uuid4().hex[:8]}",
                'timestamp': (datetime.now() - timedelta(minutes=random.randint(1, 60))).isoformat(),
                'unitCount': 1,
                'actualProductCount': random.randint(1, 4),
                'cavityCount': random.randint(1, 4),
                'isForwarded': random.choice([True, False])
            }
            logs.append(log)
            
        return logs
    
    def generate_devices(self, count):
        """生成指定數量的設備"""
        devices = {}
        for i in range(1, count + 1):
            device = self.generate_device(i)
            devices[device['deviceId']] = device
            
        return devices
    
    def save_test_devices(self, count, filename=None):
        """生成並保存測試設備數據"""
        if filename is None:
            filename = f"test_devices_{count}.json"
            
        devices = self.generate_devices(count)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(devices, f, ensure_ascii=False, indent=2)
            
        print(f"✅ 已生成 {count} 個測試設備，保存到 {filename}")
        return devices
    
    def load_test_devices(self, filename):
        """載入測試設備數據"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                devices = json.load(f)
            print(f"✅ 已載入 {len(devices)} 個測試設備從 {filename}")
            return devices
        except FileNotFoundError:
            print(f"❌ 文件 {filename} 不存在")
            return {}
        except Exception as e:
            print(f"❌ 載入測試數據失敗: {e}")
            return {}

def main():
    """主函數 - 生成測試數據"""
    generator = TestDataGenerator()
    
    # 生成不同數量的測試數據
    test_counts = [40, 50, 60, 70, 100]
    
    for count in test_counts:
        print(f"\n🔄 生成 {count} 個設備的測試數據...")
        generator.save_test_devices(count, f"test_devices_{count}.json")
        
    print("\n🎉 所有測試數據生成完成！")
    print("使用方法:")
    print("1. python test_data_generator.py  # 生成測試數據")
    print("2. 在app.py中調用load_test_devices()載入測試數據")

if __name__ == "__main__":
    main()
