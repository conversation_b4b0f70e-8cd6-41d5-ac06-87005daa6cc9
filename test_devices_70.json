{"S720050001": {"deviceId": "S720050001", "lineName": "B2-01", "sectionName": "TESTING", "groupName": "PACK", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 7, "forwardedCount": 1, "unforwardedCount": 6, "lastLog": "測試產品條碼-b886f600", "lastUpdateTime": "2025-06-13T16:59:31.496371", "createdAt": "2024-09-07T16:59:31.496371", "workOrders": [{"workOrderNumber": "WO388301", "targetQuantity": 410, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-15T16:59:31.496371", "status": "completed", "mesForwardedCount": 78, "completionRate": 77.6}, {"workOrderNumber": "WO847659", "targetQuantity": 109, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-13T16:59:31.496371", "status": "completed", "mesForwardedCount": 69, "completionRate": 48.9}, {"workOrderNumber": "WO243948", "targetQuantity": 329, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-12T16:59:31.496371", "status": "active", "mesForwardedCount": 13, "completionRate": 0.2}], "currentWorkOrder": 0, "currentWorkOrderProgress": 78, "displayWorkOrder": {"workOrderNumber": "WO388301", "targetQuantity": 410, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-15T16:59:31.496371", "status": "completed", "mesForwardedCount": 78, "completionRate": 77.6}, "mesErrors": [{"timestamp": "2025-06-13T11:06:31.496371", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050001"}}], "recentLogs": [{"message": "測試條碼-437ae4ab", "timestamp": "2025-06-13T16:36:31.496371", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-e667b2d1", "timestamp": "2025-06-13T16:21:31.496371", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-b4193bf6", "timestamp": "2025-06-13T16:55:31.496371", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-c197505c", "timestamp": "2025-06-13T16:16:31.496371", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-7d0d3375", "timestamp": "2025-06-13T16:57:31.496371", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-9b01925d", "timestamp": "2025-06-13T16:45:31.496371", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-6f473d0d", "timestamp": "2025-06-13T16:04:31.496371", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-5ca90d4c", "timestamp": "2025-06-13T16:05:31.496371", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-b7a9dc32", "timestamp": "2025-06-13T16:50:31.496371", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-58af0112", "timestamp": "2025-06-13T16:11:31.496371", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-01b05a00", "timestamp": "2025-06-13T16:17:31.496371", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}]}, "S720050002": {"deviceId": "S720050002", "lineName": "A1-01", "sectionName": "INJECTION", "groupName": "INJECTION", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 238, "forwardedCount": 214, "unforwardedCount": 24, "lastLog": "測試產品條碼-e5a3abac", "lastUpdateTime": "2025-06-13T16:59:31.496371", "createdAt": "2024-07-10T16:59:31.496371", "workOrders": [{"workOrderNumber": "WO321574", "targetQuantity": 134, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-28T16:59:31.496371", "status": "active", "mesForwardedCount": 56, "completionRate": 39.3}, {"workOrderNumber": "WO378554", "targetQuantity": 101, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-01T16:59:31.496371", "status": "active", "mesForwardedCount": 79, "completionRate": 43.5}, {"workOrderNumber": "WO116011", "targetQuantity": 381, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-20T16:59:31.496371", "status": "pending", "mesForwardedCount": 6, "completionRate": 17.6}], "currentWorkOrder": 0, "currentWorkOrderProgress": 13, "displayWorkOrder": {"workOrderNumber": "WO321574", "targetQuantity": 134, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-28T16:59:31.496371", "status": "active", "mesForwardedCount": 56, "completionRate": 39.3}, "mesErrors": [{"timestamp": "2025-06-13T08:57:31.496371", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050002"}}], "recentLogs": [{"message": "測試條碼-430b58d5", "timestamp": "2025-06-13T16:30:31.496371", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-90fb3d46", "timestamp": "2025-06-13T16:36:31.497347", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-7e3ed448", "timestamp": "2025-06-13T16:06:31.497347", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-2b2e5ce1", "timestamp": "2025-06-13T16:43:31.497347", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-66d9a22c", "timestamp": "2025-06-13T16:05:31.497347", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-d40745be", "timestamp": "2025-06-13T16:45:31.497347", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-de43a72a", "timestamp": "2025-06-13T16:15:31.497347", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}]}, "S720050003": {"deviceId": "S720050003", "lineName": "A2-01", "sectionName": "PACKAGING", "groupName": "INJECTION", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 620, "forwardedCount": 290, "unforwardedCount": 330, "lastLog": "測試產品條碼-022bbf1b", "lastUpdateTime": "2025-06-13T16:59:31.497347", "createdAt": "2025-03-04T16:59:31.497347", "workOrders": [{"workOrderNumber": "WO258985", "targetQuantity": 52, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-01T16:59:31.497347", "status": "completed", "mesForwardedCount": 76, "completionRate": 48.5}, {"workOrderNumber": "WO792822", "targetQuantity": 60, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-19T16:59:31.497347", "status": "completed", "mesForwardedCount": 17, "completionRate": 39.6}, {"workOrderNumber": "WO553616", "targetQuantity": 298, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-15T16:59:31.497347", "status": "pending", "mesForwardedCount": 45, "completionRate": 46.2}], "currentWorkOrder": 2, "currentWorkOrderProgress": 36, "displayWorkOrder": {"workOrderNumber": "WO553616", "targetQuantity": 298, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-15T16:59:31.497347", "status": "pending", "mesForwardedCount": 45, "completionRate": 46.2}, "mesErrors": [{"timestamp": "2025-06-13T16:13:31.497347", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050003"}}, {"timestamp": "2025-06-13T04:25:31.497347", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050003"}}, {"timestamp": "2025-06-13T01:38:31.497442", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050003"}}], "recentLogs": [{"message": "測試條碼-cf1ddfbc", "timestamp": "2025-06-13T16:51:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-c804dbb5", "timestamp": "2025-06-13T16:36:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-fe04f37e", "timestamp": "2025-06-13T15:59:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-1b81e096", "timestamp": "2025-06-13T16:36:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-73a1e347", "timestamp": "2025-06-13T16:05:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-64c3abbb", "timestamp": "2025-06-13T16:07:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-4e555b75", "timestamp": "2025-06-13T16:31:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-82c528b1", "timestamp": "2025-06-13T16:45:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-2f0ab127", "timestamp": "2025-06-13T16:33:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-dc26bccd", "timestamp": "2025-06-13T16:31:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-78caf0ab", "timestamp": "2025-06-13T16:02:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-b4970192", "timestamp": "2025-06-13T16:19:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-73f3e941", "timestamp": "2025-06-13T16:02:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-eb979178", "timestamp": "2025-06-13T15:59:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-fdda6be6", "timestamp": "2025-06-13T16:32:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-3908df25", "timestamp": "2025-06-13T16:26:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-e97018a6", "timestamp": "2025-06-13T16:39:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-0795673f", "timestamp": "2025-06-13T16:13:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}]}, "S720050004": {"deviceId": "S720050004", "lineName": "B2-02", "sectionName": "PACKAGING", "groupName": "INJECTION", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 671, "forwardedCount": 293, "unforwardedCount": 378, "lastLog": "測試產品條碼-ce736858", "lastUpdateTime": "2025-06-13T16:59:31.497442", "createdAt": "2024-08-08T16:59:31.497442", "workOrders": [{"workOrderNumber": "WO584033", "targetQuantity": 484, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-27T16:59:31.497442", "status": "completed", "mesForwardedCount": 16, "completionRate": 0.0}, {"workOrderNumber": "WO343883", "targetQuantity": 255, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-13T16:59:31.497442", "status": "pending", "mesForwardedCount": 14, "completionRate": 15.8}, {"workOrderNumber": "WO646064", "targetQuantity": 362, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-03T16:59:31.497442", "status": "active", "mesForwardedCount": 5, "completionRate": 56.2}], "currentWorkOrder": 0, "currentWorkOrderProgress": 72, "displayWorkOrder": {"workOrderNumber": "WO584033", "targetQuantity": 484, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-27T16:59:31.497442", "status": "completed", "mesForwardedCount": 16, "completionRate": 0.0}, "mesErrors": [{"timestamp": "2025-06-13T15:09:31.497442", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050004"}}], "recentLogs": [{"message": "測試條碼-5bd68eb0", "timestamp": "2025-06-13T16:11:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-13de48da", "timestamp": "2025-06-13T16:35:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-b2876803", "timestamp": "2025-06-13T16:14:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-c1877735", "timestamp": "2025-06-13T16:50:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-3364b77e", "timestamp": "2025-06-13T16:06:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-d67a3383", "timestamp": "2025-06-13T16:01:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-8dcf3ebc", "timestamp": "2025-06-13T16:24:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-595fd5d8", "timestamp": "2025-06-13T16:40:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-4648207b", "timestamp": "2025-06-13T16:38:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-79d49e87", "timestamp": "2025-06-13T16:05:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ee997bf1", "timestamp": "2025-06-13T16:42:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-b61aae32", "timestamp": "2025-06-13T16:58:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-c5c8d3c7", "timestamp": "2025-06-13T16:38:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-c8c96991", "timestamp": "2025-06-13T16:37:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-ec796dc3", "timestamp": "2025-06-13T16:41:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-06c71478", "timestamp": "2025-06-13T16:29:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-041359bd", "timestamp": "2025-06-13T16:39:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}]}, "S720050005": {"deviceId": "S720050005", "lineName": "A2-02", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 925, "forwardedCount": 39, "unforwardedCount": 886, "lastLog": "測試產品條碼-ea32c4b8", "lastUpdateTime": "2025-06-13T16:59:31.497442", "createdAt": "2024-12-02T16:59:31.497442", "workOrders": [{"workOrderNumber": "WO695354", "targetQuantity": 67, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-21T16:59:31.497442", "status": "completed", "mesForwardedCount": 19, "completionRate": 5.2}, {"workOrderNumber": "WO769731", "targetQuantity": 349, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-23T16:59:31.497442", "status": "completed", "mesForwardedCount": 28, "completionRate": 60.6}, {"workOrderNumber": "WO544899", "targetQuantity": 206, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-30T16:59:31.497442", "status": "pending", "mesForwardedCount": 52, "completionRate": 60.8}], "currentWorkOrder": 2, "currentWorkOrderProgress": 83, "displayWorkOrder": {"workOrderNumber": "WO544899", "targetQuantity": 206, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-30T16:59:31.497442", "status": "pending", "mesForwardedCount": 52, "completionRate": 60.8}, "mesErrors": [{"timestamp": "2025-06-13T03:30:31.497442", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050005"}}], "recentLogs": [{"message": "測試條碼-6fa04bbd", "timestamp": "2025-06-13T16:43:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-599a2c69", "timestamp": "2025-06-13T16:14:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-5fe43c69", "timestamp": "2025-06-13T16:38:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-78e6156f", "timestamp": "2025-06-13T16:18:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-ae211905", "timestamp": "2025-06-13T16:28:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-fa9701f6", "timestamp": "2025-06-13T16:53:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f39de56f", "timestamp": "2025-06-13T16:46:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}]}, "S720050006": {"deviceId": "S720050006", "lineName": "A1-01", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 712, "forwardedCount": 94, "unforwardedCount": 618, "lastLog": "測試產品條碼-fa0f7d3a", "lastUpdateTime": "2025-06-13T16:59:31.497442", "createdAt": "2024-07-27T16:59:31.497442", "workOrders": [{"workOrderNumber": "WO807682", "targetQuantity": 486, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-02T16:59:31.497442", "status": "pending", "mesForwardedCount": 61, "completionRate": 91.9}, {"workOrderNumber": "WO996145", "targetQuantity": 196, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-14T16:59:31.497442", "status": "completed", "mesForwardedCount": 46, "completionRate": 0.9}, {"workOrderNumber": "WO628996", "targetQuantity": 99, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-21T16:59:31.497442", "status": "pending", "mesForwardedCount": 13, "completionRate": 72.2}], "currentWorkOrder": 2, "currentWorkOrderProgress": 29, "displayWorkOrder": {"workOrderNumber": "WO628996", "targetQuantity": 99, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-21T16:59:31.497442", "status": "pending", "mesForwardedCount": 13, "completionRate": 72.2}, "mesErrors": [{"timestamp": "2025-06-13T04:20:31.497442", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050006"}}], "recentLogs": [{"message": "測試條碼-a0fd3224", "timestamp": "2025-06-13T16:35:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-eec65001", "timestamp": "2025-06-13T16:34:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-1aa86658", "timestamp": "2025-06-13T16:33:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-b8744770", "timestamp": "2025-06-13T16:51:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-7271f542", "timestamp": "2025-06-13T16:04:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-23cd359f", "timestamp": "2025-06-13T16:32:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-617ec4b8", "timestamp": "2025-06-13T16:10:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-1799fbb2", "timestamp": "2025-06-13T16:08:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-8613dd82", "timestamp": "2025-06-13T16:46:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-4ace7b4d", "timestamp": "2025-06-13T16:00:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-52e4b7ca", "timestamp": "2025-06-13T16:38:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-be0f9680", "timestamp": "2025-06-13T16:30:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-4c817622", "timestamp": "2025-06-13T16:01:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-85b35c64", "timestamp": "2025-06-13T16:33:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-fcd265d7", "timestamp": "2025-06-13T16:47:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-5da1c36e", "timestamp": "2025-06-13T16:26:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f50bb591", "timestamp": "2025-06-13T16:14:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-d163ff25", "timestamp": "2025-06-13T16:49:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-735261a6", "timestamp": "2025-06-13T16:04:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-6afec2a4", "timestamp": "2025-06-13T16:23:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}]}, "S720050007": {"deviceId": "S720050007", "lineName": "A1-01", "sectionName": "INJECTION", "groupName": "PACK", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 559, "forwardedCount": 126, "unforwardedCount": 433, "lastLog": "測試產品條碼-57765a8a", "lastUpdateTime": "2025-06-13T16:59:31.497442", "createdAt": "2024-11-25T16:59:31.497442", "workOrders": [{"workOrderNumber": "WO756108", "targetQuantity": 318, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-20T16:59:31.497442", "status": "pending", "mesForwardedCount": 11, "completionRate": 97.9}, {"workOrderNumber": "WO163233", "targetQuantity": 105, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-15T16:59:31.497442", "status": "active", "mesForwardedCount": 48, "completionRate": 15.6}, {"workOrderNumber": "WO246656", "targetQuantity": 176, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-12T16:59:31.497442", "status": "pending", "mesForwardedCount": 35, "completionRate": 54.3}], "currentWorkOrder": 2, "currentWorkOrderProgress": 97, "displayWorkOrder": {"workOrderNumber": "WO246656", "targetQuantity": 176, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-12T16:59:31.497442", "status": "pending", "mesForwardedCount": 35, "completionRate": 54.3}, "mesErrors": [{"timestamp": "2025-06-13T09:45:31.497442", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050007"}}, {"timestamp": "2025-06-12T23:53:31.497442", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050007"}}, {"timestamp": "2025-06-12T17:09:31.497442", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050007"}}, {"timestamp": "2025-06-12T20:04:31.497442", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050007"}}, {"timestamp": "2025-06-12T19:59:31.497442", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050007"}}], "recentLogs": [{"message": "測試條碼-c5a99c83", "timestamp": "2025-06-13T16:58:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-cc073d89", "timestamp": "2025-06-13T16:37:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f9928e10", "timestamp": "2025-06-13T16:32:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-2e98fb43", "timestamp": "2025-06-13T16:44:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-2cc329f0", "timestamp": "2025-06-13T16:46:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-5c54ea33", "timestamp": "2025-06-13T16:37:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-205a157d", "timestamp": "2025-06-13T16:03:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-7862a82b", "timestamp": "2025-06-13T16:35:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-9648f3c7", "timestamp": "2025-06-13T16:54:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-765bbe84", "timestamp": "2025-06-13T16:04:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-eb61a890", "timestamp": "2025-06-13T16:11:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-3a789488", "timestamp": "2025-06-13T16:44:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}]}, "S720050008": {"deviceId": "S720050008", "lineName": "A1-02", "sectionName": "PACKAGING", "groupName": "ASSEMBLY", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 95, "forwardedCount": 71, "unforwardedCount": 24, "lastLog": "測試產品條碼-23c6553a", "lastUpdateTime": "2025-06-13T16:59:31.497442", "createdAt": "2024-12-03T16:59:31.497442", "workOrders": [{"workOrderNumber": "WO716564", "targetQuantity": 164, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-25T16:59:31.497442", "status": "completed", "mesForwardedCount": 13, "completionRate": 66.5}, {"workOrderNumber": "WO572527", "targetQuantity": 245, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-02T16:59:31.497442", "status": "active", "mesForwardedCount": 39, "completionRate": 90.7}, {"workOrderNumber": "WO125654", "targetQuantity": 236, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-18T16:59:31.497442", "status": "active", "mesForwardedCount": 98, "completionRate": 1.9}], "currentWorkOrder": 2, "currentWorkOrderProgress": 93, "displayWorkOrder": {"workOrderNumber": "WO125654", "targetQuantity": 236, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-18T16:59:31.497442", "status": "active", "mesForwardedCount": 98, "completionRate": 1.9}, "mesErrors": [{"timestamp": "2025-06-13T13:19:31.497442", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050008"}}, {"timestamp": "2025-06-13T11:45:31.497442", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050008"}}, {"timestamp": "2025-06-13T09:56:31.497442", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050008"}}, {"timestamp": "2025-06-12T17:20:31.497442", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050008"}}], "recentLogs": [{"message": "測試條碼-e6655bb8", "timestamp": "2025-06-13T16:30:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-7e154649", "timestamp": "2025-06-13T16:44:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-6333d154", "timestamp": "2025-06-13T16:55:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-6409f829", "timestamp": "2025-06-13T16:56:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-8abe2456", "timestamp": "2025-06-13T16:12:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-127111d7", "timestamp": "2025-06-13T16:56:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}]}, "S720050009": {"deviceId": "S720050009", "lineName": "A1-01", "sectionName": "INJECTION", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 88, "forwardedCount": 47, "unforwardedCount": 41, "lastLog": "測試產品條碼-eddecaa1", "lastUpdateTime": "2025-06-13T16:59:31.497442", "createdAt": "2025-03-27T16:59:31.497442", "workOrders": [{"workOrderNumber": "WO930480", "targetQuantity": 207, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-02T16:59:31.497442", "status": "active", "mesForwardedCount": 5, "completionRate": 15.1}, {"workOrderNumber": "WO682594", "targetQuantity": 485, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-09T16:59:31.497442", "status": "completed", "mesForwardedCount": 73, "completionRate": 86.6}, {"workOrderNumber": "WO160057", "targetQuantity": 476, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-31T16:59:31.497442", "status": "completed", "mesForwardedCount": 13, "completionRate": 18.6}], "currentWorkOrder": 0, "currentWorkOrderProgress": 60, "displayWorkOrder": {"workOrderNumber": "WO930480", "targetQuantity": 207, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-02T16:59:31.497442", "status": "active", "mesForwardedCount": 5, "completionRate": 15.1}, "mesErrors": [{"timestamp": "2025-06-12T20:10:31.497442", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050009"}}, {"timestamp": "2025-06-13T08:27:31.497442", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050009"}}, {"timestamp": "2025-06-12T20:18:31.497442", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050009"}}, {"timestamp": "2025-06-12T18:57:31.497442", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050009"}}, {"timestamp": "2025-06-12T17:17:31.497442", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050009"}}], "recentLogs": [{"message": "測試條碼-b3ded3fb", "timestamp": "2025-06-13T16:53:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d908448d", "timestamp": "2025-06-13T16:57:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-1e515159", "timestamp": "2025-06-13T16:19:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-9461b684", "timestamp": "2025-06-13T16:46:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-73f38cf6", "timestamp": "2025-06-13T16:54:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-8238a2d7", "timestamp": "2025-06-13T16:16:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-1ee940e9", "timestamp": "2025-06-13T16:45:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-2881043a", "timestamp": "2025-06-13T16:16:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}]}, "S720050010": {"deviceId": "S720050010", "lineName": "A1-01", "sectionName": "INJECTION", "groupName": "ASSEMBLY", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 327, "forwardedCount": 229, "unforwardedCount": 98, "lastLog": "測試產品條碼-4bd77ace", "lastUpdateTime": "2025-06-13T16:59:31.497442", "createdAt": "2025-04-27T16:59:31.497442", "workOrders": [{"workOrderNumber": "WO450011", "targetQuantity": 425, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-21T16:59:31.497442", "status": "completed", "mesForwardedCount": 23, "completionRate": 57.0}, {"workOrderNumber": "WO480626", "targetQuantity": 428, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-25T16:59:31.497442", "status": "active", "mesForwardedCount": 27, "completionRate": 55.7}, {"workOrderNumber": "WO131631", "targetQuantity": 398, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-13T16:59:31.497442", "status": "completed", "mesForwardedCount": 59, "completionRate": 39.0}], "currentWorkOrder": 0, "currentWorkOrderProgress": 42, "displayWorkOrder": {"workOrderNumber": "WO450011", "targetQuantity": 425, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-21T16:59:31.497442", "status": "completed", "mesForwardedCount": 23, "completionRate": 57.0}, "mesErrors": [{"timestamp": "2025-06-13T01:06:31.497442", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050010"}}, {"timestamp": "2025-06-13T15:16:31.497442", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050010"}}, {"timestamp": "2025-06-12T18:42:31.497442", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050010"}}, {"timestamp": "2025-06-13T06:55:31.497442", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050010"}}], "recentLogs": [{"message": "測試條碼-31eca85e", "timestamp": "2025-06-13T16:30:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-06656ad0", "timestamp": "2025-06-13T16:30:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-5e2a6e27", "timestamp": "2025-06-13T16:11:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-0ad8ca19", "timestamp": "2025-06-13T16:57:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ec55dfd0", "timestamp": "2025-06-13T16:36:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-60c102d7", "timestamp": "2025-06-13T16:20:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-84aa21b4", "timestamp": "2025-06-13T16:51:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-abf8c27f", "timestamp": "2025-06-13T16:22:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-ce3cb3a7", "timestamp": "2025-06-13T16:56:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-5fba1ca9", "timestamp": "2025-06-13T16:24:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-f5d38c2f", "timestamp": "2025-06-13T16:16:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-fb0f8c30", "timestamp": "2025-06-13T16:25:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a8d79ab1", "timestamp": "2025-06-13T16:26:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-0f390dda", "timestamp": "2025-06-13T16:09:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}]}, "S720050011": {"deviceId": "S720050011", "lineName": "B1-01", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_03", "isActive": true, "isMonitoring": true, "productCount": 997, "forwardedCount": 575, "unforwardedCount": 422, "lastLog": "測試產品條碼-6f18adc6", "lastUpdateTime": "2025-06-13T16:59:31.497442", "createdAt": "2024-10-03T16:59:31.497442", "workOrders": [{"workOrderNumber": "WO193319", "targetQuantity": 304, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-04T16:59:31.497442", "status": "active", "mesForwardedCount": 18, "completionRate": 45.8}, {"workOrderNumber": "WO280915", "targetQuantity": 416, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-23T16:59:31.497442", "status": "completed", "mesForwardedCount": 0, "completionRate": 58.9}, {"workOrderNumber": "WO506180", "targetQuantity": 168, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-26T16:59:31.497442", "status": "pending", "mesForwardedCount": 43, "completionRate": 8.5}], "currentWorkOrder": 0, "currentWorkOrderProgress": 88, "displayWorkOrder": {"workOrderNumber": "WO193319", "targetQuantity": 304, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-04T16:59:31.497442", "status": "active", "mesForwardedCount": 18, "completionRate": 45.8}, "mesErrors": [{"timestamp": "2025-06-13T02:38:31.497442", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050011"}}, {"timestamp": "2025-06-13T03:45:31.497442", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050011"}}, {"timestamp": "2025-06-12T22:21:31.497442", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050011"}}, {"timestamp": "2025-06-13T14:21:31.497442", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050011"}}], "recentLogs": [{"message": "測試條碼-fd5fdfc8", "timestamp": "2025-06-13T16:00:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-b3d2bfa2", "timestamp": "2025-06-13T16:00:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-07860405", "timestamp": "2025-06-13T16:50:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-016e6ccb", "timestamp": "2025-06-13T16:15:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-b1573185", "timestamp": "2025-06-13T16:50:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-b69f4d0a", "timestamp": "2025-06-13T16:05:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-f5b5d3fa", "timestamp": "2025-06-13T16:13:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-38b1c85e", "timestamp": "2025-06-13T16:28:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}]}, "S720050012": {"deviceId": "S720050012", "lineName": "B1-01", "sectionName": "ASSEMBLY", "groupName": "INJECTION", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 814, "forwardedCount": 216, "unforwardedCount": 598, "lastLog": "測試產品條碼-b6915b4d", "lastUpdateTime": "2025-06-13T16:59:31.497442", "createdAt": "2024-12-21T16:59:31.497442", "workOrders": [{"workOrderNumber": "WO193234", "targetQuantity": 156, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-07T16:59:31.497442", "status": "completed", "mesForwardedCount": 75, "completionRate": 52.9}, {"workOrderNumber": "WO110368", "targetQuantity": 415, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-24T16:59:31.497442", "status": "completed", "mesForwardedCount": 29, "completionRate": 33.4}, {"workOrderNumber": "WO413718", "targetQuantity": 335, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-21T16:59:31.497442", "status": "active", "mesForwardedCount": 64, "completionRate": 94.9}], "currentWorkOrder": 0, "currentWorkOrderProgress": 87, "displayWorkOrder": {"workOrderNumber": "WO193234", "targetQuantity": 156, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-07T16:59:31.497442", "status": "completed", "mesForwardedCount": 75, "completionRate": 52.9}, "mesErrors": [{"timestamp": "2025-06-13T16:35:31.497442", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050012"}}, {"timestamp": "2025-06-13T01:07:31.497442", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050012"}}], "recentLogs": [{"message": "測試條碼-62270de7", "timestamp": "2025-06-13T16:20:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-675b6406", "timestamp": "2025-06-13T16:54:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-e52375d9", "timestamp": "2025-06-13T16:55:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-f9ff36ab", "timestamp": "2025-06-13T16:09:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-ddddd983", "timestamp": "2025-06-13T16:55:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-8c215ded", "timestamp": "2025-06-13T16:46:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-afb82aae", "timestamp": "2025-06-13T16:26:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-264da06e", "timestamp": "2025-06-13T16:33:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}]}, "S720050013": {"deviceId": "S720050013", "lineName": "B2-01", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 776, "forwardedCount": 650, "unforwardedCount": 126, "lastLog": "測試產品條碼-79b7d765", "lastUpdateTime": "2025-06-13T16:59:31.497442", "createdAt": "2024-08-02T16:59:31.497442", "workOrders": [{"workOrderNumber": "WO311826", "targetQuantity": 355, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-01T16:59:31.497442", "status": "completed", "mesForwardedCount": 88, "completionRate": 89.3}, {"workOrderNumber": "WO263407", "targetQuantity": 103, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-16T16:59:31.497442", "status": "pending", "mesForwardedCount": 5, "completionRate": 46.5}, {"workOrderNumber": "WO454576", "targetQuantity": 466, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-29T16:59:31.497442", "status": "completed", "mesForwardedCount": 63, "completionRate": 51.3}], "currentWorkOrder": 0, "currentWorkOrderProgress": 29, "displayWorkOrder": {"workOrderNumber": "WO311826", "targetQuantity": 355, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-01T16:59:31.497442", "status": "completed", "mesForwardedCount": 88, "completionRate": 89.3}, "mesErrors": [{"timestamp": "2025-06-13T11:47:31.497442", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050013"}}, {"timestamp": "2025-06-12T22:05:31.497442", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050013"}}, {"timestamp": "2025-06-13T02:05:31.497442", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050013"}}], "recentLogs": [{"message": "測試條碼-46b60b11", "timestamp": "2025-06-13T16:24:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-1f534cd6", "timestamp": "2025-06-13T16:08:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-4b4943ed", "timestamp": "2025-06-13T16:00:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-53a66570", "timestamp": "2025-06-13T16:52:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-bc31454e", "timestamp": "2025-06-13T16:29:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}]}, "S720050014": {"deviceId": "S720050014", "lineName": "B1-01", "sectionName": "TESTING", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 216, "forwardedCount": 72, "unforwardedCount": 144, "lastLog": "測試產品條碼-4d7fccde", "lastUpdateTime": "2025-06-13T16:59:31.497442", "createdAt": "2024-12-11T16:59:31.497442", "workOrders": [{"workOrderNumber": "WO987346", "targetQuantity": 104, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-29T16:59:31.497442", "status": "completed", "mesForwardedCount": 11, "completionRate": 17.7}, {"workOrderNumber": "WO765069", "targetQuantity": 213, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-18T16:59:31.497442", "status": "completed", "mesForwardedCount": 99, "completionRate": 62.7}, {"workOrderNumber": "WO315100", "targetQuantity": 149, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-06T16:59:31.497442", "status": "active", "mesForwardedCount": 41, "completionRate": 59.8}], "currentWorkOrder": 1, "currentWorkOrderProgress": 86, "displayWorkOrder": {"workOrderNumber": "WO765069", "targetQuantity": 213, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-18T16:59:31.497442", "status": "completed", "mesForwardedCount": 99, "completionRate": 62.7}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-f1a3a061", "timestamp": "2025-06-13T16:40:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-2def847a", "timestamp": "2025-06-13T16:51:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-54c4957f", "timestamp": "2025-06-13T16:09:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-3f6307c6", "timestamp": "2025-06-13T15:59:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-a619dda4", "timestamp": "2025-06-13T16:15:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-8578963b", "timestamp": "2025-06-13T16:05:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-de5189eb", "timestamp": "2025-06-13T16:31:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-e16aef31", "timestamp": "2025-06-13T16:41:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-eb82559a", "timestamp": "2025-06-13T16:15:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-ede4a283", "timestamp": "2025-06-13T16:00:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-39b25b61", "timestamp": "2025-06-13T16:48:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-384a6444", "timestamp": "2025-06-13T16:24:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a9a76b47", "timestamp": "2025-06-13T16:15:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-6d246f0e", "timestamp": "2025-06-13T16:11:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-d7c51f4f", "timestamp": "2025-06-13T16:02:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-7e3d9ea0", "timestamp": "2025-06-13T16:18:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-ad579a0d", "timestamp": "2025-06-13T16:44:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-3511d924", "timestamp": "2025-06-13T16:46:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-ec9936d3", "timestamp": "2025-06-13T16:03:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}]}, "S720050015": {"deviceId": "S720050015", "lineName": "A2-02", "sectionName": "INJECTION", "groupName": "PACK", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 727, "forwardedCount": 115, "unforwardedCount": 612, "lastLog": "測試產品條碼-b01fa88c", "lastUpdateTime": "2025-06-13T16:59:31.497442", "createdAt": "2025-04-18T16:59:31.497442", "workOrders": [{"workOrderNumber": "WO975924", "targetQuantity": 239, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-17T16:59:31.497442", "status": "pending", "mesForwardedCount": 68, "completionRate": 77.1}, {"workOrderNumber": "WO936526", "targetQuantity": 320, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-15T16:59:31.497442", "status": "active", "mesForwardedCount": 48, "completionRate": 74.2}, {"workOrderNumber": "WO912783", "targetQuantity": 493, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-14T16:59:31.497442", "status": "active", "mesForwardedCount": 45, "completionRate": 9.7}], "currentWorkOrder": 0, "currentWorkOrderProgress": 52, "displayWorkOrder": {"workOrderNumber": "WO975924", "targetQuantity": 239, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-17T16:59:31.497442", "status": "pending", "mesForwardedCount": 68, "completionRate": 77.1}, "mesErrors": [{"timestamp": "2025-06-12T21:15:31.497442", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050015"}}, {"timestamp": "2025-06-13T07:17:31.497442", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050015"}}, {"timestamp": "2025-06-13T15:29:31.497442", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050015"}}, {"timestamp": "2025-06-13T13:52:31.497442", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050015"}}, {"timestamp": "2025-06-13T11:20:31.497442", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050015"}}], "recentLogs": [{"message": "測試條碼-f4b774c0", "timestamp": "2025-06-13T16:11:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-d676e481", "timestamp": "2025-06-13T16:10:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-004d5247", "timestamp": "2025-06-13T16:38:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-11e8aa4b", "timestamp": "2025-06-13T16:49:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-1da909a8", "timestamp": "2025-06-13T16:53:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-afd549a0", "timestamp": "2025-06-13T16:53:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-6e2d9650", "timestamp": "2025-06-13T16:43:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-9645c43d", "timestamp": "2025-06-13T16:47:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-0c30a6e2", "timestamp": "2025-06-13T16:00:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-21f45a13", "timestamp": "2025-06-13T16:56:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-07b25801", "timestamp": "2025-06-13T16:20:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}]}, "S720050016": {"deviceId": "S720050016", "lineName": "A1-01", "sectionName": "TESTING", "groupName": "TEST", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 61, "forwardedCount": 51, "unforwardedCount": 10, "lastLog": "測試產品條碼-499f937e", "lastUpdateTime": "2025-06-13T16:59:31.497442", "createdAt": "2025-02-16T16:59:31.497442", "workOrders": [{"workOrderNumber": "WO812418", "targetQuantity": 158, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-19T16:59:31.497442", "status": "pending", "mesForwardedCount": 67, "completionRate": 78.1}, {"workOrderNumber": "WO718856", "targetQuantity": 229, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-12T16:59:31.497442", "status": "completed", "mesForwardedCount": 5, "completionRate": 33.7}, {"workOrderNumber": "WO232806", "targetQuantity": 146, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-05T16:59:31.497442", "status": "completed", "mesForwardedCount": 86, "completionRate": 21.6}], "currentWorkOrder": 2, "currentWorkOrderProgress": 94, "displayWorkOrder": {"workOrderNumber": "WO232806", "targetQuantity": 146, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-05T16:59:31.497442", "status": "completed", "mesForwardedCount": 86, "completionRate": 21.6}, "mesErrors": [{"timestamp": "2025-06-13T01:34:31.497442", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050016"}}, {"timestamp": "2025-06-13T15:28:31.497442", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050016"}}, {"timestamp": "2025-06-13T11:19:31.497442", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050016"}}], "recentLogs": [{"message": "測試條碼-09cc80d3", "timestamp": "2025-06-13T16:12:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-eeb1329a", "timestamp": "2025-06-13T16:28:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-4fc3f239", "timestamp": "2025-06-13T16:57:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-e89269dc", "timestamp": "2025-06-13T16:43:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-69b3cc64", "timestamp": "2025-06-13T16:03:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-e7e68fb9", "timestamp": "2025-06-13T16:09:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-67f6f741", "timestamp": "2025-06-13T16:52:31.497442", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-a49560d9", "timestamp": "2025-06-13T16:04:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-b6a79857", "timestamp": "2025-06-13T16:25:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}]}, "S720050017": {"deviceId": "S720050017", "lineName": "B1-02", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 201, "forwardedCount": 151, "unforwardedCount": 50, "lastLog": "測試產品條碼-868e7fb2", "lastUpdateTime": "2025-06-13T16:59:31.497442", "createdAt": "2025-02-05T16:59:31.497442", "workOrders": [{"workOrderNumber": "WO342873", "targetQuantity": 340, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-04T16:59:31.497442", "status": "pending", "mesForwardedCount": 52, "completionRate": 40.1}, {"workOrderNumber": "WO955919", "targetQuantity": 324, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-03T16:59:31.497442", "status": "completed", "mesForwardedCount": 27, "completionRate": 95.6}, {"workOrderNumber": "WO371675", "targetQuantity": 417, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-31T16:59:31.497442", "status": "active", "mesForwardedCount": 63, "completionRate": 17.5}], "currentWorkOrder": 2, "currentWorkOrderProgress": 9, "displayWorkOrder": {"workOrderNumber": "WO371675", "targetQuantity": 417, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-31T16:59:31.497442", "status": "active", "mesForwardedCount": 63, "completionRate": 17.5}, "mesErrors": [{"timestamp": "2025-06-12T18:37:31.497442", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050017"}}, {"timestamp": "2025-06-13T04:45:31.497442", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050017"}}, {"timestamp": "2025-06-13T11:27:31.497442", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050017"}}, {"timestamp": "2025-06-13T10:49:31.497442", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050017"}}, {"timestamp": "2025-06-13T10:48:31.497442", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050017"}}], "recentLogs": [{"message": "測試條碼-a429c0fd", "timestamp": "2025-06-13T16:10:31.497442", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-78be009a", "timestamp": "2025-06-13T16:44:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-e4d48c4b", "timestamp": "2025-06-13T16:28:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-49c9dcc4", "timestamp": "2025-06-13T16:40:31.497442", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d7d72a35", "timestamp": "2025-06-13T16:00:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}]}, "S720050018": {"deviceId": "S720050018", "lineName": "A1-01", "sectionName": "ASSEMBLY", "groupName": "TEST", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 374, "forwardedCount": 177, "unforwardedCount": 197, "lastLog": "測試產品條碼-af8b32f1", "lastUpdateTime": "2025-06-13T16:59:31.497442", "createdAt": "2024-09-16T16:59:31.497442", "workOrders": [{"workOrderNumber": "WO322725", "targetQuantity": 104, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-30T16:59:31.497442", "status": "active", "mesForwardedCount": 46, "completionRate": 79.3}, {"workOrderNumber": "WO416267", "targetQuantity": 150, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-13T16:59:31.497442", "status": "pending", "mesForwardedCount": 89, "completionRate": 78.1}, {"workOrderNumber": "WO761862", "targetQuantity": 147, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-30T16:59:31.497442", "status": "active", "mesForwardedCount": 35, "completionRate": 23.3}], "currentWorkOrder": 0, "currentWorkOrderProgress": 75, "displayWorkOrder": {"workOrderNumber": "WO322725", "targetQuantity": 104, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-30T16:59:31.497442", "status": "active", "mesForwardedCount": 46, "completionRate": 79.3}, "mesErrors": [{"timestamp": "2025-06-13T05:20:31.497442", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050018"}}, {"timestamp": "2025-06-12T19:11:31.497442", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050018"}}], "recentLogs": [{"message": "測試條碼-c1161653", "timestamp": "2025-06-13T16:25:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-a7dce774", "timestamp": "2025-06-13T16:02:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-e6790205", "timestamp": "2025-06-13T16:33:31.497442", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-9372b1b8", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-cc1eaecc", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-790c72a5", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}]}, "S720050019": {"deviceId": "S720050019", "lineName": "A1-01", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 128, "forwardedCount": 41, "unforwardedCount": 87, "lastLog": "測試產品條碼-6a50bf54", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-01-23T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO613524", "targetQuantity": 305, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-03T16:59:31.499522", "status": "completed", "mesForwardedCount": 17, "completionRate": 70.6}, {"workOrderNumber": "WO534593", "targetQuantity": 293, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-01T16:59:31.499522", "status": "active", "mesForwardedCount": 88, "completionRate": 37.9}, {"workOrderNumber": "WO617960", "targetQuantity": 208, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-14T16:59:31.499522", "status": "active", "mesForwardedCount": 55, "completionRate": 9.3}], "currentWorkOrder": 1, "currentWorkOrderProgress": 0, "displayWorkOrder": {"workOrderNumber": "WO534593", "targetQuantity": 293, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-01T16:59:31.499522", "status": "active", "mesForwardedCount": 88, "completionRate": 37.9}, "mesErrors": [{"timestamp": "2025-06-13T00:09:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050019"}}, {"timestamp": "2025-06-12T21:17:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050019"}}, {"timestamp": "2025-06-13T00:15:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050019"}}], "recentLogs": [{"message": "測試條碼-00e4e2e5", "timestamp": "2025-06-13T16:01:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-3d8a91e8", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-aaec7664", "timestamp": "2025-06-13T16:12:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-60e7e1c7", "timestamp": "2025-06-13T16:17:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-2633eaab", "timestamp": "2025-06-13T16:30:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-c40d709b", "timestamp": "2025-06-13T16:41:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-8ce15ddc", "timestamp": "2025-06-13T16:28:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-9d4958ad", "timestamp": "2025-06-13T16:38:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-bb3ad91c", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-37dd9ba0", "timestamp": "2025-06-13T16:24:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-4679d4c3", "timestamp": "2025-06-13T16:10:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}]}, "S720050020": {"deviceId": "S720050020", "lineName": "B1-02", "sectionName": "INJECTION", "groupName": "PACK", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 565, "forwardedCount": 220, "unforwardedCount": 345, "lastLog": "測試產品條碼-01856e16", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-12-23T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO413091", "targetQuantity": 322, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-11T16:59:31.499522", "status": "pending", "mesForwardedCount": 15, "completionRate": 49.2}, {"workOrderNumber": "WO718889", "targetQuantity": 429, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-21T16:59:31.499522", "status": "completed", "mesForwardedCount": 19, "completionRate": 86.3}, {"workOrderNumber": "WO291776", "targetQuantity": 402, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-22T16:59:31.499522", "status": "pending", "mesForwardedCount": 44, "completionRate": 8.7}], "currentWorkOrder": 0, "currentWorkOrderProgress": 94, "displayWorkOrder": {"workOrderNumber": "WO413091", "targetQuantity": 322, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-11T16:59:31.499522", "status": "pending", "mesForwardedCount": 15, "completionRate": 49.2}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-2c23a5e0", "timestamp": "2025-06-13T16:06:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-9b2dd89c", "timestamp": "2025-06-13T16:56:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-678c6121", "timestamp": "2025-06-13T16:08:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-79955931", "timestamp": "2025-06-13T16:25:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-3e78abe9", "timestamp": "2025-06-13T16:47:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-91fa346e", "timestamp": "2025-06-13T16:16:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-c5c20792", "timestamp": "2025-06-13T16:45:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-88e38fc6", "timestamp": "2025-06-13T16:55:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-88374f10", "timestamp": "2025-06-13T16:12:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-7f51ce1a", "timestamp": "2025-06-13T15:59:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-c6325708", "timestamp": "2025-06-13T16:43:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-a2c7389f", "timestamp": "2025-06-13T16:48:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-d595b509", "timestamp": "2025-06-13T16:37:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-8588daca", "timestamp": "2025-06-13T16:52:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-88721a33", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}]}, "S720050021": {"deviceId": "S720050021", "lineName": "A1-02", "sectionName": "PACKAGING", "groupName": "TEST", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 40, "forwardedCount": 25, "unforwardedCount": 15, "lastLog": "測試產品條碼-8b978b05", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-03-06T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO846538", "targetQuantity": 256, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-21T16:59:31.499522", "status": "completed", "mesForwardedCount": 82, "completionRate": 79.1}, {"workOrderNumber": "WO836596", "targetQuantity": 443, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-29T16:59:31.499522", "status": "completed", "mesForwardedCount": 39, "completionRate": 79.7}, {"workOrderNumber": "WO822098", "targetQuantity": 269, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-12T16:59:31.499522", "status": "active", "mesForwardedCount": 12, "completionRate": 76.2}], "currentWorkOrder": 1, "currentWorkOrderProgress": 33, "displayWorkOrder": {"workOrderNumber": "WO836596", "targetQuantity": 443, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-29T16:59:31.499522", "status": "completed", "mesForwardedCount": 39, "completionRate": 79.7}, "mesErrors": [{"timestamp": "2025-06-12T17:32:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050021"}}, {"timestamp": "2025-06-13T16:08:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050021"}}], "recentLogs": [{"message": "測試條碼-32bff448", "timestamp": "2025-06-13T16:48:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-134e348d", "timestamp": "2025-06-13T16:36:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-c53b2e10", "timestamp": "2025-06-13T16:12:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-3ed68b8d", "timestamp": "2025-06-13T16:41:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-9bb7f5b3", "timestamp": "2025-06-13T16:08:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ab1687f2", "timestamp": "2025-06-13T16:36:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-d3fb57f4", "timestamp": "2025-06-13T16:34:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-b2f97173", "timestamp": "2025-06-13T15:59:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-6a6f81ce", "timestamp": "2025-06-13T16:03:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ffaa4fc4", "timestamp": "2025-06-13T16:37:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-f683e3d4", "timestamp": "2025-06-13T16:06:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-08372abf", "timestamp": "2025-06-13T16:44:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-8eb4f98d", "timestamp": "2025-06-13T16:53:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-c41995a9", "timestamp": "2025-06-13T16:36:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}]}, "S720050022": {"deviceId": "S720050022", "lineName": "A1-01", "sectionName": "ASSEMBLY", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 168, "forwardedCount": 56, "unforwardedCount": 112, "lastLog": "測試產品條碼-30ca1b99", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-05-17T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO954792", "targetQuantity": 303, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-25T16:59:31.499522", "status": "pending", "mesForwardedCount": 64, "completionRate": 21.7}, {"workOrderNumber": "WO333864", "targetQuantity": 280, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-18T16:59:31.499522", "status": "completed", "mesForwardedCount": 74, "completionRate": 58.2}, {"workOrderNumber": "WO597567", "targetQuantity": 247, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-25T16:59:31.499522", "status": "pending", "mesForwardedCount": 4, "completionRate": 60.1}], "currentWorkOrder": 0, "currentWorkOrderProgress": 74, "displayWorkOrder": {"workOrderNumber": "WO954792", "targetQuantity": 303, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-25T16:59:31.499522", "status": "pending", "mesForwardedCount": 64, "completionRate": 21.7}, "mesErrors": [{"timestamp": "2025-06-12T19:16:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050022"}}, {"timestamp": "2025-06-12T21:35:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050022"}}, {"timestamp": "2025-06-13T15:24:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050022"}}, {"timestamp": "2025-06-12T17:00:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050022"}}], "recentLogs": [{"message": "測試條碼-2f686cfd", "timestamp": "2025-06-13T16:09:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-e34ba846", "timestamp": "2025-06-13T16:15:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-393830d3", "timestamp": "2025-06-13T16:19:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-87b26551", "timestamp": "2025-06-13T16:44:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-35b3f2d5", "timestamp": "2025-06-13T16:11:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-67768230", "timestamp": "2025-06-13T16:23:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-672537ef", "timestamp": "2025-06-13T16:53:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-1c5192d0", "timestamp": "2025-06-13T16:17:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-472571b9", "timestamp": "2025-06-13T16:20:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-2c8bd3f3", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-02990fb1", "timestamp": "2025-06-13T16:45:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-fa60dc26", "timestamp": "2025-06-13T16:40:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-d8578ffb", "timestamp": "2025-06-13T16:55:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-6d9e49f3", "timestamp": "2025-06-13T16:40:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}]}, "S720050023": {"deviceId": "S720050023", "lineName": "B1-02", "sectionName": "ASSEMBLY", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 652, "forwardedCount": 458, "unforwardedCount": 194, "lastLog": "測試產品條碼-aac6e560", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-02-08T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO973041", "targetQuantity": 272, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-24T16:59:31.499522", "status": "active", "mesForwardedCount": 27, "completionRate": 24.1}, {"workOrderNumber": "WO196027", "targetQuantity": 198, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-22T16:59:31.499522", "status": "pending", "mesForwardedCount": 81, "completionRate": 75.9}, {"workOrderNumber": "WO420012", "targetQuantity": 268, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-07T16:59:31.499522", "status": "active", "mesForwardedCount": 100, "completionRate": 91.4}], "currentWorkOrder": 0, "currentWorkOrderProgress": 90, "displayWorkOrder": {"workOrderNumber": "WO973041", "targetQuantity": 272, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-24T16:59:31.499522", "status": "active", "mesForwardedCount": 27, "completionRate": 24.1}, "mesErrors": [{"timestamp": "2025-06-13T05:05:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050023"}}], "recentLogs": [{"message": "測試條碼-aa6998dc", "timestamp": "2025-06-13T16:24:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-33551f9b", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-4428998a", "timestamp": "2025-06-13T16:36:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-c771340e", "timestamp": "2025-06-13T16:07:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-9f85ef7b", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-bdd7f93f", "timestamp": "2025-06-13T16:05:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-d964a9ec", "timestamp": "2025-06-13T16:10:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-9bf3ee9f", "timestamp": "2025-06-13T16:56:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-8809e0d2", "timestamp": "2025-06-13T16:19:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-bda57e24", "timestamp": "2025-06-13T15:59:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-1dacc086", "timestamp": "2025-06-13T16:00:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-b5ae1d43", "timestamp": "2025-06-13T16:03:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-1d8fa163", "timestamp": "2025-06-13T16:21:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ec3c3416", "timestamp": "2025-06-13T16:14:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-68b32cbd", "timestamp": "2025-06-13T16:32:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-86694f30", "timestamp": "2025-06-13T16:03:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-8a25dba5", "timestamp": "2025-06-13T16:50:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-1f5b68cf", "timestamp": "2025-06-13T16:30:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-8f3eb086", "timestamp": "2025-06-13T16:15:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-0631b353", "timestamp": "2025-06-13T16:08:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}]}, "S720050024": {"deviceId": "S720050024", "lineName": "B1-02", "sectionName": "PACKAGING", "groupName": "ASSEMBLY", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 780, "forwardedCount": 196, "unforwardedCount": 584, "lastLog": "測試產品條碼-5a098233", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-05-03T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO598764", "targetQuantity": 161, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-09T16:59:31.499522", "status": "active", "mesForwardedCount": 73, "completionRate": 14.2}, {"workOrderNumber": "WO151079", "targetQuantity": 303, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-22T16:59:31.499522", "status": "active", "mesForwardedCount": 47, "completionRate": 76.6}, {"workOrderNumber": "WO575849", "targetQuantity": 227, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-10T16:59:31.499522", "status": "completed", "mesForwardedCount": 30, "completionRate": 42.6}], "currentWorkOrder": 1, "currentWorkOrderProgress": 45, "displayWorkOrder": {"workOrderNumber": "WO151079", "targetQuantity": 303, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-22T16:59:31.499522", "status": "active", "mesForwardedCount": 47, "completionRate": 76.6}, "mesErrors": [{"timestamp": "2025-06-12T22:18:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050024"}}, {"timestamp": "2025-06-13T05:29:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050024"}}], "recentLogs": [{"message": "測試條碼-edaf52af", "timestamp": "2025-06-13T16:10:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-66dcf11f", "timestamp": "2025-06-13T16:34:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-1261a58e", "timestamp": "2025-06-13T16:37:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e924659c", "timestamp": "2025-06-13T16:19:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-00a962fb", "timestamp": "2025-06-13T16:28:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-42b87d65", "timestamp": "2025-06-13T16:28:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-c69deb06", "timestamp": "2025-06-13T16:46:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-d237cccb", "timestamp": "2025-06-13T16:52:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e15edd39", "timestamp": "2025-06-13T16:24:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-e73956e7", "timestamp": "2025-06-13T16:25:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-6313fba0", "timestamp": "2025-06-13T16:49:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-2c6258e1", "timestamp": "2025-06-13T16:03:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-3530bb93", "timestamp": "2025-06-13T16:32:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-48b7e2ce", "timestamp": "2025-06-13T16:18:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-b3f23316", "timestamp": "2025-06-13T16:45:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-f811de0d", "timestamp": "2025-06-13T16:11:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-9ed9048c", "timestamp": "2025-06-13T16:20:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}]}, "S720050025": {"deviceId": "S720050025", "lineName": "A1-02", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 475, "forwardedCount": 159, "unforwardedCount": 316, "lastLog": "測試產品條碼-80a53446", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-08-04T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO723398", "targetQuantity": 492, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-18T16:59:31.499522", "status": "active", "mesForwardedCount": 72, "completionRate": 78.7}, {"workOrderNumber": "WO668838", "targetQuantity": 276, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-28T16:59:31.499522", "status": "active", "mesForwardedCount": 82, "completionRate": 72.7}, {"workOrderNumber": "WO809008", "targetQuantity": 498, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-26T16:59:31.499522", "status": "completed", "mesForwardedCount": 29, "completionRate": 21.3}], "currentWorkOrder": 2, "currentWorkOrderProgress": 1, "displayWorkOrder": {"workOrderNumber": "WO809008", "targetQuantity": 498, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-26T16:59:31.499522", "status": "completed", "mesForwardedCount": 29, "completionRate": 21.3}, "mesErrors": [{"timestamp": "2025-06-13T14:05:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050025"}}, {"timestamp": "2025-06-12T18:04:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050025"}}, {"timestamp": "2025-06-13T09:41:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050025"}}, {"timestamp": "2025-06-12T22:50:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050025"}}], "recentLogs": [{"message": "測試條碼-163c1854", "timestamp": "2025-06-13T16:53:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-01087e36", "timestamp": "2025-06-13T16:35:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-ca6c24fc", "timestamp": "2025-06-13T16:01:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-cb466701", "timestamp": "2025-06-13T16:48:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-8d234677", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-6e6846a1", "timestamp": "2025-06-13T16:21:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-c570f567", "timestamp": "2025-06-13T16:54:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-979e69e6", "timestamp": "2025-06-13T16:12:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-db92c8dc", "timestamp": "2025-06-13T16:14:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-05c02546", "timestamp": "2025-06-13T16:53:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-5f781dae", "timestamp": "2025-06-13T16:11:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-d593189d", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-cd5044eb", "timestamp": "2025-06-13T16:52:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-bca5b516", "timestamp": "2025-06-13T16:40:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}]}, "S720050026": {"deviceId": "S720050026", "lineName": "A2-01", "sectionName": "ASSEMBLY", "groupName": "INJECTION", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 181, "forwardedCount": 166, "unforwardedCount": 15, "lastLog": "測試產品條碼-0b8a02ea", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-12-19T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO963287", "targetQuantity": 392, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-02T16:59:31.499522", "status": "active", "mesForwardedCount": 23, "completionRate": 53.5}, {"workOrderNumber": "WO499686", "targetQuantity": 236, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-10T16:59:31.499522", "status": "completed", "mesForwardedCount": 47, "completionRate": 5.0}, {"workOrderNumber": "WO386137", "targetQuantity": 373, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-01T16:59:31.499522", "status": "completed", "mesForwardedCount": 72, "completionRate": 60.1}], "currentWorkOrder": 0, "currentWorkOrderProgress": 96, "displayWorkOrder": {"workOrderNumber": "WO963287", "targetQuantity": 392, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-02T16:59:31.499522", "status": "active", "mesForwardedCount": 23, "completionRate": 53.5}, "mesErrors": [{"timestamp": "2025-06-13T09:00:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050026"}}, {"timestamp": "2025-06-12T18:31:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050026"}}, {"timestamp": "2025-06-13T11:24:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050026"}}, {"timestamp": "2025-06-13T00:57:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050026"}}, {"timestamp": "2025-06-13T16:35:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050026"}}], "recentLogs": [{"message": "測試條碼-6e784215", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-ace16325", "timestamp": "2025-06-13T16:26:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-e919bc7d", "timestamp": "2025-06-13T16:27:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-70def09a", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-39814458", "timestamp": "2025-06-13T16:51:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-a8dfd3c8", "timestamp": "2025-06-13T16:05:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-1e2f32ea", "timestamp": "2025-06-13T16:47:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-e4ac4180", "timestamp": "2025-06-13T16:45:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-351f1e4e", "timestamp": "2025-06-13T16:20:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-7cad30ba", "timestamp": "2025-06-13T16:42:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-b4a620af", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-0cb25da1", "timestamp": "2025-06-13T16:51:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-d407fbd0", "timestamp": "2025-06-13T16:17:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}]}, "S720050027": {"deviceId": "S720050027", "lineName": "A2-02", "sectionName": "INJECTION", "groupName": "PACK", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 673, "forwardedCount": 664, "unforwardedCount": 9, "lastLog": "測試產品條碼-2f729c94", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-08-09T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO461580", "targetQuantity": 453, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-29T16:59:31.499522", "status": "pending", "mesForwardedCount": 94, "completionRate": 6.2}, {"workOrderNumber": "WO344451", "targetQuantity": 260, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-04T16:59:31.499522", "status": "active", "mesForwardedCount": 3, "completionRate": 90.5}, {"workOrderNumber": "WO738840", "targetQuantity": 79, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-06T16:59:31.499522", "status": "completed", "mesForwardedCount": 2, "completionRate": 63.6}], "currentWorkOrder": 0, "currentWorkOrderProgress": 84, "displayWorkOrder": {"workOrderNumber": "WO461580", "targetQuantity": 453, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-29T16:59:31.499522", "status": "pending", "mesForwardedCount": 94, "completionRate": 6.2}, "mesErrors": [{"timestamp": "2025-06-13T04:34:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050027"}}, {"timestamp": "2025-06-13T01:18:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050027"}}, {"timestamp": "2025-06-13T15:27:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050027"}}, {"timestamp": "2025-06-13T00:37:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050027"}}, {"timestamp": "2025-06-13T15:29:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050027"}}], "recentLogs": [{"message": "測試條碼-6096ec7d", "timestamp": "2025-06-13T16:07:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-57809f55", "timestamp": "2025-06-13T16:38:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-8e66a27f", "timestamp": "2025-06-13T16:38:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-0955b5fd", "timestamp": "2025-06-13T16:13:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-05570300", "timestamp": "2025-06-13T16:34:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-e4eb0492", "timestamp": "2025-06-13T15:59:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e55a9a4b", "timestamp": "2025-06-13T16:23:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1373c33a", "timestamp": "2025-06-13T16:03:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e2678675", "timestamp": "2025-06-13T16:54:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-51bbdb37", "timestamp": "2025-06-13T16:06:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-5eb850e9", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-6123c27e", "timestamp": "2025-06-13T16:25:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-11b40a15", "timestamp": "2025-06-13T16:38:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-9d13dace", "timestamp": "2025-06-13T16:35:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-869fb1c1", "timestamp": "2025-06-13T16:44:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}]}, "S720050028": {"deviceId": "S720050028", "lineName": "A2-02", "sectionName": "PACKAGING", "groupName": "ASSEMBLY", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 307, "forwardedCount": 84, "unforwardedCount": 223, "lastLog": "測試產品條碼-3a6f0bb4", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-03-20T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO371837", "targetQuantity": 498, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-30T16:59:31.499522", "status": "active", "mesForwardedCount": 87, "completionRate": 53.3}, {"workOrderNumber": "WO668255", "targetQuantity": 100, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-01T16:59:31.499522", "status": "active", "mesForwardedCount": 58, "completionRate": 42.8}, {"workOrderNumber": "WO291826", "targetQuantity": 478, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-30T16:59:31.499522", "status": "pending", "mesForwardedCount": 68, "completionRate": 81.4}], "currentWorkOrder": 0, "currentWorkOrderProgress": 69, "displayWorkOrder": {"workOrderNumber": "WO371837", "targetQuantity": 498, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-30T16:59:31.499522", "status": "active", "mesForwardedCount": 87, "completionRate": 53.3}, "mesErrors": [{"timestamp": "2025-06-13T08:18:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050028"}}, {"timestamp": "2025-06-13T09:15:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050028"}}, {"timestamp": "2025-06-13T07:55:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050028"}}, {"timestamp": "2025-06-13T14:07:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050028"}}, {"timestamp": "2025-06-12T19:26:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050028"}}], "recentLogs": [{"message": "測試條碼-1f7670f5", "timestamp": "2025-06-13T16:23:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-8f002ed0", "timestamp": "2025-06-13T16:48:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-6d5ac213", "timestamp": "2025-06-13T16:42:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-021debaf", "timestamp": "2025-06-13T16:10:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-d78d43ef", "timestamp": "2025-06-13T16:55:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ae7d6cdb", "timestamp": "2025-06-13T16:23:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}]}, "S720050029": {"deviceId": "S720050029", "lineName": "A2-01", "sectionName": "TESTING", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 424, "forwardedCount": 14, "unforwardedCount": 410, "lastLog": "測試產品條碼-4a538c1f", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-12-05T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO883360", "targetQuantity": 202, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-31T16:59:31.499522", "status": "pending", "mesForwardedCount": 48, "completionRate": 13.4}, {"workOrderNumber": "WO107779", "targetQuantity": 254, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-09T16:59:31.499522", "status": "completed", "mesForwardedCount": 35, "completionRate": 0.3}, {"workOrderNumber": "WO933917", "targetQuantity": 436, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-01T16:59:31.499522", "status": "active", "mesForwardedCount": 66, "completionRate": 45.6}], "currentWorkOrder": 1, "currentWorkOrderProgress": 73, "displayWorkOrder": {"workOrderNumber": "WO107779", "targetQuantity": 254, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-09T16:59:31.499522", "status": "completed", "mesForwardedCount": 35, "completionRate": 0.3}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-446a88c2", "timestamp": "2025-06-13T16:08:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-f5181cb2", "timestamp": "2025-06-13T16:17:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-e6f84bd4", "timestamp": "2025-06-13T16:44:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-7ab7d8fa", "timestamp": "2025-06-13T16:54:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-cf73aa3a", "timestamp": "2025-06-13T16:46:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-7c1a4346", "timestamp": "2025-06-13T16:24:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-9dde1c60", "timestamp": "2025-06-13T16:17:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-37b84f58", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-ed355498", "timestamp": "2025-06-13T16:06:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-03f7990c", "timestamp": "2025-06-13T16:43:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e0f65d4e", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-c4298060", "timestamp": "2025-06-13T16:05:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-25179133", "timestamp": "2025-06-13T16:13:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-314ed649", "timestamp": "2025-06-13T16:36:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f5541a2f", "timestamp": "2025-06-13T16:49:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}]}, "S720050030": {"deviceId": "S720050030", "lineName": "B2-02", "sectionName": "PACKAGING", "groupName": "ASSEMBLY", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 422, "forwardedCount": 379, "unforwardedCount": 43, "lastLog": "測試產品條碼-111cbdb3", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-09-02T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO908444", "targetQuantity": 223, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-16T16:59:31.499522", "status": "completed", "mesForwardedCount": 1, "completionRate": 19.2}, {"workOrderNumber": "WO290434", "targetQuantity": 310, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-03T16:59:31.499522", "status": "pending", "mesForwardedCount": 87, "completionRate": 26.2}, {"workOrderNumber": "WO830353", "targetQuantity": 445, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-03T16:59:31.499522", "status": "active", "mesForwardedCount": 90, "completionRate": 95.5}], "currentWorkOrder": 0, "currentWorkOrderProgress": 25, "displayWorkOrder": {"workOrderNumber": "WO908444", "targetQuantity": 223, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-16T16:59:31.499522", "status": "completed", "mesForwardedCount": 1, "completionRate": 19.2}, "mesErrors": [{"timestamp": "2025-06-13T04:53:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050030"}}, {"timestamp": "2025-06-12T18:41:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050030"}}, {"timestamp": "2025-06-12T20:10:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050030"}}, {"timestamp": "2025-06-12T21:52:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050030"}}], "recentLogs": [{"message": "測試條碼-bc112158", "timestamp": "2025-06-13T16:51:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-cfab0434", "timestamp": "2025-06-13T16:49:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-31ec0c36", "timestamp": "2025-06-13T16:47:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-f3c5c646", "timestamp": "2025-06-13T16:15:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-6ab5fff6", "timestamp": "2025-06-13T16:07:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-276225f1", "timestamp": "2025-06-13T16:14:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-a5f5703e", "timestamp": "2025-06-13T16:27:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-82f52fe6", "timestamp": "2025-06-13T16:16:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-a6c5cc45", "timestamp": "2025-06-13T16:00:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-4a6403a8", "timestamp": "2025-06-13T16:00:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-b1bb2831", "timestamp": "2025-06-13T16:50:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-6cc882ba", "timestamp": "2025-06-13T16:44:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-21669a04", "timestamp": "2025-06-13T16:45:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d063cea3", "timestamp": "2025-06-13T16:05:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-409021b6", "timestamp": "2025-06-13T16:11:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-71b49032", "timestamp": "2025-06-13T16:25:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-b16a2402", "timestamp": "2025-06-13T16:10:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-ae30f5aa", "timestamp": "2025-06-13T16:54:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-c087fa96", "timestamp": "2025-06-13T16:14:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}]}, "S720050031": {"deviceId": "S720050031", "lineName": "A1-01", "sectionName": "PACKAGING", "groupName": "ASSEMBLY", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 842, "forwardedCount": 292, "unforwardedCount": 550, "lastLog": "測試產品條碼-1e3fa871", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-05-06T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO342344", "targetQuantity": 260, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-28T16:59:31.499522", "status": "active", "mesForwardedCount": 35, "completionRate": 77.8}, {"workOrderNumber": "WO977659", "targetQuantity": 301, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-04T16:59:31.499522", "status": "completed", "mesForwardedCount": 100, "completionRate": 88.5}, {"workOrderNumber": "WO295723", "targetQuantity": 152, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-13T16:59:31.499522", "status": "pending", "mesForwardedCount": 43, "completionRate": 14.7}], "currentWorkOrder": 0, "currentWorkOrderProgress": 70, "displayWorkOrder": {"workOrderNumber": "WO342344", "targetQuantity": 260, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-28T16:59:31.499522", "status": "active", "mesForwardedCount": 35, "completionRate": 77.8}, "mesErrors": [{"timestamp": "2025-06-13T14:53:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050031"}}, {"timestamp": "2025-06-13T12:46:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050031"}}], "recentLogs": [{"message": "測試條碼-8137f4c3", "timestamp": "2025-06-13T16:28:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-8439d6e0", "timestamp": "2025-06-13T16:28:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ae484265", "timestamp": "2025-06-13T16:57:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-0e7aeb19", "timestamp": "2025-06-13T16:40:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-411fbc01", "timestamp": "2025-06-13T16:21:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-d656769c", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}]}, "S720050032": {"deviceId": "S720050032", "lineName": "B2-01", "sectionName": "ASSEMBLY", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 857, "forwardedCount": 670, "unforwardedCount": 187, "lastLog": "測試產品條碼-2c08d501", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-11-11T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO542442", "targetQuantity": 443, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-12T16:59:31.499522", "status": "completed", "mesForwardedCount": 94, "completionRate": 49.3}, {"workOrderNumber": "WO480162", "targetQuantity": 339, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-12T16:59:31.499522", "status": "pending", "mesForwardedCount": 47, "completionRate": 18.0}, {"workOrderNumber": "WO982837", "targetQuantity": 304, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-16T16:59:31.499522", "status": "pending", "mesForwardedCount": 70, "completionRate": 61.7}], "currentWorkOrder": 2, "currentWorkOrderProgress": 78, "displayWorkOrder": {"workOrderNumber": "WO982837", "targetQuantity": 304, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-16T16:59:31.499522", "status": "pending", "mesForwardedCount": 70, "completionRate": 61.7}, "mesErrors": [{"timestamp": "2025-06-13T08:14:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050032"}}, {"timestamp": "2025-06-12T19:22:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050032"}}], "recentLogs": [{"message": "測試條碼-c1402003", "timestamp": "2025-06-13T16:47:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-5c26d870", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-70f82171", "timestamp": "2025-06-13T16:17:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-b76662b2", "timestamp": "2025-06-13T16:54:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-7f61835c", "timestamp": "2025-06-13T16:48:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-755a8e57", "timestamp": "2025-06-13T16:24:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-2d8fe0d0", "timestamp": "2025-06-13T16:53:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1dda6484", "timestamp": "2025-06-13T16:14:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-b932ad9c", "timestamp": "2025-06-13T16:44:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-a80defaa", "timestamp": "2025-06-13T16:01:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-4a26e39f", "timestamp": "2025-06-13T16:37:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-24fe2d1b", "timestamp": "2025-06-13T16:46:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-de7146b7", "timestamp": "2025-06-13T16:35:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-18606a0f", "timestamp": "2025-06-13T16:28:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-e0b0ca6c", "timestamp": "2025-06-13T16:28:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-091ad870", "timestamp": "2025-06-13T16:14:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-787ee549", "timestamp": "2025-06-13T16:20:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-ee3c0efc", "timestamp": "2025-06-13T16:36:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-f35cbad7", "timestamp": "2025-06-13T16:41:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-97f390e8", "timestamp": "2025-06-13T16:01:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}]}, "S720050033": {"deviceId": "S720050033", "lineName": "A2-02", "sectionName": "ASSEMBLY", "groupName": "ASSEMBLY", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 258, "forwardedCount": 31, "unforwardedCount": 227, "lastLog": "測試產品條碼-7bb2f3ed", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-04-27T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO528111", "targetQuantity": 411, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-03T16:59:31.499522", "status": "completed", "mesForwardedCount": 100, "completionRate": 49.8}, {"workOrderNumber": "WO941533", "targetQuantity": 490, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-17T16:59:31.499522", "status": "pending", "mesForwardedCount": 58, "completionRate": 22.4}, {"workOrderNumber": "WO379284", "targetQuantity": 363, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-16T16:59:31.499522", "status": "active", "mesForwardedCount": 46, "completionRate": 23.8}], "currentWorkOrder": 1, "currentWorkOrderProgress": 3, "displayWorkOrder": {"workOrderNumber": "WO941533", "targetQuantity": 490, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-17T16:59:31.499522", "status": "pending", "mesForwardedCount": 58, "completionRate": 22.4}, "mesErrors": [{"timestamp": "2025-06-13T02:30:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050033"}}, {"timestamp": "2025-06-13T16:39:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050033"}}, {"timestamp": "2025-06-13T13:47:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050033"}}, {"timestamp": "2025-06-13T08:11:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050033"}}, {"timestamp": "2025-06-13T15:38:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050033"}}], "recentLogs": [{"message": "測試條碼-5e930729", "timestamp": "2025-06-13T16:20:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-3974f850", "timestamp": "2025-06-13T16:26:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-f32e7b74", "timestamp": "2025-06-13T16:20:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-2610724e", "timestamp": "2025-06-13T16:15:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-79cf8070", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-55128bb7", "timestamp": "2025-06-13T16:27:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-c2abd4e1", "timestamp": "2025-06-13T16:10:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-3a1e0e50", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-5d117d60", "timestamp": "2025-06-13T16:05:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-967be84f", "timestamp": "2025-06-13T16:14:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-53b0029c", "timestamp": "2025-06-13T16:41:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-58f4962a", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}]}, "S720050034": {"deviceId": "S720050034", "lineName": "B2-01", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 426, "forwardedCount": 50, "unforwardedCount": 376, "lastLog": "測試產品條碼-5c609201", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-04-25T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO437835", "targetQuantity": 133, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-31T16:59:31.499522", "status": "pending", "mesForwardedCount": 36, "completionRate": 10.7}, {"workOrderNumber": "WO519543", "targetQuantity": 197, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-22T16:59:31.499522", "status": "active", "mesForwardedCount": 57, "completionRate": 44.8}, {"workOrderNumber": "WO421010", "targetQuantity": 462, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-10T16:59:31.499522", "status": "completed", "mesForwardedCount": 49, "completionRate": 41.3}], "currentWorkOrder": 2, "currentWorkOrderProgress": 70, "displayWorkOrder": {"workOrderNumber": "WO421010", "targetQuantity": 462, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-10T16:59:31.499522", "status": "completed", "mesForwardedCount": 49, "completionRate": 41.3}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-7dd8f827", "timestamp": "2025-06-13T16:53:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-5f844e63", "timestamp": "2025-06-13T16:38:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-939de5af", "timestamp": "2025-06-13T15:59:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-3d674e95", "timestamp": "2025-06-13T16:40:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-109f6616", "timestamp": "2025-06-13T16:36:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-b10966f6", "timestamp": "2025-06-13T16:33:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-2aa51073", "timestamp": "2025-06-13T16:22:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-6467caf3", "timestamp": "2025-06-13T16:00:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-b7bbeab1", "timestamp": "2025-06-13T16:34:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-afce54ce", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-3dda384e", "timestamp": "2025-06-13T16:50:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-28acb49f", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-b6c63723", "timestamp": "2025-06-13T16:18:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-9459f936", "timestamp": "2025-06-13T16:27:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-35c34a12", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}]}, "S720050035": {"deviceId": "S720050035", "lineName": "A1-02", "sectionName": "ASSEMBLY", "groupName": "TEST", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 536, "forwardedCount": 60, "unforwardedCount": 476, "lastLog": "測試產品條碼-23370e6a", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-11-19T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO491343", "targetQuantity": 343, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-14T16:59:31.499522", "status": "active", "mesForwardedCount": 99, "completionRate": 53.6}, {"workOrderNumber": "WO239361", "targetQuantity": 171, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-31T16:59:31.499522", "status": "completed", "mesForwardedCount": 74, "completionRate": 90.1}, {"workOrderNumber": "WO798172", "targetQuantity": 111, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-14T16:59:31.499522", "status": "pending", "mesForwardedCount": 11, "completionRate": 72.2}], "currentWorkOrder": 2, "currentWorkOrderProgress": 99, "displayWorkOrder": {"workOrderNumber": "WO798172", "targetQuantity": 111, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-14T16:59:31.499522", "status": "pending", "mesForwardedCount": 11, "completionRate": 72.2}, "mesErrors": [{"timestamp": "2025-06-13T13:11:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050035"}}, {"timestamp": "2025-06-13T04:42:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050035"}}, {"timestamp": "2025-06-13T04:53:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050035"}}, {"timestamp": "2025-06-13T01:49:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050035"}}], "recentLogs": [{"message": "測試條碼-8897d213", "timestamp": "2025-06-13T16:29:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-576aa341", "timestamp": "2025-06-13T15:59:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-0fc013f4", "timestamp": "2025-06-13T16:29:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-def62ebe", "timestamp": "2025-06-13T16:45:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-b8ce30a6", "timestamp": "2025-06-13T16:09:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-4ef7c5ce", "timestamp": "2025-06-13T16:09:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-f13c28dd", "timestamp": "2025-06-13T16:30:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-4b49a90b", "timestamp": "2025-06-13T16:19:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d1af3042", "timestamp": "2025-06-13T16:29:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-b7b551e4", "timestamp": "2025-06-13T16:30:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-9bd7e49d", "timestamp": "2025-06-13T16:08:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-51a81f11", "timestamp": "2025-06-13T16:17:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-30c9b206", "timestamp": "2025-06-13T16:54:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-c1a14afc", "timestamp": "2025-06-13T16:09:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}]}, "S720050036": {"deviceId": "S720050036", "lineName": "B2-01", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 348, "forwardedCount": 62, "unforwardedCount": 286, "lastLog": "測試產品條碼-c6a85a1c", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-01-10T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO821447", "targetQuantity": 141, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-19T16:59:31.499522", "status": "completed", "mesForwardedCount": 15, "completionRate": 22.2}, {"workOrderNumber": "WO243664", "targetQuantity": 137, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-03T16:59:31.499522", "status": "completed", "mesForwardedCount": 61, "completionRate": 23.0}, {"workOrderNumber": "WO875303", "targetQuantity": 306, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-24T16:59:31.499522", "status": "pending", "mesForwardedCount": 25, "completionRate": 34.9}], "currentWorkOrder": 1, "currentWorkOrderProgress": 37, "displayWorkOrder": {"workOrderNumber": "WO243664", "targetQuantity": 137, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-03T16:59:31.499522", "status": "completed", "mesForwardedCount": 61, "completionRate": 23.0}, "mesErrors": [{"timestamp": "2025-06-13T10:59:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050036"}}, {"timestamp": "2025-06-13T02:50:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050036"}}, {"timestamp": "2025-06-13T03:47:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050036"}}, {"timestamp": "2025-06-12T19:59:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050036"}}, {"timestamp": "2025-06-13T09:54:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050036"}}], "recentLogs": [{"message": "測試條碼-2fe1f3bf", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-55fdd9f6", "timestamp": "2025-06-13T16:13:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-e1a7462c", "timestamp": "2025-06-13T16:06:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-0785bf2a", "timestamp": "2025-06-13T16:40:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-0aa50831", "timestamp": "2025-06-13T16:09:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-5af8c8af", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-7604c32a", "timestamp": "2025-06-13T16:51:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-b4d93799", "timestamp": "2025-06-13T16:30:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-ff787178", "timestamp": "2025-06-13T16:52:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-53b59885", "timestamp": "2025-06-13T16:07:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}]}, "S720050037": {"deviceId": "S720050037", "lineName": "B2-01", "sectionName": "TESTING", "groupName": "TEST", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 959, "forwardedCount": 471, "unforwardedCount": 488, "lastLog": "測試產品條碼-329193ec", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-08-10T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO961329", "targetQuantity": 378, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-24T16:59:31.499522", "status": "pending", "mesForwardedCount": 87, "completionRate": 23.0}, {"workOrderNumber": "WO605611", "targetQuantity": 196, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-22T16:59:31.499522", "status": "completed", "mesForwardedCount": 34, "completionRate": 55.0}, {"workOrderNumber": "WO199307", "targetQuantity": 307, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-14T16:59:31.499522", "status": "pending", "mesForwardedCount": 91, "completionRate": 26.2}], "currentWorkOrder": 0, "currentWorkOrderProgress": 26, "displayWorkOrder": {"workOrderNumber": "WO961329", "targetQuantity": 378, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-24T16:59:31.499522", "status": "pending", "mesForwardedCount": 87, "completionRate": 23.0}, "mesErrors": [{"timestamp": "2025-06-13T08:11:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050037"}}], "recentLogs": [{"message": "測試條碼-cd0e5359", "timestamp": "2025-06-13T16:35:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-347080a3", "timestamp": "2025-06-13T16:37:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-164f2809", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-dc45b50d", "timestamp": "2025-06-13T16:16:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-74d5ae46", "timestamp": "2025-06-13T16:22:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-7b465d23", "timestamp": "2025-06-13T16:09:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-1cbf0497", "timestamp": "2025-06-13T16:57:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-35dced7d", "timestamp": "2025-06-13T16:55:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-c6e522fe", "timestamp": "2025-06-13T16:21:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-52374240", "timestamp": "2025-06-13T16:52:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f86a0f59", "timestamp": "2025-06-13T16:45:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-9cf73e35", "timestamp": "2025-06-13T16:15:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-a28fd3b4", "timestamp": "2025-06-13T16:06:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-80a2f6c7", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-033ee5d2", "timestamp": "2025-06-13T16:51:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}]}, "S720050038": {"deviceId": "S720050038", "lineName": "A2-02", "sectionName": "INJECTION", "groupName": "INJECTION", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 996, "forwardedCount": 837, "unforwardedCount": 159, "lastLog": "測試產品條碼-cc69861f", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-03-17T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO851878", "targetQuantity": 413, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-13T16:59:31.499522", "status": "pending", "mesForwardedCount": 36, "completionRate": 44.5}, {"workOrderNumber": "WO662613", "targetQuantity": 99, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-13T16:59:31.499522", "status": "pending", "mesForwardedCount": 31, "completionRate": 17.4}, {"workOrderNumber": "WO474145", "targetQuantity": 494, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-12T16:59:31.499522", "status": "active", "mesForwardedCount": 39, "completionRate": 67.8}], "currentWorkOrder": 0, "currentWorkOrderProgress": 7, "displayWorkOrder": {"workOrderNumber": "WO851878", "targetQuantity": 413, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-13T16:59:31.499522", "status": "pending", "mesForwardedCount": 36, "completionRate": 44.5}, "mesErrors": [{"timestamp": "2025-06-13T06:33:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050038"}}, {"timestamp": "2025-06-13T03:44:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050038"}}, {"timestamp": "2025-06-13T14:44:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050038"}}], "recentLogs": [{"message": "測試條碼-f235040b", "timestamp": "2025-06-13T16:41:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-e43f0b6c", "timestamp": "2025-06-13T16:06:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-125a54d1", "timestamp": "2025-06-13T16:20:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-c1d44872", "timestamp": "2025-06-13T16:26:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-e06e3c98", "timestamp": "2025-06-13T16:07:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-d1a7a710", "timestamp": "2025-06-13T16:43:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-ac66fc59", "timestamp": "2025-06-13T16:26:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-a3612939", "timestamp": "2025-06-13T16:57:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-e94ff9d7", "timestamp": "2025-06-13T16:26:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}]}, "S720050039": {"deviceId": "S720050039", "lineName": "B2-02", "sectionName": "ASSEMBLY", "groupName": "ASSEMBLY", "stationName": "STATION_03", "isActive": true, "isMonitoring": true, "productCount": 389, "forwardedCount": 165, "unforwardedCount": 224, "lastLog": "測試產品條碼-8823c7d5", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-01-30T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO589572", "targetQuantity": 60, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-31T16:59:31.499522", "status": "completed", "mesForwardedCount": 80, "completionRate": 57.1}, {"workOrderNumber": "WO473047", "targetQuantity": 133, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-16T16:59:31.499522", "status": "active", "mesForwardedCount": 49, "completionRate": 82.4}, {"workOrderNumber": "WO751825", "targetQuantity": 467, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-14T16:59:31.499522", "status": "active", "mesForwardedCount": 69, "completionRate": 24.6}], "currentWorkOrder": 0, "currentWorkOrderProgress": 20, "displayWorkOrder": {"workOrderNumber": "WO589572", "targetQuantity": 60, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-31T16:59:31.499522", "status": "completed", "mesForwardedCount": 80, "completionRate": 57.1}, "mesErrors": [{"timestamp": "2025-06-13T16:56:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050039"}}, {"timestamp": "2025-06-13T00:06:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050039"}}, {"timestamp": "2025-06-13T13:43:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050039"}}, {"timestamp": "2025-06-13T03:46:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050039"}}], "recentLogs": [{"message": "測試條碼-b713fabb", "timestamp": "2025-06-13T16:55:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-3e75ae6d", "timestamp": "2025-06-13T16:54:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-84124f15", "timestamp": "2025-06-13T16:03:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-8212918b", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-4e032183", "timestamp": "2025-06-13T16:58:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-bd0c1e98", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1e9616e8", "timestamp": "2025-06-13T16:36:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-9bdd3684", "timestamp": "2025-06-13T16:08:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1a91e857", "timestamp": "2025-06-13T16:03:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1097fe7a", "timestamp": "2025-06-13T16:56:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-afa1373d", "timestamp": "2025-06-13T16:23:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-bbc33d91", "timestamp": "2025-06-13T16:29:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-18bad88a", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-2781c9df", "timestamp": "2025-06-13T16:42:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-90ecdd82", "timestamp": "2025-06-13T16:41:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-75cdcf69", "timestamp": "2025-06-13T15:59:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-4600f22b", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-a750d951", "timestamp": "2025-06-13T16:26:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}]}, "S720050040": {"deviceId": "S720050040", "lineName": "B2-02", "sectionName": "PACKAGING", "groupName": "TEST", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 733, "forwardedCount": 532, "unforwardedCount": 201, "lastLog": "測試產品條碼-b7574d53", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-07-26T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO225679", "targetQuantity": 199, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-28T16:59:31.499522", "status": "active", "mesForwardedCount": 63, "completionRate": 48.8}, {"workOrderNumber": "WO928437", "targetQuantity": 130, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-14T16:59:31.499522", "status": "pending", "mesForwardedCount": 73, "completionRate": 39.8}, {"workOrderNumber": "WO411810", "targetQuantity": 302, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-16T16:59:31.499522", "status": "completed", "mesForwardedCount": 55, "completionRate": 55.9}], "currentWorkOrder": 2, "currentWorkOrderProgress": 64, "displayWorkOrder": {"workOrderNumber": "WO411810", "targetQuantity": 302, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-16T16:59:31.499522", "status": "completed", "mesForwardedCount": 55, "completionRate": 55.9}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-260a663e", "timestamp": "2025-06-13T16:01:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-47c29cd7", "timestamp": "2025-06-13T16:54:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-f24a56e8", "timestamp": "2025-06-13T16:19:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-93134e78", "timestamp": "2025-06-13T16:46:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-d25d9b79", "timestamp": "2025-06-13T16:28:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-defb9d24", "timestamp": "2025-06-13T16:09:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}]}, "S720050041": {"deviceId": "S720050041", "lineName": "B1-01", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 403, "forwardedCount": 78, "unforwardedCount": 325, "lastLog": "測試產品條碼-9d316e12", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-08-05T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO932928", "targetQuantity": 227, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-21T16:59:31.499522", "status": "completed", "mesForwardedCount": 29, "completionRate": 14.0}, {"workOrderNumber": "WO101336", "targetQuantity": 341, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-03T16:59:31.499522", "status": "completed", "mesForwardedCount": 36, "completionRate": 53.6}, {"workOrderNumber": "WO439400", "targetQuantity": 319, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-11T16:59:31.499522", "status": "active", "mesForwardedCount": 57, "completionRate": 7.2}], "currentWorkOrder": 0, "currentWorkOrderProgress": 32, "displayWorkOrder": {"workOrderNumber": "WO932928", "targetQuantity": 227, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-21T16:59:31.499522", "status": "completed", "mesForwardedCount": 29, "completionRate": 14.0}, "mesErrors": [{"timestamp": "2025-06-12T18:58:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050041"}}, {"timestamp": "2025-06-13T02:42:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050041"}}], "recentLogs": [{"message": "測試條碼-ae1f3565", "timestamp": "2025-06-13T16:27:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-63b0c7a5", "timestamp": "2025-06-13T16:57:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-dc1db69e", "timestamp": "2025-06-13T16:51:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-cb91b4ff", "timestamp": "2025-06-13T16:01:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-90743617", "timestamp": "2025-06-13T16:01:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-f1249b5b", "timestamp": "2025-06-13T16:57:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-9b672f71", "timestamp": "2025-06-13T16:55:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-fdce6cab", "timestamp": "2025-06-13T16:11:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-e94b296b", "timestamp": "2025-06-13T16:25:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-246031f3", "timestamp": "2025-06-13T16:16:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-6587d223", "timestamp": "2025-06-13T16:27:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-e3709e1f", "timestamp": "2025-06-13T16:12:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-4fb1ad42", "timestamp": "2025-06-13T16:55:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-91bf6123", "timestamp": "2025-06-13T16:36:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-8f00e749", "timestamp": "2025-06-13T16:20:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1656dd55", "timestamp": "2025-06-13T16:23:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-9001ba30", "timestamp": "2025-06-13T16:38:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-2c8b9e0e", "timestamp": "2025-06-13T16:55:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-9f611216", "timestamp": "2025-06-13T16:30:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}]}, "S720050042": {"deviceId": "S720050042", "lineName": "A1-01", "sectionName": "PACKAGING", "groupName": "TEST", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 40, "forwardedCount": 16, "unforwardedCount": 24, "lastLog": "測試產品條碼-896167a3", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-08-19T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO630034", "targetQuantity": 319, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-15T16:59:31.499522", "status": "completed", "mesForwardedCount": 6, "completionRate": 76.5}, {"workOrderNumber": "WO112758", "targetQuantity": 164, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-22T16:59:31.499522", "status": "active", "mesForwardedCount": 19, "completionRate": 85.3}, {"workOrderNumber": "WO188290", "targetQuantity": 259, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-22T16:59:31.499522", "status": "completed", "mesForwardedCount": 57, "completionRate": 86.8}], "currentWorkOrder": 2, "currentWorkOrderProgress": 3, "displayWorkOrder": {"workOrderNumber": "WO188290", "targetQuantity": 259, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-22T16:59:31.499522", "status": "completed", "mesForwardedCount": 57, "completionRate": 86.8}, "mesErrors": [{"timestamp": "2025-06-13T03:43:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050042"}}, {"timestamp": "2025-06-13T08:25:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050042"}}, {"timestamp": "2025-06-13T13:54:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050042"}}], "recentLogs": [{"message": "測試條碼-c0a97c63", "timestamp": "2025-06-13T16:58:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-1fb91b49", "timestamp": "2025-06-13T16:16:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-99156e69", "timestamp": "2025-06-13T16:00:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-8b5293c9", "timestamp": "2025-06-13T16:00:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-fb3fa0d3", "timestamp": "2025-06-13T16:15:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-047a3477", "timestamp": "2025-06-13T16:45:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-931409b3", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-02af849b", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-40a04e9e", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-bc41630a", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-a7a3d676", "timestamp": "2025-06-13T16:23:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-58b0c816", "timestamp": "2025-06-13T16:48:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-9064eb4c", "timestamp": "2025-06-13T16:27:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-584e2f1c", "timestamp": "2025-06-13T16:30:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-c6a38614", "timestamp": "2025-06-13T16:56:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}]}, "S720050043": {"deviceId": "S720050043", "lineName": "A2-01", "sectionName": "INJECTION", "groupName": "INJECTION", "stationName": "STATION_03", "isActive": true, "isMonitoring": true, "productCount": 598, "forwardedCount": 30, "unforwardedCount": 568, "lastLog": "測試產品條碼-7ce9dc6e", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-08-22T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO307051", "targetQuantity": 198, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-10T16:59:31.499522", "status": "pending", "mesForwardedCount": 17, "completionRate": 95.5}, {"workOrderNumber": "WO747588", "targetQuantity": 131, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-24T16:59:31.499522", "status": "pending", "mesForwardedCount": 29, "completionRate": 64.1}, {"workOrderNumber": "WO262381", "targetQuantity": 126, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-16T16:59:31.499522", "status": "active", "mesForwardedCount": 91, "completionRate": 64.9}], "currentWorkOrder": 1, "currentWorkOrderProgress": 85, "displayWorkOrder": {"workOrderNumber": "WO747588", "targetQuantity": 131, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-24T16:59:31.499522", "status": "pending", "mesForwardedCount": 29, "completionRate": 64.1}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-199af903", "timestamp": "2025-06-13T16:32:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-b3128625", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-2fcf5751", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-5082e202", "timestamp": "2025-06-13T16:11:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-7b347f49", "timestamp": "2025-06-13T16:30:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a691cb63", "timestamp": "2025-06-13T16:22:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-bb39d98f", "timestamp": "2025-06-13T16:44:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-2d11362f", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-b7a97af2", "timestamp": "2025-06-13T16:19:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-373a2374", "timestamp": "2025-06-13T16:03:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-489e671f", "timestamp": "2025-06-13T16:13:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}]}, "S720050044": {"deviceId": "S720050044", "lineName": "A1-01", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 196, "forwardedCount": 128, "unforwardedCount": 68, "lastLog": "測試產品條碼-f1db89ff", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-01-26T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO365509", "targetQuantity": 484, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-02T16:59:31.499522", "status": "active", "mesForwardedCount": 7, "completionRate": 23.9}, {"workOrderNumber": "WO478819", "targetQuantity": 51, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-29T16:59:31.499522", "status": "pending", "mesForwardedCount": 43, "completionRate": 17.0}, {"workOrderNumber": "WO267609", "targetQuantity": 311, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-29T16:59:31.499522", "status": "pending", "mesForwardedCount": 33, "completionRate": 13.8}], "currentWorkOrder": 0, "currentWorkOrderProgress": 2, "displayWorkOrder": {"workOrderNumber": "WO365509", "targetQuantity": 484, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-02T16:59:31.499522", "status": "active", "mesForwardedCount": 7, "completionRate": 23.9}, "mesErrors": [{"timestamp": "2025-06-12T22:47:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050044"}}, {"timestamp": "2025-06-12T22:15:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050044"}}, {"timestamp": "2025-06-12T19:01:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050044"}}, {"timestamp": "2025-06-13T08:49:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050044"}}], "recentLogs": [{"message": "測試條碼-04160c6b", "timestamp": "2025-06-13T16:56:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-9b4e495d", "timestamp": "2025-06-13T16:07:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-7f0b6d18", "timestamp": "2025-06-13T16:16:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-42ffef51", "timestamp": "2025-06-13T16:15:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-346dea89", "timestamp": "2025-06-13T16:10:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-8e630ef5", "timestamp": "2025-06-13T15:59:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-e8546fb0", "timestamp": "2025-06-13T16:14:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ccfce49f", "timestamp": "2025-06-13T16:25:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-f3369c5b", "timestamp": "2025-06-13T16:16:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}]}, "S720050045": {"deviceId": "S720050045", "lineName": "A1-02", "sectionName": "INJECTION", "groupName": "TEST", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 52, "forwardedCount": 49, "unforwardedCount": 3, "lastLog": "測試產品條碼-0adefb0b", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-03-27T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO673732", "targetQuantity": 70, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-12T16:59:31.499522", "status": "pending", "mesForwardedCount": 62, "completionRate": 74.1}, {"workOrderNumber": "WO720151", "targetQuantity": 260, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-14T16:59:31.499522", "status": "completed", "mesForwardedCount": 22, "completionRate": 70.9}, {"workOrderNumber": "WO922232", "targetQuantity": 459, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-11T16:59:31.499522", "status": "pending", "mesForwardedCount": 36, "completionRate": 97.0}], "currentWorkOrder": 0, "currentWorkOrderProgress": 18, "displayWorkOrder": {"workOrderNumber": "WO673732", "targetQuantity": 70, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-12T16:59:31.499522", "status": "pending", "mesForwardedCount": 62, "completionRate": 74.1}, "mesErrors": [{"timestamp": "2025-06-12T18:26:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050045"}}, {"timestamp": "2025-06-13T03:36:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050045"}}, {"timestamp": "2025-06-13T08:54:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050045"}}, {"timestamp": "2025-06-12T19:17:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050045"}}], "recentLogs": [{"message": "測試條碼-ff8c826d", "timestamp": "2025-06-13T16:05:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-4fc02288", "timestamp": "2025-06-13T16:29:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-8c2667f4", "timestamp": "2025-06-13T16:32:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-98294de9", "timestamp": "2025-06-13T16:51:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-128016a4", "timestamp": "2025-06-13T16:22:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-e5149243", "timestamp": "2025-06-13T16:10:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-e2c893bb", "timestamp": "2025-06-13T16:21:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}]}, "S720050046": {"deviceId": "S720050046", "lineName": "A1-01", "sectionName": "TESTING", "groupName": "TEST", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 757, "forwardedCount": 20, "unforwardedCount": 737, "lastLog": "測試產品條碼-1f0a0475", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-04-03T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO595017", "targetQuantity": 147, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-13T16:59:31.499522", "status": "active", "mesForwardedCount": 65, "completionRate": 71.9}, {"workOrderNumber": "WO779100", "targetQuantity": 210, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-23T16:59:31.499522", "status": "completed", "mesForwardedCount": 22, "completionRate": 55.2}, {"workOrderNumber": "WO861554", "targetQuantity": 319, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-27T16:59:31.499522", "status": "pending", "mesForwardedCount": 8, "completionRate": 99.2}], "currentWorkOrder": 1, "currentWorkOrderProgress": 93, "displayWorkOrder": {"workOrderNumber": "WO779100", "targetQuantity": 210, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-23T16:59:31.499522", "status": "completed", "mesForwardedCount": 22, "completionRate": 55.2}, "mesErrors": [{"timestamp": "2025-06-13T00:37:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050046"}}, {"timestamp": "2025-06-13T11:36:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050046"}}, {"timestamp": "2025-06-13T16:03:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050046"}}, {"timestamp": "2025-06-13T04:03:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050046"}}], "recentLogs": [{"message": "測試條碼-24bd48d5", "timestamp": "2025-06-13T16:06:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-a2551c5e", "timestamp": "2025-06-13T16:57:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-92311b3e", "timestamp": "2025-06-13T16:43:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-a9bb15c2", "timestamp": "2025-06-13T16:49:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-eecb8e5f", "timestamp": "2025-06-13T16:58:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-716a6164", "timestamp": "2025-06-13T16:42:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-0772fa97", "timestamp": "2025-06-13T16:24:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-d03c3e38", "timestamp": "2025-06-13T16:36:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-5ee39511", "timestamp": "2025-06-13T16:29:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-81ebba5e", "timestamp": "2025-06-13T16:50:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}]}, "S720050047": {"deviceId": "S720050047", "lineName": "B2-01", "sectionName": "ASSEMBLY", "groupName": "ASSEMBLY", "stationName": "STATION_03", "isActive": true, "isMonitoring": true, "productCount": 69, "forwardedCount": 14, "unforwardedCount": 55, "lastLog": "測試產品條碼-b745fb4b", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-03-05T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO347279", "targetQuantity": 185, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-17T16:59:31.499522", "status": "pending", "mesForwardedCount": 88, "completionRate": 83.7}, {"workOrderNumber": "WO126672", "targetQuantity": 71, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-09T16:59:31.499522", "status": "pending", "mesForwardedCount": 65, "completionRate": 8.1}, {"workOrderNumber": "WO604341", "targetQuantity": 187, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-07T16:59:31.499522", "status": "pending", "mesForwardedCount": 19, "completionRate": 5.0}], "currentWorkOrder": 2, "currentWorkOrderProgress": 36, "displayWorkOrder": {"workOrderNumber": "WO604341", "targetQuantity": 187, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-07T16:59:31.499522", "status": "pending", "mesForwardedCount": 19, "completionRate": 5.0}, "mesErrors": [{"timestamp": "2025-06-13T09:25:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050047"}}], "recentLogs": [{"message": "測試條碼-9fce1d07", "timestamp": "2025-06-13T16:21:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-6c714e7f", "timestamp": "2025-06-13T16:14:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-faf06363", "timestamp": "2025-06-13T16:21:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-f79c0bba", "timestamp": "2025-06-13T16:00:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-5bafc034", "timestamp": "2025-06-13T16:15:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-59fa2830", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-14d7eb05", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-87aa24d8", "timestamp": "2025-06-13T16:21:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-3818bd6d", "timestamp": "2025-06-13T16:47:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-bcb16ac4", "timestamp": "2025-06-13T16:58:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-a45d8b64", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-b9e36300", "timestamp": "2025-06-13T16:38:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-cae039ab", "timestamp": "2025-06-13T16:18:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}]}, "S720050048": {"deviceId": "S720050048", "lineName": "A2-01", "sectionName": "INJECTION", "groupName": "TEST", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 19, "forwardedCount": 6, "unforwardedCount": 13, "lastLog": "測試產品條碼-678a6467", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-04-19T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO262819", "targetQuantity": 418, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-04T16:59:31.499522", "status": "completed", "mesForwardedCount": 94, "completionRate": 94.7}, {"workOrderNumber": "WO289393", "targetQuantity": 76, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-19T16:59:31.499522", "status": "active", "mesForwardedCount": 2, "completionRate": 83.8}, {"workOrderNumber": "WO270654", "targetQuantity": 403, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-16T16:59:31.499522", "status": "pending", "mesForwardedCount": 85, "completionRate": 63.7}], "currentWorkOrder": 1, "currentWorkOrderProgress": 26, "displayWorkOrder": {"workOrderNumber": "WO289393", "targetQuantity": 76, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-19T16:59:31.499522", "status": "active", "mesForwardedCount": 2, "completionRate": 83.8}, "mesErrors": [{"timestamp": "2025-06-12T17:38:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050048"}}, {"timestamp": "2025-06-12T21:42:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050048"}}, {"timestamp": "2025-06-13T10:39:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050048"}}, {"timestamp": "2025-06-12T20:08:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050048"}}], "recentLogs": [{"message": "測試條碼-547<PERSON><PERSON>f", "timestamp": "2025-06-13T16:12:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-8a9e6f79", "timestamp": "2025-06-13T16:13:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-a2ac117f", "timestamp": "2025-06-13T16:11:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-0e1362f3", "timestamp": "2025-06-13T16:01:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-e4d3343e", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-05c4514e", "timestamp": "2025-06-13T16:53:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-76b811b7", "timestamp": "2025-06-13T16:03:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-d973539d", "timestamp": "2025-06-13T16:26:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-cbed0dd7", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-07f79e9d", "timestamp": "2025-06-13T16:45:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1f260508", "timestamp": "2025-06-13T16:30:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-4233efbf", "timestamp": "2025-06-13T16:33:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-19e8a86b", "timestamp": "2025-06-13T16:42:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-aa0fd4d0", "timestamp": "2025-06-13T16:20:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-d2f8e3f3", "timestamp": "2025-06-13T16:35:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-86522f99", "timestamp": "2025-06-13T16:10:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-5d0d8f67", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-c783224b", "timestamp": "2025-06-13T16:26:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-15f58b4f", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-7d1f4ca5", "timestamp": "2025-06-13T16:28:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}]}, "S720050049": {"deviceId": "S720050049", "lineName": "B2-02", "sectionName": "INJECTION", "groupName": "ASSEMBLY", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 981, "forwardedCount": 963, "unforwardedCount": 18, "lastLog": "測試產品條碼-2e5a914f", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-07-29T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO284908", "targetQuantity": 233, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-13T16:59:31.499522", "status": "pending", "mesForwardedCount": 18, "completionRate": 72.6}, {"workOrderNumber": "WO370603", "targetQuantity": 448, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-24T16:59:31.499522", "status": "pending", "mesForwardedCount": 61, "completionRate": 11.8}, {"workOrderNumber": "WO372079", "targetQuantity": 458, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-02T16:59:31.499522", "status": "active", "mesForwardedCount": 52, "completionRate": 25.7}], "currentWorkOrder": 0, "currentWorkOrderProgress": 68, "displayWorkOrder": {"workOrderNumber": "WO284908", "targetQuantity": 233, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-13T16:59:31.499522", "status": "pending", "mesForwardedCount": 18, "completionRate": 72.6}, "mesErrors": [{"timestamp": "2025-06-13T13:32:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050049"}}, {"timestamp": "2025-06-12T18:01:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050049"}}, {"timestamp": "2025-06-12T18:21:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050049"}}], "recentLogs": [{"message": "測試條碼-744f0c2c", "timestamp": "2025-06-13T16:57:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-90a941e0", "timestamp": "2025-06-13T16:53:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-c9ac910c", "timestamp": "2025-06-13T16:10:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-ad63cc85", "timestamp": "2025-06-13T16:58:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-85c396ac", "timestamp": "2025-06-13T16:00:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-c991ca4b", "timestamp": "2025-06-13T16:09:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-d75d258e", "timestamp": "2025-06-13T16:08:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-3df41fc4", "timestamp": "2025-06-13T16:48:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-15da6ef0", "timestamp": "2025-06-13T16:31:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-783f3ab4", "timestamp": "2025-06-13T16:16:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-9d5402cf", "timestamp": "2025-06-13T16:54:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-152c4cd6", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-cffa8961", "timestamp": "2025-06-13T16:47:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}]}, "S720050050": {"deviceId": "S720050050", "lineName": "A1-01", "sectionName": "TESTING", "groupName": "TEST", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 623, "forwardedCount": 311, "unforwardedCount": 312, "lastLog": "測試產品條碼-a022a50c", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-07-23T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO853824", "targetQuantity": 459, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-31T16:59:31.499522", "status": "active", "mesForwardedCount": 68, "completionRate": 86.5}, {"workOrderNumber": "WO706993", "targetQuantity": 423, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-06T16:59:31.499522", "status": "completed", "mesForwardedCount": 98, "completionRate": 58.2}, {"workOrderNumber": "WO291214", "targetQuantity": 210, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-26T16:59:31.499522", "status": "completed", "mesForwardedCount": 100, "completionRate": 24.4}], "currentWorkOrder": 2, "currentWorkOrderProgress": 44, "displayWorkOrder": {"workOrderNumber": "WO291214", "targetQuantity": 210, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-26T16:59:31.499522", "status": "completed", "mesForwardedCount": 100, "completionRate": 24.4}, "mesErrors": [{"timestamp": "2025-06-13T10:46:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050050"}}, {"timestamp": "2025-06-12T21:47:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050050"}}, {"timestamp": "2025-06-12T20:04:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050050"}}, {"timestamp": "2025-06-12T19:55:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050050"}}], "recentLogs": [{"message": "測試條碼-19d9db24", "timestamp": "2025-06-13T16:40:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-2b3452d3", "timestamp": "2025-06-13T16:45:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-8ca5854a", "timestamp": "2025-06-13T16:03:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-5048cb27", "timestamp": "2025-06-13T16:51:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-cd3cf26f", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-eec3c47d", "timestamp": "2025-06-13T16:50:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-a8076984", "timestamp": "2025-06-13T16:33:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-0750f8b6", "timestamp": "2025-06-13T16:10:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-48b4841e", "timestamp": "2025-06-13T16:20:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-724b6b80", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-bae9f77b", "timestamp": "2025-06-13T16:12:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-3dc85f7b", "timestamp": "2025-06-13T16:45:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}]}, "S720050051": {"deviceId": "S720050051", "lineName": "A2-02", "sectionName": "ASSEMBLY", "groupName": "PACK", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 771, "forwardedCount": 706, "unforwardedCount": 65, "lastLog": "測試產品條碼-038a6159", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-08-03T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO278569", "targetQuantity": 117, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-27T16:59:31.499522", "status": "pending", "mesForwardedCount": 66, "completionRate": 31.1}, {"workOrderNumber": "WO245611", "targetQuantity": 440, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-24T16:59:31.499522", "status": "active", "mesForwardedCount": 51, "completionRate": 86.1}, {"workOrderNumber": "WO940156", "targetQuantity": 298, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-13T16:59:31.499522", "status": "completed", "mesForwardedCount": 14, "completionRate": 74.7}], "currentWorkOrder": 1, "currentWorkOrderProgress": 87, "displayWorkOrder": {"workOrderNumber": "WO245611", "targetQuantity": 440, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-24T16:59:31.499522", "status": "active", "mesForwardedCount": 51, "completionRate": 86.1}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-af50ce75", "timestamp": "2025-06-13T16:44:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1e4abb9c", "timestamp": "2025-06-13T16:16:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-d7080664", "timestamp": "2025-06-13T16:48:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-473d10b5", "timestamp": "2025-06-13T16:52:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-332323b1", "timestamp": "2025-06-13T16:57:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}]}, "S720050052": {"deviceId": "S720050052", "lineName": "B1-02", "sectionName": "ASSEMBLY", "groupName": "TEST", "stationName": "STATION_03", "isActive": true, "isMonitoring": true, "productCount": 599, "forwardedCount": 286, "unforwardedCount": 313, "lastLog": "測試產品條碼-305cc2b1", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-12-27T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO989631", "targetQuantity": 407, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-24T16:59:31.499522", "status": "completed", "mesForwardedCount": 21, "completionRate": 76.9}, {"workOrderNumber": "WO852196", "targetQuantity": 254, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-02T16:59:31.499522", "status": "completed", "mesForwardedCount": 58, "completionRate": 10.9}, {"workOrderNumber": "WO346370", "targetQuantity": 276, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-10T16:59:31.499522", "status": "completed", "mesForwardedCount": 51, "completionRate": 15.7}], "currentWorkOrder": 2, "currentWorkOrderProgress": 90, "displayWorkOrder": {"workOrderNumber": "WO346370", "targetQuantity": 276, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-10T16:59:31.499522", "status": "completed", "mesForwardedCount": 51, "completionRate": 15.7}, "mesErrors": [{"timestamp": "2025-06-12T19:15:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050052"}}, {"timestamp": "2025-06-13T10:45:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050052"}}, {"timestamp": "2025-06-12T23:27:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050052"}}], "recentLogs": [{"message": "測試條碼-2c643f86", "timestamp": "2025-06-13T16:30:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d6e27b13", "timestamp": "2025-06-13T16:19:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-15547eb8", "timestamp": "2025-06-13T16:28:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-883dd5a4", "timestamp": "2025-06-13T16:23:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-e94e6c2a", "timestamp": "2025-06-13T16:05:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}]}, "S720050053": {"deviceId": "S720050053", "lineName": "A1-01", "sectionName": "PACKAGING", "groupName": "INJECTION", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 218, "forwardedCount": 44, "unforwardedCount": 174, "lastLog": "測試產品條碼-34f1a434", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-09-20T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO632504", "targetQuantity": 493, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-10T16:59:31.499522", "status": "completed", "mesForwardedCount": 72, "completionRate": 38.5}, {"workOrderNumber": "WO655229", "targetQuantity": 87, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-30T16:59:31.499522", "status": "pending", "mesForwardedCount": 62, "completionRate": 37.0}, {"workOrderNumber": "WO301977", "targetQuantity": 396, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-08T16:59:31.499522", "status": "completed", "mesForwardedCount": 57, "completionRate": 65.3}], "currentWorkOrder": 2, "currentWorkOrderProgress": 56, "displayWorkOrder": {"workOrderNumber": "WO301977", "targetQuantity": 396, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-08T16:59:31.499522", "status": "completed", "mesForwardedCount": 57, "completionRate": 65.3}, "mesErrors": [{"timestamp": "2025-06-13T09:41:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050053"}}, {"timestamp": "2025-06-13T12:33:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050053"}}], "recentLogs": [{"message": "測試條碼-367021b8", "timestamp": "2025-06-13T16:09:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-210b349e", "timestamp": "2025-06-13T16:42:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-264f85d9", "timestamp": "2025-06-13T16:29:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-cb321af1", "timestamp": "2025-06-13T16:55:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-0b9a5929", "timestamp": "2025-06-13T16:18:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-c50d8197", "timestamp": "2025-06-13T16:07:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-da742bca", "timestamp": "2025-06-13T16:15:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-6a8fb6b1", "timestamp": "2025-06-13T16:13:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-66bd26dc", "timestamp": "2025-06-13T16:53:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-cc0448d7", "timestamp": "2025-06-13T16:50:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-49243422", "timestamp": "2025-06-13T16:29:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-603363ef", "timestamp": "2025-06-13T16:22:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-e530c0f4", "timestamp": "2025-06-13T16:41:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-d9c0b0a8", "timestamp": "2025-06-13T16:29:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1ccaba27", "timestamp": "2025-06-13T16:06:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}]}, "S720050054": {"deviceId": "S720050054", "lineName": "A2-02", "sectionName": "PACKAGING", "groupName": "INJECTION", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 921, "forwardedCount": 73, "unforwardedCount": 848, "lastLog": "測試產品條碼-f74c2b9d", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-01-13T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO995966", "targetQuantity": 273, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-13T16:59:31.499522", "status": "completed", "mesForwardedCount": 33, "completionRate": 73.7}, {"workOrderNumber": "WO898090", "targetQuantity": 83, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-31T16:59:31.499522", "status": "completed", "mesForwardedCount": 3, "completionRate": 37.3}, {"workOrderNumber": "WO861631", "targetQuantity": 86, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-07T16:59:31.499522", "status": "active", "mesForwardedCount": 81, "completionRate": 44.9}], "currentWorkOrder": 2, "currentWorkOrderProgress": 61, "displayWorkOrder": {"workOrderNumber": "WO861631", "targetQuantity": 86, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-07T16:59:31.499522", "status": "active", "mesForwardedCount": 81, "completionRate": 44.9}, "mesErrors": [{"timestamp": "2025-06-12T18:32:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050054"}}, {"timestamp": "2025-06-13T06:58:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050054"}}, {"timestamp": "2025-06-13T16:28:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050054"}}], "recentLogs": [{"message": "測試條碼-666aea59", "timestamp": "2025-06-13T16:05:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-a0975793", "timestamp": "2025-06-13T16:47:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-2f1a9081", "timestamp": "2025-06-13T16:50:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-971a1010", "timestamp": "2025-06-13T16:20:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-1a9a2951", "timestamp": "2025-06-13T16:36:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-318dc77d", "timestamp": "2025-06-13T16:47:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-1afc589c", "timestamp": "2025-06-13T16:48:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-a470d5a9", "timestamp": "2025-06-13T16:55:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-04b41a51", "timestamp": "2025-06-13T16:34:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-8ef7542b", "timestamp": "2025-06-13T16:11:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}]}, "S720050055": {"deviceId": "S720050055", "lineName": "A2-02", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 114, "forwardedCount": 12, "unforwardedCount": 102, "lastLog": "測試產品條碼-47360324", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-03-05T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO675465", "targetQuantity": 325, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-12T16:59:31.499522", "status": "pending", "mesForwardedCount": 75, "completionRate": 57.5}, {"workOrderNumber": "WO585529", "targetQuantity": 124, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-22T16:59:31.499522", "status": "completed", "mesForwardedCount": 48, "completionRate": 67.4}, {"workOrderNumber": "WO975356", "targetQuantity": 334, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-23T16:59:31.499522", "status": "pending", "mesForwardedCount": 64, "completionRate": 30.4}], "currentWorkOrder": 1, "currentWorkOrderProgress": 74, "displayWorkOrder": {"workOrderNumber": "WO585529", "targetQuantity": 124, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-22T16:59:31.499522", "status": "completed", "mesForwardedCount": 48, "completionRate": 67.4}, "mesErrors": [{"timestamp": "2025-06-12T23:30:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050055"}}, {"timestamp": "2025-06-12T18:44:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050055"}}], "recentLogs": [{"message": "測試條碼-c65c9a16", "timestamp": "2025-06-13T16:30:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-22617955", "timestamp": "2025-06-13T16:17:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-20fb8bc3", "timestamp": "2025-06-13T16:14:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-3f4b5b37", "timestamp": "2025-06-13T16:26:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e5f19b13", "timestamp": "2025-06-13T16:26:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-450dacd6", "timestamp": "2025-06-13T16:26:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-17b591b0", "timestamp": "2025-06-13T16:28:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-2311a58e", "timestamp": "2025-06-13T16:53:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-8723b96d", "timestamp": "2025-06-13T16:36:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}]}, "S720050056": {"deviceId": "S720050056", "lineName": "A1-01", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 805, "forwardedCount": 601, "unforwardedCount": 204, "lastLog": "測試產品條碼-e9815a3b", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-12-04T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO604350", "targetQuantity": 450, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-17T16:59:31.499522", "status": "pending", "mesForwardedCount": 45, "completionRate": 94.9}, {"workOrderNumber": "WO434752", "targetQuantity": 464, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-12T16:59:31.499522", "status": "completed", "mesForwardedCount": 97, "completionRate": 44.1}, {"workOrderNumber": "WO545392", "targetQuantity": 69, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-03T16:59:31.499522", "status": "completed", "mesForwardedCount": 66, "completionRate": 64.0}], "currentWorkOrder": 0, "currentWorkOrderProgress": 16, "displayWorkOrder": {"workOrderNumber": "WO604350", "targetQuantity": 450, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-17T16:59:31.499522", "status": "pending", "mesForwardedCount": 45, "completionRate": 94.9}, "mesErrors": [{"timestamp": "2025-06-13T02:24:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050056"}}, {"timestamp": "2025-06-13T12:17:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050056"}}], "recentLogs": [{"message": "測試條碼-666e9466", "timestamp": "2025-06-13T16:18:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-55699cb9", "timestamp": "2025-06-13T16:35:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-ee409fdb", "timestamp": "2025-06-13T16:21:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-3a9b4007", "timestamp": "2025-06-13T16:41:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-6dee01ef", "timestamp": "2025-06-13T16:23:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-e423fb78", "timestamp": "2025-06-13T16:48:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}]}, "S720050057": {"deviceId": "S720050057", "lineName": "B2-02", "sectionName": "PACKAGING", "groupName": "INJECTION", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 470, "forwardedCount": 197, "unforwardedCount": 273, "lastLog": "測試產品條碼-5e2db659", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-08-03T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO426520", "targetQuantity": 108, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-27T16:59:31.499522", "status": "pending", "mesForwardedCount": 65, "completionRate": 14.2}, {"workOrderNumber": "WO741160", "targetQuantity": 425, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-11T16:59:31.499522", "status": "pending", "mesForwardedCount": 26, "completionRate": 59.0}, {"workOrderNumber": "WO305393", "targetQuantity": 183, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-13T16:59:31.499522", "status": "pending", "mesForwardedCount": 58, "completionRate": 68.3}], "currentWorkOrder": 0, "currentWorkOrderProgress": 94, "displayWorkOrder": {"workOrderNumber": "WO426520", "targetQuantity": 108, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-27T16:59:31.499522", "status": "pending", "mesForwardedCount": 65, "completionRate": 14.2}, "mesErrors": [{"timestamp": "2025-06-13T05:10:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050057"}}, {"timestamp": "2025-06-12T20:49:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050057"}}, {"timestamp": "2025-06-13T16:46:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050057"}}, {"timestamp": "2025-06-13T06:31:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050057"}}], "recentLogs": [{"message": "測試條碼-2243e96b", "timestamp": "2025-06-13T16:42:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a07335df", "timestamp": "2025-06-13T16:50:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-d5e88ebc", "timestamp": "2025-06-13T16:45:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-6e085269", "timestamp": "2025-06-13T16:06:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-1fd75e2e", "timestamp": "2025-06-13T16:38:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-2a4ba7cb", "timestamp": "2025-06-13T16:58:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-99f177af", "timestamp": "2025-06-13T16:38:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-1b4d4aed", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-14aead84", "timestamp": "2025-06-13T16:57:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-aa0c8fa7", "timestamp": "2025-06-13T16:35:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}]}, "S720050058": {"deviceId": "S720050058", "lineName": "A1-01", "sectionName": "INJECTION", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 252, "forwardedCount": 163, "unforwardedCount": 89, "lastLog": "測試產品條碼-ed1045cc", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-02-14T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO498229", "targetQuantity": 432, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-05T16:59:31.499522", "status": "completed", "mesForwardedCount": 29, "completionRate": 85.7}, {"workOrderNumber": "WO600446", "targetQuantity": 283, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-22T16:59:31.499522", "status": "completed", "mesForwardedCount": 77, "completionRate": 90.9}, {"workOrderNumber": "WO969162", "targetQuantity": 155, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-08T16:59:31.499522", "status": "completed", "mesForwardedCount": 22, "completionRate": 91.8}], "currentWorkOrder": 2, "currentWorkOrderProgress": 88, "displayWorkOrder": {"workOrderNumber": "WO969162", "targetQuantity": 155, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-08T16:59:31.499522", "status": "completed", "mesForwardedCount": 22, "completionRate": 91.8}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-c7ca0231", "timestamp": "2025-06-13T16:56:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-ef95cda1", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-99a287c8", "timestamp": "2025-06-13T16:05:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-939297b6", "timestamp": "2025-06-13T16:57:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-9afb6c4a", "timestamp": "2025-06-13T16:26:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-0adda02a", "timestamp": "2025-06-13T16:55:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}]}, "S720050059": {"deviceId": "S720050059", "lineName": "B1-02", "sectionName": "ASSEMBLY", "groupName": "TEST", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 166, "forwardedCount": 72, "unforwardedCount": 94, "lastLog": "測試產品條碼-d933cf39", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-05-28T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO808565", "targetQuantity": 405, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-09T16:59:31.499522", "status": "pending", "mesForwardedCount": 20, "completionRate": 52.3}, {"workOrderNumber": "WO201057", "targetQuantity": 224, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-20T16:59:31.499522", "status": "active", "mesForwardedCount": 100, "completionRate": 76.8}, {"workOrderNumber": "WO631231", "targetQuantity": 406, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-30T16:59:31.499522", "status": "active", "mesForwardedCount": 18, "completionRate": 62.8}], "currentWorkOrder": 2, "currentWorkOrderProgress": 30, "displayWorkOrder": {"workOrderNumber": "WO631231", "targetQuantity": 406, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-30T16:59:31.499522", "status": "active", "mesForwardedCount": 18, "completionRate": 62.8}, "mesErrors": [{"timestamp": "2025-06-13T06:37:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050059"}}, {"timestamp": "2025-06-12T23:06:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050059"}}], "recentLogs": [{"message": "測試條碼-973b8982", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-<PERSON><PERSON><PERSON><PERSON>", "timestamp": "2025-06-13T16:06:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-19849874", "timestamp": "2025-06-13T16:46:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-ad4d4c54", "timestamp": "2025-06-13T16:19:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-24c3f54a", "timestamp": "2025-06-13T16:57:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-340ded46", "timestamp": "2025-06-13T16:03:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}]}, "S720050060": {"deviceId": "S720050060", "lineName": "A1-01", "sectionName": "ASSEMBLY", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 56, "forwardedCount": 19, "unforwardedCount": 37, "lastLog": "測試產品條碼-3f1b210f", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-12-01T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO861242", "targetQuantity": 470, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-28T16:59:31.499522", "status": "pending", "mesForwardedCount": 0, "completionRate": 93.7}, {"workOrderNumber": "WO165855", "targetQuantity": 379, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-12T16:59:31.499522", "status": "pending", "mesForwardedCount": 55, "completionRate": 65.0}, {"workOrderNumber": "WO264967", "targetQuantity": 356, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-28T16:59:31.499522", "status": "completed", "mesForwardedCount": 9, "completionRate": 9.9}], "currentWorkOrder": 0, "currentWorkOrderProgress": 84, "displayWorkOrder": {"workOrderNumber": "WO861242", "targetQuantity": 470, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-28T16:59:31.499522", "status": "pending", "mesForwardedCount": 0, "completionRate": 93.7}, "mesErrors": [{"timestamp": "2025-06-12T19:16:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050060"}}, {"timestamp": "2025-06-13T01:41:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050060"}}, {"timestamp": "2025-06-12T17:02:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050060"}}, {"timestamp": "2025-06-12T17:11:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050060"}}, {"timestamp": "2025-06-12T23:33:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050060"}}], "recentLogs": [{"message": "測試條碼-1278b111", "timestamp": "2025-06-13T16:22:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-dcbc5ed5", "timestamp": "2025-06-13T15:59:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-30aac9a2", "timestamp": "2025-06-13T16:14:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-770d1a01", "timestamp": "2025-06-13T16:14:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-771378a9", "timestamp": "2025-06-13T16:01:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-77b6825b", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-44ec3ba0", "timestamp": "2025-06-13T16:58:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}]}, "S720050061": {"deviceId": "S720050061", "lineName": "B1-01", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 321, "forwardedCount": 119, "unforwardedCount": 202, "lastLog": "測試產品條碼-af9c55f8", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-02-13T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO335771", "targetQuantity": 382, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-06T16:59:31.499522", "status": "active", "mesForwardedCount": 58, "completionRate": 77.4}, {"workOrderNumber": "WO969074", "targetQuantity": 199, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-01T16:59:31.499522", "status": "completed", "mesForwardedCount": 59, "completionRate": 71.2}, {"workOrderNumber": "WO252829", "targetQuantity": 217, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-28T16:59:31.499522", "status": "pending", "mesForwardedCount": 91, "completionRate": 0.5}], "currentWorkOrder": 2, "currentWorkOrderProgress": 93, "displayWorkOrder": {"workOrderNumber": "WO252829", "targetQuantity": 217, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-28T16:59:31.499522", "status": "pending", "mesForwardedCount": 91, "completionRate": 0.5}, "mesErrors": [{"timestamp": "2025-06-13T09:27:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050061"}}, {"timestamp": "2025-06-13T07:49:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050061"}}], "recentLogs": [{"message": "測試條碼-aace3b10", "timestamp": "2025-06-13T16:37:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-efb5aa3d", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-c5277395", "timestamp": "2025-06-13T16:42:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-1493ed3f", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-1b65e468", "timestamp": "2025-06-13T16:58:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-e8ff2d66", "timestamp": "2025-06-13T16:36:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-d4b2058c", "timestamp": "2025-06-13T16:43:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-769503f6", "timestamp": "2025-06-13T16:36:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}]}, "S720050062": {"deviceId": "S720050062", "lineName": "A2-01", "sectionName": "ASSEMBLY", "groupName": "TEST", "stationName": "STATION_03", "isActive": true, "isMonitoring": true, "productCount": 86, "forwardedCount": 65, "unforwardedCount": 21, "lastLog": "測試產品條碼-f2298d5f", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-10-17T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO558531", "targetQuantity": 218, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-02T16:59:31.499522", "status": "pending", "mesForwardedCount": 57, "completionRate": 45.3}, {"workOrderNumber": "WO908910", "targetQuantity": 150, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-09T16:59:31.499522", "status": "active", "mesForwardedCount": 55, "completionRate": 66.2}, {"workOrderNumber": "WO194764", "targetQuantity": 126, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-22T16:59:31.499522", "status": "active", "mesForwardedCount": 13, "completionRate": 0.3}], "currentWorkOrder": 1, "currentWorkOrderProgress": 40, "displayWorkOrder": {"workOrderNumber": "WO908910", "targetQuantity": 150, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-09T16:59:31.499522", "status": "active", "mesForwardedCount": 55, "completionRate": 66.2}, "mesErrors": [{"timestamp": "2025-06-12T19:25:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050062"}}, {"timestamp": "2025-06-13T02:41:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050062"}}, {"timestamp": "2025-06-13T02:49:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050062"}}, {"timestamp": "2025-06-12T19:01:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050062"}}], "recentLogs": [{"message": "測試條碼-bf991b82", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-f3a62b62", "timestamp": "2025-06-13T16:18:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-8fee01e4", "timestamp": "2025-06-13T16:00:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-f8927ea1", "timestamp": "2025-06-13T16:08:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-99bc6d0e", "timestamp": "2025-06-13T16:12:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-d75d1d3e", "timestamp": "2025-06-13T16:58:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-906f6a45", "timestamp": "2025-06-13T16:13:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-5df6ff95", "timestamp": "2025-06-13T16:01:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-202c38ea", "timestamp": "2025-06-13T16:51:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-0d8f6b80", "timestamp": "2025-06-13T16:33:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-1ac7874e", "timestamp": "2025-06-13T16:33:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-40dfefdb", "timestamp": "2025-06-13T16:03:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-53bd95ec", "timestamp": "2025-06-13T16:14:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-5ddb74df", "timestamp": "2025-06-13T16:53:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-68b44b4a", "timestamp": "2025-06-13T16:43:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-79813125", "timestamp": "2025-06-13T16:37:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}]}, "S720050063": {"deviceId": "S720050063", "lineName": "B2-01", "sectionName": "TESTING", "groupName": "INJECTION", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 555, "forwardedCount": 508, "unforwardedCount": 47, "lastLog": "測試產品條碼-c57b3c39", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-09-11T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO188351", "targetQuantity": 244, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-25T16:59:31.499522", "status": "active", "mesForwardedCount": 46, "completionRate": 29.8}, {"workOrderNumber": "WO467162", "targetQuantity": 324, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-13T16:59:31.499522", "status": "pending", "mesForwardedCount": 89, "completionRate": 76.7}, {"workOrderNumber": "WO131258", "targetQuantity": 390, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-31T16:59:31.499522", "status": "pending", "mesForwardedCount": 81, "completionRate": 35.7}], "currentWorkOrder": 2, "currentWorkOrderProgress": 70, "displayWorkOrder": {"workOrderNumber": "WO131258", "targetQuantity": 390, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-31T16:59:31.499522", "status": "pending", "mesForwardedCount": 81, "completionRate": 35.7}, "mesErrors": [{"timestamp": "2025-06-13T10:13:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050063"}}, {"timestamp": "2025-06-12T21:52:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050063"}}, {"timestamp": "2025-06-12T19:25:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050063"}}, {"timestamp": "2025-06-13T06:22:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050063"}}], "recentLogs": [{"message": "測試條碼-a1926e64", "timestamp": "2025-06-13T16:42:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-f1e12e3a", "timestamp": "2025-06-13T16:11:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-025adb09", "timestamp": "2025-06-13T16:58:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-79f9c6c0", "timestamp": "2025-06-13T16:49:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-013d62bb", "timestamp": "2025-06-13T16:46:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-98d31fbf", "timestamp": "2025-06-13T16:22:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-3b09f8f1", "timestamp": "2025-06-13T16:19:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-43eacad9", "timestamp": "2025-06-13T16:50:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-3948699f", "timestamp": "2025-06-13T16:49:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-8c969d65", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-066729b8", "timestamp": "2025-06-13T16:14:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-76a26e39", "timestamp": "2025-06-13T16:38:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-ccabb0eb", "timestamp": "2025-06-13T16:08:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-244204c1", "timestamp": "2025-06-13T16:19:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-d8fa8192", "timestamp": "2025-06-13T16:25:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}]}, "S720050064": {"deviceId": "S720050064", "lineName": "B2-01", "sectionName": "ASSEMBLY", "groupName": "TEST", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 289, "forwardedCount": 8, "unforwardedCount": 281, "lastLog": "測試產品條碼-546c544d", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-02-05T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO152921", "targetQuantity": 148, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-09T16:59:31.499522", "status": "pending", "mesForwardedCount": 35, "completionRate": 49.2}, {"workOrderNumber": "WO249010", "targetQuantity": 185, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-10T16:59:31.499522", "status": "completed", "mesForwardedCount": 61, "completionRate": 78.5}, {"workOrderNumber": "WO985529", "targetQuantity": 343, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-05T16:59:31.499522", "status": "pending", "mesForwardedCount": 28, "completionRate": 72.3}], "currentWorkOrder": 0, "currentWorkOrderProgress": 67, "displayWorkOrder": {"workOrderNumber": "WO152921", "targetQuantity": 148, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-09T16:59:31.499522", "status": "pending", "mesForwardedCount": 35, "completionRate": 49.2}, "mesErrors": [{"timestamp": "2025-06-12T19:55:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050064"}}, {"timestamp": "2025-06-13T16:04:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050064"}}, {"timestamp": "2025-06-13T02:12:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050064"}}, {"timestamp": "2025-06-12T22:00:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050064"}}, {"timestamp": "2025-06-12T23:57:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050064"}}], "recentLogs": [{"message": "測試條碼-33cae038", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-eb33cf9e", "timestamp": "2025-06-13T16:57:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-f5724484", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-5432735b", "timestamp": "2025-06-13T16:00:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-c089750a", "timestamp": "2025-06-13T16:28:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-4a1e0924", "timestamp": "2025-06-13T16:02:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-1e2af666", "timestamp": "2025-06-13T16:06:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-7928a984", "timestamp": "2025-06-13T16:07:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-829684c1", "timestamp": "2025-06-13T16:42:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-3183fa62", "timestamp": "2025-06-13T16:05:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-24a246bb", "timestamp": "2025-06-13T16:10:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-c81459bd", "timestamp": "2025-06-13T16:54:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-12205f03", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-dc32c9ab", "timestamp": "2025-06-13T16:44:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}]}, "S720050065": {"deviceId": "S720050065", "lineName": "A1-01", "sectionName": "INJECTION", "groupName": "TEST", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 37, "forwardedCount": 9, "unforwardedCount": 28, "lastLog": "測試產品條碼-b3fb1aa7", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-01-21T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO920343", "targetQuantity": 441, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-10T16:59:31.499522", "status": "completed", "mesForwardedCount": 71, "completionRate": 34.3}, {"workOrderNumber": "WO204785", "targetQuantity": 322, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-18T16:59:31.499522", "status": "active", "mesForwardedCount": 13, "completionRate": 61.5}, {"workOrderNumber": "WO998209", "targetQuantity": 66, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-12T16:59:31.499522", "status": "completed", "mesForwardedCount": 39, "completionRate": 35.1}], "currentWorkOrder": 2, "currentWorkOrderProgress": 43, "displayWorkOrder": {"workOrderNumber": "WO998209", "targetQuantity": 66, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-12T16:59:31.499522", "status": "completed", "mesForwardedCount": 39, "completionRate": 35.1}, "mesErrors": [{"timestamp": "2025-06-13T07:01:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050065"}}, {"timestamp": "2025-06-13T05:36:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050065"}}, {"timestamp": "2025-06-13T07:30:31.499522", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050065"}}], "recentLogs": [{"message": "測試條碼-57cea666", "timestamp": "2025-06-13T16:56:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-43aa1c33", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-9696b56b", "timestamp": "2025-06-13T16:23:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-64fe16bd", "timestamp": "2025-06-13T16:27:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-ba4f850c", "timestamp": "2025-06-13T16:53:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-bb8a3ee4", "timestamp": "2025-06-13T16:21:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}]}, "S720050066": {"deviceId": "S720050066", "lineName": "B1-01", "sectionName": "PACKAGING", "groupName": "INJECTION", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 493, "forwardedCount": 4, "unforwardedCount": 489, "lastLog": "測試產品條碼-41d22745", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-09-08T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO420667", "targetQuantity": 495, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-06T16:59:31.499522", "status": "pending", "mesForwardedCount": 6, "completionRate": 53.6}, {"workOrderNumber": "WO228528", "targetQuantity": 263, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-10T16:59:31.499522", "status": "completed", "mesForwardedCount": 91, "completionRate": 52.5}, {"workOrderNumber": "WO659697", "targetQuantity": 180, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-21T16:59:31.499522", "status": "pending", "mesForwardedCount": 90, "completionRate": 7.0}], "currentWorkOrder": 2, "currentWorkOrderProgress": 54, "displayWorkOrder": {"workOrderNumber": "WO659697", "targetQuantity": 180, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-21T16:59:31.499522", "status": "pending", "mesForwardedCount": 90, "completionRate": 7.0}, "mesErrors": [{"timestamp": "2025-06-12T16:59:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050066"}}, {"timestamp": "2025-06-13T00:59:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050066"}}, {"timestamp": "2025-06-13T13:11:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050066"}}, {"timestamp": "2025-06-12T22:00:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050066"}}], "recentLogs": [{"message": "測試條碼-4e48dea1", "timestamp": "2025-06-13T16:48:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-3feca214", "timestamp": "2025-06-13T16:18:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-c4ec7b51", "timestamp": "2025-06-13T16:54:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-72a63c43", "timestamp": "2025-06-13T16:54:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-991fed99", "timestamp": "2025-06-13T16:51:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-6dc893e7", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-41df5570", "timestamp": "2025-06-13T16:45:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-dc28f8af", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-577eee36", "timestamp": "2025-06-13T16:16:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f9c68493", "timestamp": "2025-06-13T16:39:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-b1b9bd94", "timestamp": "2025-06-13T16:01:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-29a301fe", "timestamp": "2025-06-13T16:00:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-1a895480", "timestamp": "2025-06-13T16:32:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-10aa88c6", "timestamp": "2025-06-13T16:15:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-7a0071af", "timestamp": "2025-06-13T16:40:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-6cbbd73d", "timestamp": "2025-06-13T16:47:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-dfbe5a71", "timestamp": "2025-06-13T16:40:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-a1332a1a", "timestamp": "2025-06-13T16:32:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}]}, "S720050067": {"deviceId": "S720050067", "lineName": "A2-02", "sectionName": "ASSEMBLY", "groupName": "INJECTION", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 111, "forwardedCount": 107, "unforwardedCount": 4, "lastLog": "測試產品條碼-2544bb5f", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2024-09-07T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO966116", "targetQuantity": 202, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-25T16:59:31.499522", "status": "active", "mesForwardedCount": 37, "completionRate": 88.4}, {"workOrderNumber": "WO867689", "targetQuantity": 437, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-03T16:59:31.499522", "status": "pending", "mesForwardedCount": 95, "completionRate": 79.5}, {"workOrderNumber": "WO978847", "targetQuantity": 315, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-01T16:59:31.499522", "status": "active", "mesForwardedCount": 32, "completionRate": 30.8}], "currentWorkOrder": 2, "currentWorkOrderProgress": 13, "displayWorkOrder": {"workOrderNumber": "WO978847", "targetQuantity": 315, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-01T16:59:31.499522", "status": "active", "mesForwardedCount": 32, "completionRate": 30.8}, "mesErrors": [{"timestamp": "2025-06-12T21:15:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050067"}}, {"timestamp": "2025-06-13T01:51:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050067"}}], "recentLogs": [{"message": "測試條碼-f24ba3eb", "timestamp": "2025-06-13T16:04:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-1b1d4095", "timestamp": "2025-06-13T16:12:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-4842e4eb", "timestamp": "2025-06-13T16:05:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-08b950a5", "timestamp": "2025-06-13T16:50:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-c836bd28", "timestamp": "2025-06-13T16:07:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-2986c00f", "timestamp": "2025-06-13T16:34:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-eeb73dc8", "timestamp": "2025-06-13T16:01:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}]}, "S720050068": {"deviceId": "S720050068", "lineName": "B2-02", "sectionName": "PACKAGING", "groupName": "TEST", "stationName": "STATION_03", "isActive": true, "isMonitoring": true, "productCount": 388, "forwardedCount": 218, "unforwardedCount": 170, "lastLog": "測試產品條碼-6a2f5c63", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-05-25T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO450678", "targetQuantity": 388, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-04T16:59:31.499522", "status": "completed", "mesForwardedCount": 12, "completionRate": 38.8}, {"workOrderNumber": "WO372001", "targetQuantity": 175, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-23T16:59:31.499522", "status": "active", "mesForwardedCount": 42, "completionRate": 28.1}, {"workOrderNumber": "WO672716", "targetQuantity": 488, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-25T16:59:31.499522", "status": "pending", "mesForwardedCount": 1, "completionRate": 28.3}], "currentWorkOrder": 1, "currentWorkOrderProgress": 17, "displayWorkOrder": {"workOrderNumber": "WO372001", "targetQuantity": 175, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-23T16:59:31.499522", "status": "active", "mesForwardedCount": 42, "completionRate": 28.1}, "mesErrors": [{"timestamp": "2025-06-12T21:28:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050068"}}, {"timestamp": "2025-06-13T00:56:31.499522", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050068"}}, {"timestamp": "2025-06-12T18:09:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050068"}}, {"timestamp": "2025-06-13T01:57:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050068"}}, {"timestamp": "2025-06-13T12:35:31.499522", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050068"}}], "recentLogs": [{"message": "測試條碼-72d32fac", "timestamp": "2025-06-13T16:44:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-6010990d", "timestamp": "2025-06-13T16:28:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-63275482", "timestamp": "2025-06-13T16:29:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-a61f3455", "timestamp": "2025-06-13T16:40:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-183842a9", "timestamp": "2025-06-13T16:47:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-8d02bcd8", "timestamp": "2025-06-13T16:00:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-d307aee4", "timestamp": "2025-06-13T16:25:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-9d930227", "timestamp": "2025-06-13T16:11:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-4437a8d3", "timestamp": "2025-06-13T16:43:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-a316f301", "timestamp": "2025-06-13T16:21:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-a8f054f9", "timestamp": "2025-06-13T16:16:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1a0b86d3", "timestamp": "2025-06-13T16:29:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-cb06a5e4", "timestamp": "2025-06-13T16:22:31.499522", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}]}, "S720050069": {"deviceId": "S720050069", "lineName": "A2-01", "sectionName": "INJECTION", "groupName": "TEST", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 199, "forwardedCount": 94, "unforwardedCount": 105, "lastLog": "測試產品條碼-340b01e7", "lastUpdateTime": "2025-06-13T16:59:31.499522", "createdAt": "2025-01-14T16:59:31.499522", "workOrders": [{"workOrderNumber": "WO303724", "targetQuantity": 308, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-11T16:59:31.499522", "status": "completed", "mesForwardedCount": 50, "completionRate": 18.3}, {"workOrderNumber": "WO872608", "targetQuantity": 60, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-03T16:59:31.499522", "status": "completed", "mesForwardedCount": 46, "completionRate": 60.4}, {"workOrderNumber": "WO436645", "targetQuantity": 420, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-18T16:59:31.499522", "status": "completed", "mesForwardedCount": 26, "completionRate": 59.9}], "currentWorkOrder": 1, "currentWorkOrderProgress": 24, "displayWorkOrder": {"workOrderNumber": "WO872608", "targetQuantity": 60, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-03T16:59:31.499522", "status": "completed", "mesForwardedCount": 46, "completionRate": 60.4}, "mesErrors": [{"timestamp": "2025-06-13T06:22:31.499522", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050069"}}], "recentLogs": [{"message": "測試條碼-2c2f486e", "timestamp": "2025-06-13T16:19:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-7267fbd5", "timestamp": "2025-06-13T16:48:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-81a986a2", "timestamp": "2025-06-13T16:48:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-2bd3f3bc", "timestamp": "2025-06-13T16:24:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a969e476", "timestamp": "2025-06-13T16:46:31.499522", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-a9d13891", "timestamp": "2025-06-13T16:32:31.499522", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-a2c207d2", "timestamp": "2025-06-13T16:22:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-08759400", "timestamp": "2025-06-13T16:25:31.499522", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-ccfe618b", "timestamp": "2025-06-13T16:40:31.506730", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-334d9e20", "timestamp": "2025-06-13T16:17:31.506730", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-51bafc65", "timestamp": "2025-06-13T16:30:31.506730", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-86c439f9", "timestamp": "2025-06-13T16:54:31.506730", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-cd0d071d", "timestamp": "2025-06-13T16:43:31.506730", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-6654fcde", "timestamp": "2025-06-13T16:24:31.506730", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-e4148994", "timestamp": "2025-06-13T16:03:31.506730", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-b465544e", "timestamp": "2025-06-13T16:07:31.506730", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-768f0954", "timestamp": "2025-06-13T16:45:31.506730", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-ec42bcbf", "timestamp": "2025-06-13T16:28:31.506730", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}]}, "S720050070": {"deviceId": "S720050070", "lineName": "B2-01", "sectionName": "PACKAGING", "groupName": "INJECTION", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 417, "forwardedCount": 287, "unforwardedCount": 130, "lastLog": "測試產品條碼-73106011", "lastUpdateTime": "2025-06-13T16:59:31.506730", "createdAt": "2024-10-29T16:59:31.506730", "workOrders": [{"workOrderNumber": "WO412919", "targetQuantity": 446, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-31T16:59:31.506730", "status": "completed", "mesForwardedCount": 100, "completionRate": 13.8}, {"workOrderNumber": "WO170317", "targetQuantity": 126, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-23T16:59:31.506730", "status": "active", "mesForwardedCount": 20, "completionRate": 31.9}, {"workOrderNumber": "WO547946", "targetQuantity": 251, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-12T16:59:31.506730", "status": "active", "mesForwardedCount": 9, "completionRate": 10.7}], "currentWorkOrder": 0, "currentWorkOrderProgress": 51, "displayWorkOrder": {"workOrderNumber": "WO412919", "targetQuantity": 446, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-31T16:59:31.506730", "status": "completed", "mesForwardedCount": 100, "completionRate": 13.8}, "mesErrors": [{"timestamp": "2025-06-13T07:39:31.506730", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050070"}}, {"timestamp": "2025-06-13T14:51:31.506730", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050070"}}, {"timestamp": "2025-06-12T18:21:31.506730", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050070"}}, {"timestamp": "2025-06-13T01:43:31.506730", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050070"}}], "recentLogs": [{"message": "測試條碼-f643f7c9", "timestamp": "2025-06-13T16:17:31.506730", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a22b50f5", "timestamp": "2025-06-13T16:35:31.506730", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-8fce107b", "timestamp": "2025-06-13T16:30:31.506730", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-8bd13501", "timestamp": "2025-06-13T16:05:31.506730", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-8569ee19", "timestamp": "2025-06-13T16:56:31.506730", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-a8b34f74", "timestamp": "2025-06-13T15:59:31.506730", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-3793851a", "timestamp": "2025-06-13T16:01:31.506730", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f61310bb", "timestamp": "2025-06-13T16:41:31.506730", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-381780f8", "timestamp": "2025-06-13T16:52:31.506730", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-4ba34365", "timestamp": "2025-06-13T16:48:31.506730", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-6860d3f3", "timestamp": "2025-06-13T16:43:31.506730", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-64938ed4", "timestamp": "2025-06-13T16:30:31.506730", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-89310d83", "timestamp": "2025-06-13T16:24:31.506730", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-cd1aa1af", "timestamp": "2025-06-13T16:38:31.506730", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-3bac2df9", "timestamp": "2025-06-13T16:47:31.506730", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-6af765f5", "timestamp": "2025-06-13T16:25:31.506730", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-889913c8", "timestamp": "2025-06-13T16:10:31.506730", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-071b758f", "timestamp": "2025-06-13T16:04:31.506730", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-fd2d13b2", "timestamp": "2025-06-13T16:46:31.506730", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}]}}