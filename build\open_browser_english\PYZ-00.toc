('E:\\vscode\\tomesmo\\build\\open_browser_english\\PYZ-00.pyz',
 [('_compat_pickle', 'E:\\python\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'E:\\python\\lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'E:\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('_strptime', 'E:\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'E:\\python\\lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'E:\\python\\lib\\argparse.py', 'PYMODULE'),
  ('base64', 'E:\\python\\lib\\base64.py', 'PYMODULE'),
  ('bisect', 'E:\\python\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'E:\\python\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'E:\\python\\lib\\calendar.py', 'PYMODULE'),
  ('contextlib', 'E:\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('copy', 'E:\\python\\lib\\copy.py', 'PYMODULE'),
  ('datetime', 'E:\\python\\lib\\datetime.py', 'PYMODULE'),
  ('email', 'E:\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'E:\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'E:\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'E:\\python\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'E:\\python\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'E:\\python\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'E:\\python\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'E:\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'E:\\python\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'E:\\python\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'E:\\python\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'E:\\python\\lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'E:\\python\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'E:\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'E:\\python\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'E:\\python\\lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'E:\\python\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'E:\\python\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'E:\\python\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'E:\\python\\lib\\email\\utils.py', 'PYMODULE'),
  ('ftplib', 'E:\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'E:\\python\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'E:\\python\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'E:\\python\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'E:\\python\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'E:\\python\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'E:\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('http', 'E:\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'E:\\python\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'E:\\python\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('importlib', 'E:\\python\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'E:\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'E:\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'E:\\python\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'E:\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.util', 'E:\\python\\lib\\importlib\\util.py', 'PYMODULE'),
  ('logging', 'E:\\python\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'E:\\python\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'E:\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('netrc', 'E:\\python\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'E:\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('optparse', 'E:\\python\\lib\\optparse.py', 'PYMODULE'),
  ('pickle', 'E:\\python\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'E:\\python\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'E:\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('quopri', 'E:\\python\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'E:\\python\\lib\\random.py', 'PYMODULE'),
  ('selectors', 'E:\\python\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'E:\\python\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'E:\\python\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'E:\\python\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'E:\\python\\lib\\socket.py', 'PYMODULE'),
  ('ssl', 'E:\\python\\lib\\ssl.py', 'PYMODULE'),
  ('string', 'E:\\python\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'E:\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'E:\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'E:\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'E:\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'E:\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'E:\\python\\lib\\threading.py', 'PYMODULE'),
  ('tracemalloc', 'E:\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'E:\\python\\lib\\typing.py', 'PYMODULE'),
  ('uu', 'E:\\python\\lib\\uu.py', 'PYMODULE'),
  ('webbrowser', 'E:\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('zipfile', 'E:\\python\\lib\\zipfile.py', 'PYMODULE')])
