('E:\\vscode\\tomesmo\\build\\open_browser_english\\PYZ-00.pyz',
 [('_compat_pickle', 'e:\\python\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'e:\\python\\lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'e:\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('_strptime', 'e:\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'e:\\python\\lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'e:\\python\\lib\\argparse.py', 'PYMODULE'),
  ('base64', 'e:\\python\\lib\\base64.py', 'PYMODULE'),
  ('bisect', 'e:\\python\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'e:\\python\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'e:\\python\\lib\\calendar.py', 'PYMODULE'),
  ('contextlib', 'e:\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('copy', 'e:\\python\\lib\\copy.py', 'PYMODULE'),
  ('datetime', 'e:\\python\\lib\\datetime.py', 'PYMODULE'),
  ('email', 'e:\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'e:\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'e:\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'e:\\python\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'e:\\python\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'e:\\python\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'e:\\python\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'e:\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'e:\\python\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'e:\\python\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'e:\\python\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'e:\\python\\lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'e:\\python\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'e:\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'e:\\python\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'e:\\python\\lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'e:\\python\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'e:\\python\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'e:\\python\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'e:\\python\\lib\\email\\utils.py', 'PYMODULE'),
  ('ftplib', 'e:\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'e:\\python\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'e:\\python\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'e:\\python\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'e:\\python\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'e:\\python\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'e:\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('http', 'e:\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'e:\\python\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'e:\\python\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('importlib', 'e:\\python\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'e:\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'e:\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'e:\\python\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'e:\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.util', 'e:\\python\\lib\\importlib\\util.py', 'PYMODULE'),
  ('logging', 'e:\\python\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'e:\\python\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'e:\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('netrc', 'e:\\python\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'e:\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('optparse', 'e:\\python\\lib\\optparse.py', 'PYMODULE'),
  ('pickle', 'e:\\python\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'e:\\python\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'e:\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('quopri', 'e:\\python\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'e:\\python\\lib\\random.py', 'PYMODULE'),
  ('selectors', 'e:\\python\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'e:\\python\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'e:\\python\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'e:\\python\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'e:\\python\\lib\\socket.py', 'PYMODULE'),
  ('ssl', 'e:\\python\\lib\\ssl.py', 'PYMODULE'),
  ('string', 'e:\\python\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'e:\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'e:\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'e:\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'e:\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'e:\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'e:\\python\\lib\\threading.py', 'PYMODULE'),
  ('tracemalloc', 'e:\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'e:\\python\\lib\\typing.py', 'PYMODULE'),
  ('uu', 'e:\\python\\lib\\uu.py', 'PYMODULE'),
  ('webbrowser', 'e:\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('zipfile', 'e:\\python\\lib\\zipfile.py', 'PYMODULE')])
