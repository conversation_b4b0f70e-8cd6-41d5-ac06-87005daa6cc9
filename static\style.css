* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 98%;
    margin: 0 auto;
    padding: 6px;
}

header {
    margin-bottom: 12px;
    padding: 8px 12px;
    background: rgb(0, 135, 220);
    color: white;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    align-items: center;
    position: relative;
}

.header-left {
    display: flex;
    align-items: flex-end;
    gap: 8px;
    position: absolute;
    left: 0;
}

.header-logo {
    height: 32px;
    width: auto;
    object-fit: contain;
}

.header-center {
    flex: 1;
    text-align: center;
}

header h1 {
    font-size: 1.6em;
    margin: 0;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.department-info {
    font-size: 0.9em;
    font-weight: 500;
    color: white;
    opacity: 0.9;
    line-height: 1;
    margin-bottom: 2px;
}

/* 移除标题装饰线以节省空间 */

header p {
    font-size: 1.1em;
    opacity: 0.9;
}

.control-panel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.monitoring-status {
    display: flex;
    flex-direction: row;
    gap: 20px;
    align-items: center;
}

.kafka-status,
.rabbitmq-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-buttons {
    display: flex;
    gap: 6px;
}

#kafka-status-text,
#rabbitmq-status-text {
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: #f8f9fa;
    font-size: 0.9em;
    white-space: nowrap;
}

.btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #138496;
}

.btn-custom {
    background-color: rgb(0, 135, 220);
    color: white;
}

.btn-custom:hover {
    background-color: rgb(0, 115, 200);
}

/* 监控按钮样式 */
.btn-monitor {
    background-color: #ffc107;
    color: #212529;
    border: 1px solid #ffc107;
}

.btn-monitor:hover {
    background-color: #e0a800;
    border-color: #d39e00;
}

.btn-monitor:disabled {
    background-color: #6c757d;
    color: #fff;
    border-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.65;
}

.btn-monitor:disabled:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    transform: none;
    box-shadow: none;
}



.devices-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 12px;
}

.device-card {
    background: white;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 3px solid #007bff;
    position: relative;
    overflow: hidden;
}

.device-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #007bff, #0056b3);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.device-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.device-card:hover::before {
    opacity: 1;
}

.device-card.inactive {
    border-left-color: #dc3545;
    opacity: 0.7;
    background: linear-gradient(135deg, #ffffff, #fdf2f2);
}

.device-card.mes-active {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #ffffff, #f8fff9);
}

.device-card.mes-active::before {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.device-card.no-work-order {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #ffffff, #fffbf0);
}

.device-card.no-work-order::before {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.device-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.device-id {
    font-size: 1.2em;
    font-weight: bold;
    color: #007bff;
}

.device-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

/* 緊湊設備信息佈局 */
.device-info-compact {
    margin-bottom: 6px;
    background: rgba(248, 249, 250, 0.5);
    border-radius: 6px;
    padding: 6px 8px;
}

/* 滾動橫幅樣式 */
.info-marquee-container {
    width: 100%;
    height: 24px;
    overflow: hidden;
    background: linear-gradient(90deg, #f8f9fa, #e9ecef);
    border-radius: 4px;
    margin-bottom: 6px;
    border: 1px solid #dee2e6;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.info-marquee-container:hover {
    background: linear-gradient(90deg, #e9ecef, #dee2e6);
    border-color: #adb5bd;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 添加漸變邊緣效果 */
.info-marquee-container::before,
.info-marquee-container::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 10px;
    pointer-events: none;
    z-index: 1;
}

.info-marquee-container::before {
    left: 0;
    background: linear-gradient(90deg, rgba(248,249,250,0.8), transparent);
}

.info-marquee-container::after {
    right: 0;
    background: linear-gradient(270deg, rgba(248,249,250,0.8), transparent);
}

.info-marquee {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.marquee-content {
    display: inline-block;
    white-space: nowrap;
    font-size: 0.8em;
    color: #495057;
    font-weight: 500;
    line-height: 22px;
    padding: 1px 8px;
    animation: marquee 15s linear infinite;
}

@keyframes marquee {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* 當內容較短時暫停動畫 */
.info-marquee-container:hover .marquee-content {
    animation-play-state: paused;
}



/* 工單機種固定顯示 */
.work-order-fixed {
    border-top: 1px solid #e9ecef;
    padding-top: 4px;
}

.work-order-line,
.model-line {
    display: flex;
    align-items: center;
    margin-bottom: 2px;
    font-size: 0.85em;
}

.model-line {
    margin-bottom: 0;
}

.info-label {
    font-weight: 500;
    color: #666;
    font-size: 0.8em;
    min-width: 32px;
    margin-right: 4px;
}

.info-value {
    color: #333;
    font-weight: 600;
    flex: 1;
}

.work-order-highlight {
    color: #007bff;
    font-weight: 700;
}

.model-highlight {
    color: #007bff;
    font-weight: 700;
}

/* 緊湊工單進度樣式 */
.work-order-progress-compact {
    margin-bottom: 6px;
    padding: 6px 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    min-height: 32px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.progress-bar-compact {
    width: 100%;
    height: 12px;
    background-color: #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 4px;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.progress-text-compact {
    font-size: 0.75em;
    color: #6c757d;
    font-weight: 500;
    text-align: center;
}

.progress-empty {
    text-align: center;
    color: #adb5bd;
    font-size: 0.75em;
    font-style: italic;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 24px;
}

/* 最近使用工單樣式 */
.recent-work-orders {
    margin-bottom: 6px;
    padding: 4px 6px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #6c757d;
}

.recent-work-orders-header {
    font-size: 0.7em;
    color: #6c757d;
    font-weight: 600;
    margin-bottom: 3px;
    text-align: left;
}

.recent-work-orders-list {
    font-size: 0.65em;
}

.recent-work-order-item {
    padding: 2px 0;
    border-bottom: 1px solid #e9ecef;
}

.recent-work-order-item:last-child {
    border-bottom: none;
}

.recent-wo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1px;
}

.recent-wo-number {
    color: #495057;
    font-weight: 500;
    text-align: left;
}

.recent-wo-time {
    color: #6c757d;
    font-size: 0.9em;
    text-align: right;
}

.recent-wo-progress {
    color: #6c757d;
    font-size: 0.95em;
    text-align: left;
}

.no-recent-work-orders,
.recent-work-orders-error {
    text-align: center;
    color: #adb5bd;
    font-style: italic;
    padding: 2px 0;
}

/* 保留原有樣式作為備用 */
.device-info {
    margin-bottom: 15px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 5px 0;
    border-bottom: 1px solid #f0f0f0;
}

.device-actions {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    margin-top: 6px;
}

.device-actions .btn {
    flex: 1;
    padding: 4px 6px;
    font-size: 10px;
    min-width: 60px;
}

.device-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 6px;
    margin-bottom: 6px;
    padding: 6px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.6em;
    font-weight: 700;
    color: #007bff;
    transition: all 0.3s ease;
    position: relative;
}

.stat-value:hover {
    transform: scale(1.1);
    color: #0056b3;
}

.stat-value.updated {
    animation: stat-update 0.6s ease;
}

@keyframes stat-update {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); color: #28a745; }
    100% { transform: scale(1); }
}

.stat-label {
    font-size: 0.75em;
    color: #666;
    margin-top: 2px;
    font-weight: 500;
    text-transform: uppercase;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1.2;
    letter-spacing: 0.5px;
}

.device-monitoring {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 8px 12px;
    background-color: #e9ecef;
    border-radius: 5px;
}

.monitoring-status {
    font-weight: 500;
}

.monitoring-status.active {
    color: #28a745;
}

.monitoring-status.inactive {
    color: #6c757d;
}

.period-stats {
    margin-top: 15px;
    padding: 8px 12px;
    background-color: #fff3cd;
    border-radius: 5px;
    border-left: 3px solid #ffc107;
}

.period-header {
    font-size: 0.85em;
    font-weight: 500;
    color: #856404;
    margin-bottom: 5px;
}

.period-info {
    font-size: 0.8em;
    color: #856404;
    margin-bottom: 5px;
    font-style: italic;
}

.period-data {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.period-item {
    font-size: 0.8em;
    color: #856404;
    font-weight: 500;
}

/* 工單信息樣式 */
.work-order-info {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}

.work-order-header {
    font-size: 0.85em;
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

.work-order-current {
    font-size: 0.8em;
}

.work-order-number {
    font-weight: 600;
    color: #007bff;
    margin-bottom: 5px;
}

.work-order-model {
    font-size: 0.75em;
    color: #6c757d;
    margin-bottom: 3px;
}

.work-order-progress {
    margin-bottom: 5px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 8px;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
    transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.progress-fill::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(90deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
    border-radius: 10px 10px 0 0;
}

.progress-fill.completed {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        opacity: 1;
        box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
    }
    50% {
        opacity: 0.8;
        box-shadow: 0 0 20px rgba(255, 193, 7, 0.8);
    }
}

.progress-text {
    font-size: 0.75em;
    color: #6c757d;
}

.work-order-desc {
    font-size: 0.75em;
    color: #6c757d;
    font-style: italic;
}

.work-order-empty {
    text-align: center;
    color: #6c757d;
}

.empty-text {
    font-size: 0.8em;
    margin-bottom: 3px;
}

.empty-hint {
    font-size: 0.7em;
    color: #adb5bd;
}

.work-order-error {
    color: #dc3545;
    font-size: 0.8em;
    text-align: center;
}

/* 工單管理模態框樣式 */
.modal-large {
    max-width: 800px;
    width: 90%;
}

.work-order-manager {
    padding: 10px 15px;
}

.add-work-order-section {
    margin-bottom: 12px;
    padding: 10px 12px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 6px;
    border: 1px solid #dee2e6;
    box-shadow: 0 1px 4px rgba(0,0,0,0.05);
}

.add-work-order-section h3 {
    margin-bottom: 8px;
    margin-top: 0;
    color: #495057;
    font-size: 1em;
}

.form-row {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.form-row .form-group {
    flex: 1;
}

.work-order-list-section h3 {
    margin-bottom: 6px;
    margin-top: 0;
    color: #495057;
    font-size: 1em;
}

.sort-hint {
    font-size: 0.8em;
    color: #6c757d;
    font-weight: normal;
}

.work-order-list {
    /* 移除滾動條，顯示所有工單 */
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 8px;
    background-color: #fafafa;
}

.work-order-item {
    padding: 6px 10px;
    margin-bottom: 4px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background-color: #fff;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    min-height: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.work-order-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-color: #007bff;
}

/* 拖拽時的樣式優化 */
.work-order-item:active {
    cursor: grabbing;
    transform: scale(1.02);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    z-index: 1000;
}

.work-order-item.active {
    border-color: #28a745;
    background-color: #f8fff9;
}

.work-order-item.completed {
    border-color: #6c757d;
    background-color: #f8f9fa;
    opacity: 0.8;
}

.work-order-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2px;
    position: relative;
}

.work-order-priority {
    font-size: 0.8em;
    font-weight: bold;
    color: #dc3545;
    margin-right: 10px;
    min-width: 40px;
}

.drag-handle {
    cursor: move;
    color: #6c757d;
    font-size: 1em;
    padding: 2px;
    user-select: none;
}

.drag-handle:hover {
    color: #495057;
}

.work-order-item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
}

.work-order-number {
    font-weight: 600;
    color: #007bff;
    flex: 1;
    font-size: 0.85em;
}

.work-order-status {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.75em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.status-pending {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-active {
    background: linear-gradient(135deg, #d4edda, #00b894);
    color: #155724;
    border: 1px solid #00b894;
    animation: pulse-active 2s infinite;
}

.status-completed {
    background: linear-gradient(135deg, #d1ecf1, #74b9ff);
    color: #0c5460;
    border: 1px solid #74b9ff;
}

@keyframes pulse-active {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 8px rgba(40, 167, 69, 0);
    }
}

.work-order-item-body {
    margin-bottom: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    font-size: 0.75em;
}

.work-order-target {
    color: #495057;
    font-weight: 500;
}

.work-order-model {
    color: #6c757d;
    font-weight: 500;
}

.work-order-forwarded {
    color: #28a745;
    font-weight: 500;
}

.work-order-completion {
    color: #007bff;
    font-weight: 500;
}

.work-order-description {
    color: #6c757d;
    font-style: italic;
    flex: 1;
    min-width: 100px;
}

.work-order-item-actions {
    text-align: right;
    margin-left: auto;
}

.work-order-empty-list {
    text-align: center;
    padding: 20px;
    color: #6c757d;
    font-size: 0.9em;
}

.btn-sm {
    padding: 2px 6px;
    font-size: 0.7em;
    border-radius: 2px;
}

.device-log {
    margin-top: 6px;
    margin-bottom: 6px;
    padding: 6px 8px;
    background-color: #f1f3f4;
    border-radius: 4px;
    min-height: 48px;
}

.log-header {
    font-size: 0.7em;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 3px;
}

.log-content {
    font-size: 0.85em;
    color: #333;
    font-family: 'Courier New', monospace;
    background-color: #fff;
    padding: 5px;
    border-radius: 3px;
    border: 1px solid #e9ecef;
    min-height: 32px;
    display: flex;
    align-items: center;
}

.log-time {
    font-size: 0.75em;
    color: #999;
    margin-top: 5px;
}



/* 模態框樣式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 8px;
    width: 95%;
    max-width: 500px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 96vh;
    overflow-y: auto;
}

/* 大型模態框樣式 - 用於工單管理 */
.modal-large {
    max-width: 900px !important;
    width: 98% !important;
    margin: 1% auto !important;
    max-height: 98vh !important;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

.modal-header h2 {
    margin: 0;
    color: #333;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

.form-group {
    margin-bottom: 12px;
    padding: 0 15px;
}

.form-group label {
    display: block;
    margin-bottom: 3px;
    font-weight: 500;
    color: #333;
    font-size: 0.9em;
}

.form-group input {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
}

.form-group input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group small {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
}

.form-actions {
    display: flex;
    gap: 8px;
    padding: 10px 15px;
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

.form-actions .btn {
    flex: 1;
}

/* 消息提示 */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.message.show {
    transform: translateX(0);
}

.message.success {
    background-color: #28a745;
}

.message.error {
    background-color: #dc3545;
}

.message.info {
    background-color: #17a2b8;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    header h1 {
        font-size: 2.2em;
    }

    .control-panel {
        flex-direction: column;
        gap: 15px;
    }

    .kafka-status {
        flex-direction: column;
        text-align: center;
    }

    .devices-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .device-card {
        padding: 15px;
    }

    .device-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .device-actions {
        flex-direction: column;
        gap: 8px;
    }

    .device-actions .btn {
        font-size: 0.9em;
        padding: 8px 12px;
    }

    .modal-content {
        width: 98%;
        margin: 2% auto;
    }

    .modal-large {
        width: 99% !important;
        margin: 1% auto !important;
        max-height: 98vh !important;
    }

    .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .work-order-list {
        /* 移動端也移除滾動條限制 */
        padding: 6px;
    }

    /* 移動端滾動橫幅優化 */
    .marquee-content {
        font-size: 0.75em;
        animation-duration: 20s;
        line-height: 20px;
    }

    .info-marquee-container {
        height: 22px;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.8em;
    }

    .device-stats {
        grid-template-columns: 1fr;
    }

    .stat-value {
        font-size: 1.5em;
    }

    .work-order-item {
        padding: 4px 8px;
        margin-bottom: 3px;
        min-height: 35px;
    }

    .work-order-item-header {
        flex-direction: row;
        align-items: center;
        gap: 4px;
        margin-bottom: 1px;
    }

    .work-order-item-body {
        gap: 6px;
        font-size: 0.7em;
    }

    .work-order-manager {
        padding: 10px;
    }

    .add-work-order-section {
        padding: 8px 10px;
        margin-bottom: 10px;
    }

    .modal-header {
        padding: 8px 12px;
    }

    .form-actions {
        padding: 8px 12px;
    }
}

/* MES錯誤信息彈窗樣式 */
.mes-errors-content {
    max-height: 70vh;
    overflow-y: auto;
}

.device-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.device-info h3 {
    margin: 0 0 10px 0;
    color: #007bff;
    font-size: 1.2em;
}

.device-info p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9em;
}

.mes-errors-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.mes-errors-header h3 {
    margin: 0;
    color: #495057;
}

.mes-errors-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    background-color: #fff;
}

.mes-error-item {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s;
}

.mes-error-item:last-child {
    border-bottom: none;
}

.mes-error-item:hover {
    background-color: #f8f9fa;
}

.mes-error-time {
    font-weight: bold;
    color: #dc3545;
    font-size: 0.9em;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.mes-error-time::before {
    content: "🕒";
    margin-right: 5px;
}

.mes-error-content {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    border-left: 3px solid #dc3545;
    font-family: 'Courier New', monospace;
    font-size: 0.85em;
    white-space: pre-wrap;
    word-break: break-all;
    color: #495057;
}

.mes-error-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75em;
    font-weight: bold;
    margin-left: 10px;
}

.mes-error-status.success {
    background-color: #d4edda;
    color: #155724;
}

.mes-error-status.error {
    background-color: #f8d7da;
    color: #721c24;
}

.mes-errors-empty {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-style: italic;
}

.mes-errors-empty::before {
    content: "📝";
    display: block;
    font-size: 3em;
    margin-bottom: 10px;
}

/* 页面底部信息样式 */
.footer-info {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    margin-top: 20px;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 0.85em;
    color: #6c757d;
    gap: 8px;
}

.auto-refresh-info {
    font-weight: 500;
    line-height: 1.2;
}

.version-separator {
    font-size: 0.7em;
    color: #adb5bd;
    font-weight: 300;
    line-height: 1.2;
    margin: 0 4px;
}

.version-info {
    font-weight: 400;
    font-style: italic;
    font-size: 0.7em;
    color: #adb5bd;
    line-height: 1.2;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .device-card {
        margin-bottom: 15px;
    }

    .device-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .control-panel {
        flex-direction: column;
        gap: 15px;
    }

    .monitoring-status {
        width: 100%;
    }

    .footer-info {
        flex-direction: column;
        gap: 4px;
        text-align: center;
        align-items: center;
    }

    .version-separator {
        display: none;
    }

    .version-info {
        margin-top: 4px;
    }

    .header-content {
        flex-direction: column;
        gap: 8px;
        text-align: center;
        position: static;
    }

    .header-left {
        position: static;
        justify-content: center;
        align-items: center;
        gap: 6px;
    }

    .header-center {
        order: -1;
    }

    .department-info {
        font-size: 0.75em;
        margin-bottom: 0;
    }
}

/* 測試模式樣式 */
.test-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    font-size: 14px;
}

.test-status {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 15px;
    border-radius: 4px;
    margin: 10px 0;
}

.test-status p {
    margin: 5px 0;
    font-size: 14px;
}

.test-status span {
    font-weight: bold;
    color: #0087dc;
}

#test-mode-btn {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

#test-mode-btn:hover {
    background-color: #e0a800;
    border-color: #d39e00;
}

/* 模擬數據生成樣式 */
.fake-data-info {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
    font-size: 13px;
}

.fake-data-info p {
    margin: 3px 0;
}

.fake-data-info span {
    font-weight: bold;
    color: #0087dc;
}

#fake-data-controls {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
    margin-top: 15px;
}

#start-fake-data-btn {
    background-color: #28a745;
    border-color: #28a745;
}

#start-fake-data-btn:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

#stop-fake-data-btn {
    background-color: #6c757d;
    border-color: #6c757d;
}

#stop-fake-data-btn:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

/* 導入配置樣式 */
.import-info {
    color: #856404;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 13px;
    margin-top: 8px;
}

.import-preview {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 12px;
    border-radius: 4px;
    margin-top: 15px;
    max-height: 200px;
    overflow-y: auto;
}

.import-preview h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
}

.import-preview-content {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    background: white;
    padding: 8px;
    border-radius: 3px;
    border: 1px solid #e9ecef;
}

#config-file-input {
    margin-bottom: 10px;
}

#export-config-btn, #import-config-btn {
    background-color: #0087dc;
    border-color: #0087dc;
}

#export-config-btn:hover, #import-config-btn:hover {
    background-color: #006bb3;
    border-color: #005a94;
}
