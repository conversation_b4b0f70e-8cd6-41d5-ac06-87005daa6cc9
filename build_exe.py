#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理MES上報系統打包腳本
用於將Flask應用打包成獨立的Windows可執行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
from datetime import datetime

def create_spec_file():
    """創建PyInstaller規格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
        ('devices.json', '.'),
        ('kafka_config.json', '.'),
        ('rabbitmq_config.json', '.'),
        ('config.py', '.'),
    ],
    hiddenimports=[
        'flask',
        'kafka',
        'pika',
        'json',
        'threading',
        'datetime',
        'requests',
        'uuid',
        'time',
        'os',
        'sys'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='MES_Upload_Manager',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('mes_system.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 已創建PyInstaller規格文件: mes_system.spec")

def build_executable():
    """使用PyInstaller構建可執行文件"""
    print("🚀 開始構建可執行文件...")

    # 清理之前的構建
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("🧹 清理舊的build目錄")

    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("🧹 清理舊的dist目錄")

    # 運行PyInstaller
    cmd = ['pyinstaller', '--clean', 'mes_system.spec']
    try:
        result = subprocess.run(cmd, shell=True)
        if result.returncode == 0:
            print("✅ 可執行文件構建成功！")
            return True
        else:
            print("❌ 構建失敗，返回碼:", result.returncode)
            return False
    except Exception as e:
        print(f"❌ 構建過程中出現異常: {e}")
        return False

def create_deployment_package():
    """創建部署包"""
    print("📦 創建部署包...")

    # 生成帶日期的部署目錄名稱
    today = datetime.now().strftime('%Y%m%d')
    deploy_dir = Path(f'MES_Upload_Manager_v2.0_{today}')
    if deploy_dir.exists():
        shutil.rmtree(deploy_dir)

    deploy_dir.mkdir()

    # 複製可執行文件
    exe_path = Path('dist/MES_Upload_Manager.exe')
    if exe_path.exists():
        shutil.copy2(exe_path, deploy_dir / 'MES_Upload_Manager.exe')
        print("✅ 已複製可執行文件")
    else:
        print("❌ 找不到可執行文件")
        return False
    
    # 創建啟動腳本
    start_script = '''@echo off
chcp 65001 > nul
echo Starting MES Upload Manager...
echo.
echo System will open in browser: http://localhost:5000
echo If browser doesn't open automatically, please visit the above address manually
echo.
echo Press Ctrl+C to stop the system
echo.
"MES_Upload_Manager.exe"
pause
'''

    with open(deploy_dir / 'Start_MES_Manager.bat', 'w', encoding='utf-8') as f:
        f.write(start_script)
    print("✅ 已創建啟動腳本")
    
    # 創建說明文件
    today_str = datetime.now().strftime('%Y-%m-%d')
    readme_content = f'''# MES Upload Manager v2.0 - Deployment Guide
Build Date: {today_str}

## System Requirements
- Windows 10 or higher
- No Python installation required

## Installation Steps
1. Copy the entire folder to target computer
2. Double-click "Start_MES_Manager.bat" to start the system
3. System will automatically open in browser

## Usage Instructions
1. **Access URL**: http://localhost:5000
2. **EAP Settings**: Configure EAP server connection
3. **SIE Settings**: Configure SIE server connection (supports multiple servers)
4. **Device Management**: Add and manage production devices
5. **Work Order Management**: Configure device work orders with upload types
6. **Monitoring**: Start EAP or SIE monitoring

## Key Features
- Support for multiple SIE servers
- Work order upload type selection (PQM only, MES only, Both)
- Real-time production data monitoring
- Automatic MES forwarding with work order management
- Recent work orders tracking
- Error logging and monitoring

## Notes
- Ensure target computer can access EAP and SIE servers
- Ensure MES server address is reachable
- Do not close command window while system is running
- To stop system, press Ctrl+C or close command window

## Troubleshooting
1. **Cannot start**: Check firewall settings
2. **Cannot connect EAP/SIE**: Check network connection and server addresses
3. **MES forwarding fails**: Check MES server configuration and network

## Technical Support
Contact system administrator for assistance
'''
    
    with open(deploy_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✅ 已創建說明文件")
    
    print(f"🎉 部署包創建完成: {deploy_dir.absolute()}")
    return True

def main():
    """主函數"""
    print("🏭 代理MES上報系統 - 打包工具")
    print("=" * 50)
    
    # 檢查必要文件
    required_files = ['app.py', 'templates', 'static']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺少必要文件: {file}")
            return False
    
    # 創建規格文件
    create_spec_file()
    
    # 構建可執行文件
    if not build_executable():
        return False
    
    # 創建部署包
    if not create_deployment_package():
        return False
    
    today = datetime.now().strftime('%Y%m%d')
    deploy_folder = f'MES_Upload_Manager_v2.0_{today}'
    print("\n🎉 打包完成！")
    print(f"📁 部署包位置: {deploy_folder}/")
    print(f"🚀 可以將整個 {deploy_folder} 文件夾複製到目標電腦使用")
    
    return True

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)
