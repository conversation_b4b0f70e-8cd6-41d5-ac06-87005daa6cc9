#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理MES上報系統打包腳本
用於將Flask應用打包成獨立的Windows可執行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def create_spec_file():
    """創建PyInstaller規格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
        ('devices.json', '.'),
        ('kafka_config.json', '.'),
    ],
    hiddenimports=[
        'flask',
        'kafka',
        'pika',
        'json',
        'threading',
        'datetime',
        'requests',
        'uuid',
        'time',
        'os',
        'sys'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='代理MES上報系統',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('mes_system.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 已創建PyInstaller規格文件: mes_system.spec")

def build_executable():
    """使用PyInstaller構建可執行文件"""
    print("🚀 開始構建可執行文件...")

    # 清理之前的構建
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("🧹 清理舊的build目錄")

    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("🧹 清理舊的dist目錄")

    # 運行PyInstaller
    cmd = ['pyinstaller', '--clean', 'mes_system.spec']
    try:
        result = subprocess.run(cmd, shell=True)
        if result.returncode == 0:
            print("✅ 可執行文件構建成功！")
            return True
        else:
            print("❌ 構建失敗，返回碼:", result.returncode)
            return False
    except Exception as e:
        print(f"❌ 構建過程中出現異常: {e}")
        return False

def create_deployment_package():
    """創建部署包"""
    print("📦 創建部署包...")
    
    # 創建部署目錄
    deploy_dir = Path('MES_System_Deploy')
    if deploy_dir.exists():
        shutil.rmtree(deploy_dir)
    
    deploy_dir.mkdir()
    
    # 複製可執行文件
    exe_path = Path('dist/代理MES上報系統.exe')
    if exe_path.exists():
        shutil.copy2(exe_path, deploy_dir / '代理MES上報系統.exe')
        print("✅ 已複製可執行文件")
    else:
        print("❌ 找不到可執行文件")
        return False
    
    # 創建啟動腳本
    start_script = '''@echo off
chcp 65001 > nul
echo 正在啟動代理MES上報系統...
echo.
echo 系統將在瀏覽器中打開: http://localhost:5000
echo 如果瀏覽器沒有自動打開，請手動訪問上述地址
echo.
echo 按 Ctrl+C 停止系統
echo.
"代理MES上報系統.exe"
pause
'''
    
    with open(deploy_dir / '啟動系統.bat', 'w', encoding='utf-8') as f:
        f.write(start_script)
    print("✅ 已創建啟動腳本")
    
    # 創建說明文件
    readme_content = '''# 代理MES上報系統 - 部署說明

## 系統要求
- Windows 10 或更高版本
- 無需安裝Python環境

## 安裝步驟
1. 將整個文件夾複製到目標電腦
2. 雙擊 "啟動系統.bat" 啟動系統
3. 系統會自動在瀏覽器中打開管理界面

## 使用說明
1. **訪問地址**: http://localhost:5000
2. **EAP設置**: 配置EAP服務器連接信息
3. **SIE設置**: 配置SIE服務器連接信息
4. **設備管理**: 添加和管理生產設備
5. **工單管理**: 配置設備工單信息
6. **監控功能**: 啟動EAP或SIE監控

## 注意事項
- 確保目標電腦能夠訪問EAP和SIE服務器
- 確保MES服務器地址可達
- 系統運行時請勿關閉命令行窗口
- 如需停止系統，按 Ctrl+C 或關閉命令行窗口

## 故障排除
1. **無法啟動**: 檢查是否有防火牆阻擋
2. **無法連接EAP/SIE**: 檢查網絡連接和服務器地址
3. **MES轉發失敗**: 檢查MES服務器配置和網絡連接

## 技術支持
如有問題請聯繫系統管理員
'''
    
    with open(deploy_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✅ 已創建說明文件")
    
    print(f"🎉 部署包創建完成: {deploy_dir.absolute()}")
    return True

def main():
    """主函數"""
    print("🏭 代理MES上報系統 - 打包工具")
    print("=" * 50)
    
    # 檢查必要文件
    required_files = ['app.py', 'templates', 'static']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺少必要文件: {file}")
            return False
    
    # 創建規格文件
    create_spec_file()
    
    # 構建可執行文件
    if not build_executable():
        return False
    
    # 創建部署包
    if not create_deployment_package():
        return False
    
    print("\n🎉 打包完成！")
    print("📁 部署包位置: MES_System_Deploy/")
    print("🚀 可以將整個 MES_System_Deploy 文件夾複製到目標電腦使用")
    
    return True

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)
