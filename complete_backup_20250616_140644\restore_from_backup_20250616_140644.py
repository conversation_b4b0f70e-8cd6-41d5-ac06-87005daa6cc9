#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
項目恢復腳本
從備份 complete_backup_20250616_140644 恢復項目
"""

import os
import shutil
import json
from datetime import datetime

def restore_project():
    """從備份恢復項目"""
    backup_dir = "complete_backup_20250616_140644"

    print(f"🔄 開始從 {backup_dir} 恢復項目...")

    # 讀取備份信息
    backup_info_file = os.path.join(backup_dir, "backup_info.json")
    if os.path.exists(backup_info_file):
        with open(backup_info_file, 'r', encoding='utf-8') as f:
            backup_info = json.load(f)
        print(f"📋 備份時間: {backup_info.get('backup_time')}")
        print(f"🐍 Python版本: {backup_info.get('python_version')}")

    # 恢復文件
    for item in os.listdir(backup_dir):
        if item in ["backup_info.json", "environment_info.json", "pip_freeze.txt", "python_version.txt"]:
            continue

        src_path = os.path.join(backup_dir, item)
        dst_path = item

        if os.path.isfile(src_path):
            shutil.copy2(src_path, dst_path)
            print(f"  ✅ {item}")
        elif os.path.isdir(src_path):
            if os.path.exists(dst_path):
                shutil.rmtree(dst_path)
            shutil.copytree(src_path, dst_path)
            print(f"  ✅ {item}/")

    print("✅ 項目恢復完成！")
    print("💡 請檢查 requirements.txt 並安裝依賴: pip install -r requirements.txt")
    print("💡 檢查 pip_freeze.txt 查看完整的依賴列表")
    print("💡 檢查 environment_info.json 查看原始環境信息")

if __name__ == "__main__":
    restore_project()
