# MES Upload Manager v2.0 - Deployment Guide
Build Date: 2025-07-07

## System Requirements
- Windows 10 or higher
- No Python installation required

## Installation Steps
1. Copy the entire folder to target computer
2. Double-click "Start_MES_Manager.bat" to start the system
3. System will automatically open in browser

## Usage Instructions
1. **Access URL**: http://localhost:5000
2. **EAP Settings**: Configure EAP server connection
3. **SIE Settings**: Configure SIE server connection (supports multiple servers)
4. **Device Management**: Add and manage production devices
5. **Work Order Management**: Configure device work orders with upload types
6. **Monitoring**: Start EAP or SIE monitoring

## Key Features
- Support for multiple SIE servers
- Work order upload type selection (PQM only, MES only, Both)
- Real-time production data monitoring
- Automatic MES forwarding with work order management
- Recent work orders tracking
- Error logging and monitoring

## Notes
- Ensure target computer can access EAP and SIE servers
- Ensure MES server address is reachable
- Do not close command window while system is running
- To stop system, press Ctrl+C or close command window

## Troubleshooting
1. **Cannot start**: Check firewall settings
2. **Cannot connect EAP/SIE**: Check network connection and server addresses
3. **MES forwarding fails**: Check MES server configuration and network

## Technical Support
Contact system administrator for assistance
