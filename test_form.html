<!DOCTYPE html>
<html>
<head>
    <title>測試表單</title>
    <style>
        .form-row {
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
        }
        .form-group {
            flex: 1;
            margin-bottom: 12px;
            padding: 0 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 3px;
            font-weight: 500;
            color: #333;
            font-size: 0.9em;
        }
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            background-color: white;
        }
        .form-group small {
            display: block;
            margin-top: 5px;
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <h2>測試工單表單</h2>
    <div class="form-row">
        <div class="form-group">
            <label for="new-station-type">過站類型 *</label>
            <select id="new-station-type" required>
                <option value="無條碼過站" selected>無條碼過站</option>
                <option value="有條碼過站" disabled>有條碼過站 (開發中)</option>
                <option value="有條碼投入" disabled>有條碼投入 (開發中)</option>
            </select>
            <small>目前僅支持無條碼過站，其他類型正在開發中</small>
        </div>
        <div class="form-group">
            <label for="new-upload-type">上傳類型 *</label>
            <select id="new-upload-type" required>
                <option value="同時上報PQM和MES" selected>同時上報PQM和MES</option>
                <option value="僅上報PQM">僅上報PQM</option>
                <option value="僅上報MES" disabled>僅上報MES (開發中)</option>
            </select>
            <small>選擇數據上報方式，默認同時上報</small>
        </div>
    </div>
</body>
</html>
