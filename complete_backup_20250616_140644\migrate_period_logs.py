#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
from datetime import datetime

def migrate_period_logs():
    """遷移設備數據，添加期間日誌字段"""
    data_file = 'devices.json'
    
    if not os.path.exists(data_file):
        print("設備數據文件不存在")
        return
    
    # 讀取現有數據
    with open(data_file, 'r', encoding='utf-8') as f:
        devices = json.load(f)
    
    print(f"讀取到 {len(devices)} 個設備")
    
    # 為每個設備添加新字段
    for device_id, device in devices.items():
        print(f"處理設備: {device_id}")
        print(f"原始字段: {list(device.keys())}")
        
        # 添加期間日誌字段（如果不存在）
        if 'recentLogs' not in device:
            device['recentLogs'] = []
            print("  添加 recentLogs")
        
        if 'lastFrontendUpdate' not in device:
            device['lastFrontendUpdate'] = datetime.now().isoformat()
            print("  添加 lastFrontendUpdate")
        
        # 更新時間戳
        device['updatedAt'] = datetime.now().isoformat()
        
        print(f"  更新後字段: {list(device.keys())}")
        print(f"✅ 已遷移設備: {device_id}")
        print()
    
    # 保存更新後的數據
    with open(data_file, 'w', encoding='utf-8') as f:
        json.dump(devices, f, ensure_ascii=False, indent=2)
    
    print("✅ 遷移完成！")

if __name__ == "__main__":
    migrate_period_logs()
