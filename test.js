msg.payload={
    "id": "59b31696-4bf1-4f7d-a9d7-ddf9788bcd39",
    "device": "18585fc9-f7e1-4c96-8c67-4a9f568e372c",
    "ts": 1751015135675,
    "trx": {
        "name": "UnitsTestLog",
        "body": {
            "CT": "18.8",
            "Error1": "Pass\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000",
            "Error2": "S,46\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000",
            "Error3": "\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000",
            "RecipeName": "201",
            "RecipeRevision": "201",
            "Result1": "1",
            "Result2": "2",
            "Result3": "2",
            "UnitIdentifier": "253813"
        }
    }
}
function formattedDate_db() {
    const date = new Date();
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    const second = date.getSeconds().toString().padStart(2, '0');
    const formattedDate = `${year}-${month}-${day}T${hour}:${minute}:${second}.000+08:00`;
    return formattedDate
}
function generateGUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0,
            v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}
function getTestStartTime(endTime, testDuration) {
    // ???????? Date ??
    const end = new Date(endTime);
    // ?????(?)?????
    const duration = parseFloat(testDuration) * 1000;
    // ??????
    const start = new Date(end.getTime() - duration);

    // ???????
    const year = start.getFullYear();
    const month = (start.getMonth() + 1).toString().padStart(2, '0');
    const day = start.getDate().toString().padStart(2, '0');
    const hour = start.getHours().toString().padStart(2, '0');
    const minute = start.getMinutes().toString().padStart(2, '0');
    const second = start.getSeconds().toString().padStart(2, '0');
    const milliseconds = start.getMilliseconds().toString().padStart(3, '0');

    return `${year}-${month}-${day}T${hour}:${minute}:${second}.${milliseconds}+08:00`;
}
function getElementValueInArray(arr, element) {

    if (element in arr) {

        return arr[element].replace(/\u0000/g, '');

    } else {

        return null;
    }
}

// 解析错误码，返回错误信息
function parseErrorCode(errorCode) {
    const errorMap = {
        "Pass": "Pass",
        "R": "毛邊",
        "S": "缺料",
        "C": "銅套覆盖",
        "E": "空模",
        "W": "發白",
        "B": "燒焦",
        "H": "螺絲孔距"
    };

    // 如果是Pass，返回空字符串，否则返回对应的错误名称
    return errorCode === "Pass" ? "" : (errorMap[errorCode] || "未知錯誤，錯誤代碼為：" + errorCode);
}

// 解析错误结果，判断是否通过
function isResultPassed(errorString) {
    return errorString === "Pass";
}

// 解析相机3的Error3字符串，格式为："Pass,0,0,215992,21"
function parseCamera3Error(errorString) {
    // 先清理字符串中的\u0000
    const cleaned = errorString.replace(/\u0000/g, '');
    const parts = cleaned.split(',');

    const result = {
        isPassed: parts[0].toUpperCase() === 'PASS',
        errorName: parseErrorCode(parts[0]),
        areas: []
    };

    // 读取四个面的NG面积
    if (parts.length >= 5) {
        for (let i = 1; i <= 4; i++) {
            result.areas.push(parseInt(parts[i]) || 0);
        }
    }

    return result;
}

// 为相机3生成测量结果
function generateCamera3Measurements(camera3Error, timeStamp, isPassed) {
    const parsedError = parseCamera3Error(camera3Error);

    const measurementNames = [
        'FAN_FRAME_SIDE_1',  // 扇框第一檢測面
        'FAN_FRAME_SIDE_2',  // 扇框第二檢測面
        'FAN_FRAME_SIDE_3',  // 扇框第三檢測面
        'FAN_FRAME_SIDE_4'   // 扇框第四檢測面
    ];

    // 如果測試通過，則所有面積值為0
    if (isPassed) {
        return Array(4).fill().map((_, index) => ({
            "$type": "CFX.Structures.MeasurementValue, CFX",
            "Value": 0,
            "ExpectedMaximum": 0,
            "UniqueIdentifier": generateGUID(),
            "MeasurementName": measurementNames[index],
            "TimeRecorded": timeStamp,
            "Sequence": index,
            "Result": "Passed",
            "CRDs": null
        }));
    }

    // 如果測試不通過，則使用實際的NG面積值
    return parsedError.areas.map((area, index) => ({
        "$type": "CFX.Structures.MeasurementValue, CFX",
        "Value": area,
        "ExpectedMaximum": 0,
        "UniqueIdentifier": generateGUID(),
        "MeasurementName": measurementNames[index],
        "TimeRecorded": timeStamp,
        "Sequence": index,
        "Result": area === 0 ? "Passed" : "Failed",
        "CRDs": null
    }));
}

// 清理错误字符串，处理前导零和不同格式
function cleanErrorString(errorStr) {
    if (!errorStr) return "";

    // 移除空字符（保留这个操作）
    let cleaned = errorStr.replace(/\u0000/g, '');

    // 如果是 Pass，直接返回
    if (cleaned.toUpperCase() === 'PASS') return 'Pass';

    // 如果包含逗号，说明是相机2或相机3的格式，直接返回清理后的字符串
    if (cleaned.includes(',')) {
        return cleaned;
    }

    // 处理只有错误码的情况（相机1格式和相机2无逗号格式）
    // 如果已经是单个错误码，直接返回
    if (['R', 'S', 'C', 'E', 'W', 'B', 'H'].includes(cleaned)) {
        return cleaned;
    }

    // 移除所有的0
    let errorCode = cleaned.replace(/0/g, '');

    // 检查是否是有效的错误码
    if (['R', 'S', 'C', 'E', 'W', 'B', 'H'].includes(errorCode)) {
        return errorCode;
    }

    // 如果错误码在后面，反转字符串
    errorCode = errorCode.split('').reverse().join('');
    if (['R', 'S', 'C', 'E', 'W', 'B', 'H'].includes(errorCode)) {
        return errorCode;
    }

    // 如果都不符合，返回原始清理后的字符串
    return cleaned;
}

// 解析相机2的Error2字符串
function parseCamera2Error(errorString) {
    // 先清理字符串中的\u0000
    const cleaned = errorString.replace(/\u0000/g, '');

    // 如果是Pass，直接返回
    if (cleaned.toUpperCase() === 'PASS') {
        return {
            isPassed: true,
            errorName: "",
            area: 0
        };
    }

    // 如果包含逗号，说明有NG面积
    if (cleaned.includes(',')) {
        const parts = cleaned.split(',');
        return {
            isPassed: false,
            errorName: parseErrorCode(parts[0]),
            area: parseInt(parts[1]) || 0
        };
    }

    // 如果不包含逗号，直接使用cleanErrorString处理
    const errorCode = cleanErrorString(cleaned);
    return {
        isPassed: false,
        errorName: parseErrorCode(errorCode),
        area: 0
    };
}

// 为相机2生成测量结果
function generateCamera2Measurements(camera2Error, timeStamp, isPassed) {
    const parsedError = parseCamera2Error(camera2Error);

    // 如果測試通過，則面積值為0
    if (isPassed) {
        return [{
            "$type": "CFX.Structures.MeasurementValue, CFX",
            "Value": 0,
            "ExpectedMaximum": 0,
            "UniqueIdentifier": generateGUID(),
            "MeasurementName": "Camera2_FRAME_SIDE_1",
            "TimeRecorded": timeStamp,
            "Sequence": 0,
            "Result": "Passed",
            "CRDs": null
        }];
    }

    // 如果測試不通過，使用實際的NG面積值
    return [{
        "$type": "CFX.Structures.MeasurementValue, CFX",
        "Value": parsedError.area,
        "ExpectedMaximum": 0,
        "UniqueIdentifier": generateGUID(),
        "MeasurementName": "Camera2_FRAME_SIDE_1",
        "TimeRecorded": timeStamp,
        "Sequence": 0,
        "Result": parsedError.area === 0 ? "Passed" : "Failed",
        "CRDs": null
    }];
}

// 修改生成缺陷信息的函数，添加面积信息
function generateDefects(errorCode, area = null) {
    // 如果是Pass，不生成缺陷
    if (errorCode === "Pass") {
        return [];
    }

    const defect = {
        "$type": "CFX.Structures.Defect, CFX",
        "UniqueIdentifier": generateGUID(),
        "DefectCode": errorCode,
        "DefectCategory": "AOI",
        "Description": parseErrorCode(errorCode),
        "Comments": area !== null ? `NG面積: ${area}` : "",
        "ComponentOfInterest": {
            "$type": "CFX.Structures.ComponentDesignator, CFX",
            "ReferenceDesignator": "ALL",
            "UnitPosition": {
                "$type": "CFX.Structures.UnitPosition, CFX",
                "UnitIdentifier": generateGUID(),
                "PositionNumber": 1,
                "PositionName": "ALL",
                "X": 0,
                "Y": 0,
                "Rotation": 0,
                "FlipX": false,
                "FlipY": false,
                "Status": "Fail"
            },
            "PartNumber": "1"
        },
        "RegionOfInterest": null,
        "DefectImages": [],
        "Priority": 0,
        "ConfidenceLevel": 100,
        "RelatedMeasurements": [],
        "RelatedSymptoms": [],
        "Verification": "NotVerifiedYet",
        "VerificationDetail": ""
    };

    return [defect];
}

let oldunitIdentifier = flow.get("unitIdentifier") || "0";
let unitIdentifier = "0";

// 检查数据是否有效（不是全0）
function isValidErrorData(error1, error2, error3) {
    // 检查是否所有数据都是0或者全是\u0000
    const isAllZeros = (str) => !str || /^[0\u0000]+$/.test(str);

    return !(isAllZeros(error1) || isAllZeros(error2) || isAllZeros(error3));
}

if (msg.payload.trx.name == "UnitsTestLog") {
    unitIdentifier = msg.payload.trx.body.UnitIdentifier;
    if (unitIdentifier != oldunitIdentifier) {
        // 首先检查原始数据是否有效
        if (!isValidErrorData(
            msg.payload.trx.body.Error1,
            msg.payload.trx.body.Error2,
            msg.payload.trx.body.Error3
        )) {
            // 如果数据无效，直接返回null或undefined，不输出msg.payload
            return;
        }

        const nowtime = formattedDate_db()
        const testStartTime = getTestStartTime(nowtime, msg.payload.trx.body.CT)

        // 清理错误字符串
        const cleanError1 = cleanErrorString(msg.payload.trx.body.Error1);
        const cleanError2 = cleanErrorString(msg.payload.trx.body.Error2);
        const cleanError3 = cleanErrorString(msg.payload.trx.body.Error3);

        // 判断是否所有相机都通过
        const camera1Result = isResultPassed(cleanError1);
        const camera2Result = parseCamera2Error(cleanError2).isPassed;
        const camera3Result = parseCamera3Error(cleanError3).isPassed;
        const overallResult = (camera1Result && camera2Result && camera3Result) ? "Passed" : "Failed";

        const camera2ParsedError = parseCamera2Error(cleanError2);
        const camera3ParsedError = parseCamera3Error(cleanError3);

        const UnitsInspectedExample = {
            "MessageName": "CFX.Production.TestAndInspection.UnitsInspected",
            "Version": "1.7",
            "TimeStamp": nowtime,
            "UniqueID": generateGUID(),
            "Source": "CFX.B00.OT01270001",
            "Target": null,
            "RequestID": null,
            "MessageBody": {
                "$type": "CFX.Production.TestAndInspection.UnitsInspected, CFX",
                "TransactionId": generateGUID(),
                "InspectionMethod": "AOI",
                "SamplingInformation": null,
                "Inspector": {
                    "$type": "CFX.Structures.Operator, CFX",
                    "OperatorIdentifier": "CFX.B00.OT01270001",
                    "ActorType": "Robot",
                    "LastName": "AOI",
                    "FirstName": "AOI",
                    "LoginName": "AOI"
                },
                "RecipeName": getElementValueInArray(msg.payload.trx.body, "RecipeName"),
                "RecipeRevision": getElementValueInArray(msg.payload.trx.body, "RecipeRevision"),
                "InspectedUnits": [
                    {
                        "$type": "CFX.Structures.InspectedUnit, CFX",
                        "UnitIdentifier": getElementValueInArray(msg.payload.trx.body, "UnitIdentifier"),
                        "UnitPositionNumber": 1,
                        "OverallResult": overallResult,
                        "Inspections": [
                            {
                                "$type": "CFX.Structures.Inspection, CFX",
                                "UniqueIdentifier": generateGUID(),
                                "InspectionName": "Camera1",
                                "InspectionStartTime": testStartTime,
                                "InspectionEndTime": nowtime,
                                "TestProcedure": null,
                                "Comments": cleanError1,
                                "Result": camera1Result ? "Passed" : "Failed",
                                "Verification": "NotVerifiedYet",
                                "VerificationDetail": null,
                                "Error": parseErrorCode(cleanError1),
                                "DefectsFound": generateDefects(cleanError1),
                                "Symptoms": [],
                                "Measurements": null
                            },
                            {
                                "$type": "CFX.Structures.Inspection, CFX",
                                "UniqueIdentifier": generateGUID(),
                                "InspectionName": "Camera2",
                                "InspectionStartTime": testStartTime,
                                "InspectionEndTime": nowtime,
                                "TestProcedure": null,
                                "Comments": cleanError2,
                                "Result": camera2Result ? "Passed" : "Failed",
                                "Verification": "NotVerifiedYet",
                                "VerificationDetail": null,
                                "Error": parseCamera2Error(cleanError2).errorName,
                                "DefectsFound": generateDefects(cleanError2.split(',')[0], camera2ParsedError.area),
                                "Symptoms": [],
                                "Measurements": generateCamera2Measurements(cleanError2, nowtime, camera2Result)
                            },
                            {
                                "$type": "CFX.Structures.Inspection, CFX",
                                "UniqueIdentifier": generateGUID(),
                                "InspectionName": "Camera3",
                                "InspectionStartTime": testStartTime,
                                "InspectionEndTime": nowtime,
                                "TestProcedure": null,
                                "Comments": cleanError3,
                                "Result": camera3Result ? "Passed" : "Failed",
                                "Verification": "NotVerifiedYet",
                                "VerificationDetail": null,
                                "Error": parseCamera3Error(cleanError3).errorName,
                                "DefectsFound": generateDefects(parseCamera3Error(cleanError3).isPassed ? "Pass" : cleanError3.split(',')[0]),
                                "Symptoms": [],
                                "Measurements": generateCamera3Measurements(cleanError3, nowtime, camera3Result)
                            }
                        ],
                        "Verification": "NotVerifiedYet",
                        "TotalInspectionCount": 3
                    }
                ],
                "InspectedPanel": null
            }
        }

        flow.set("unitIdentifier", unitIdentifier);
        msg.payload = UnitsInspectedExample;
        return msg;
    }
}