{"S720050001": {"deviceId": "S720050001", "lineName": "A1-02", "sectionName": "TESTING", "groupName": "TEST", "stationName": "STATION_03", "isActive": true, "isMonitoring": true, "productCount": 426, "forwardedCount": 296, "unforwardedCount": 130, "lastLog": "測試產品條碼-8a46d287", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2025-05-12T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO328802", "targetQuantity": 78, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-14T16:59:31.424796", "status": "completed", "mesForwardedCount": 14, "completionRate": 65.7}, {"workOrderNumber": "WO836225", "targetQuantity": 328, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-19T16:59:31.424796", "status": "completed", "mesForwardedCount": 3, "completionRate": 42.8}, {"workOrderNumber": "WO610480", "targetQuantity": 477, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-25T16:59:31.424796", "status": "active", "mesForwardedCount": 13, "completionRate": 27.6}], "currentWorkOrder": 2, "currentWorkOrderProgress": 42, "displayWorkOrder": {"workOrderNumber": "WO610480", "targetQuantity": 477, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-25T16:59:31.424796", "status": "active", "mesForwardedCount": 13, "completionRate": 27.6}, "mesErrors": [{"timestamp": "2025-06-12T22:01:31.424796", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050001"}}, {"timestamp": "2025-06-12T21:44:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050001"}}], "recentLogs": [{"message": "測試條碼-f3244bac", "timestamp": "2025-06-13T16:50:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-ed602004", "timestamp": "2025-06-13T16:56:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-1e0640b7", "timestamp": "2025-06-13T16:33:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-9e2c04eb", "timestamp": "2025-06-13T16:09:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-11c4741d", "timestamp": "2025-06-13T16:19:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-3fedcfbd", "timestamp": "2025-06-13T16:43:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-9ff2e003", "timestamp": "2025-06-13T16:02:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-bb67bac2", "timestamp": "2025-06-13T16:28:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-35eda420", "timestamp": "2025-06-13T16:57:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-bc6074dc", "timestamp": "2025-06-13T16:07:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-ec517988", "timestamp": "2025-06-13T16:03:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-082af7de", "timestamp": "2025-06-13T16:31:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-8a09efe9", "timestamp": "2025-06-13T16:51:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-cbcd39ca", "timestamp": "2025-06-13T16:48:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-b61b44ae", "timestamp": "2025-06-13T16:53:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-cc098339", "timestamp": "2025-06-13T16:58:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-7ca7d14f", "timestamp": "2025-06-13T16:31:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-a38a5c71", "timestamp": "2025-06-13T16:43:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-e696a526", "timestamp": "2025-06-13T16:09:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-75bc2c9d", "timestamp": "2025-06-13T16:07:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}]}, "S720050002": {"deviceId": "S720050002", "lineName": "A2-02", "sectionName": "ASSEMBLY", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 393, "forwardedCount": 40, "unforwardedCount": 353, "lastLog": "測試產品條碼-b8a411d8", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2025-02-12T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO574824", "targetQuantity": 191, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-23T16:59:31.424796", "status": "active", "mesForwardedCount": 6, "completionRate": 51.6}, {"workOrderNumber": "WO323305", "targetQuantity": 136, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-27T16:59:31.424796", "status": "active", "mesForwardedCount": 80, "completionRate": 98.4}, {"workOrderNumber": "WO640633", "targetQuantity": 418, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-05T16:59:31.424796", "status": "active", "mesForwardedCount": 77, "completionRate": 64.2}], "currentWorkOrder": 0, "currentWorkOrderProgress": 92, "displayWorkOrder": {"workOrderNumber": "WO574824", "targetQuantity": 191, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-23T16:59:31.424796", "status": "active", "mesForwardedCount": 6, "completionRate": 51.6}, "mesErrors": [{"timestamp": "2025-06-13T11:29:31.424796", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050002"}}], "recentLogs": [{"message": "測試條碼-a1de6a64", "timestamp": "2025-06-13T16:49:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-5ac50b43", "timestamp": "2025-06-13T16:00:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-84c04c25", "timestamp": "2025-06-13T16:33:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-25e38bce", "timestamp": "2025-06-13T16:11:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-b4834474", "timestamp": "2025-06-13T16:19:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f81a60ca", "timestamp": "2025-06-13T16:41:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-e2b46d7b", "timestamp": "2025-06-13T16:54:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-87cf75c5", "timestamp": "2025-06-13T16:10:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-81d4e0a8", "timestamp": "2025-06-13T16:14:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a1ceecae", "timestamp": "2025-06-13T16:55:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-a1427ce5", "timestamp": "2025-06-13T16:16:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-55354328", "timestamp": "2025-06-13T16:14:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-ed62709b", "timestamp": "2025-06-13T16:09:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-60f8cebf", "timestamp": "2025-06-13T16:52:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-9645f1b0", "timestamp": "2025-06-13T16:25:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}]}, "S720050003": {"deviceId": "S720050003", "lineName": "B2-02", "sectionName": "PACKAGING", "groupName": "ASSEMBLY", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 674, "forwardedCount": 185, "unforwardedCount": 489, "lastLog": "測試產品條碼-4cdd5f59", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2025-06-04T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO797458", "targetQuantity": 473, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-06T16:59:31.424796", "status": "active", "mesForwardedCount": 97, "completionRate": 26.4}, {"workOrderNumber": "WO657279", "targetQuantity": 227, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-06T16:59:31.424796", "status": "pending", "mesForwardedCount": 32, "completionRate": 86.3}, {"workOrderNumber": "WO184744", "targetQuantity": 321, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-20T16:59:31.424796", "status": "active", "mesForwardedCount": 33, "completionRate": 84.1}], "currentWorkOrder": 2, "currentWorkOrderProgress": 54, "displayWorkOrder": {"workOrderNumber": "WO184744", "targetQuantity": 321, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-20T16:59:31.424796", "status": "active", "mesForwardedCount": 33, "completionRate": 84.1}, "mesErrors": [{"timestamp": "2025-06-13T14:59:31.424796", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050003"}}, {"timestamp": "2025-06-12T22:36:31.424796", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050003"}}], "recentLogs": [{"message": "測試條碼-bd1ca8a4", "timestamp": "2025-06-13T16:44:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-16d2d7f8", "timestamp": "2025-06-13T16:12:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e3587fd3", "timestamp": "2025-06-13T16:10:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-0a14314a", "timestamp": "2025-06-13T16:45:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-3dacc8b9", "timestamp": "2025-06-13T16:50:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-6d8f180d", "timestamp": "2025-06-13T16:04:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d081158a", "timestamp": "2025-06-13T16:10:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-4e87a7f8", "timestamp": "2025-06-13T16:51:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-db67ef6f", "timestamp": "2025-06-13T16:02:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}]}, "S720050004": {"deviceId": "S720050004", "lineName": "B2-01", "sectionName": "TESTING", "groupName": "INJECTION", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 234, "forwardedCount": 183, "unforwardedCount": 51, "lastLog": "測試產品條碼-320ba87b", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2025-03-20T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO797851", "targetQuantity": 334, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-01T16:59:31.424796", "status": "pending", "mesForwardedCount": 32, "completionRate": 1.5}, {"workOrderNumber": "WO779529", "targetQuantity": 113, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-14T16:59:31.424796", "status": "pending", "mesForwardedCount": 66, "completionRate": 67.7}, {"workOrderNumber": "WO905990", "targetQuantity": 306, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-16T16:59:31.424796", "status": "completed", "mesForwardedCount": 8, "completionRate": 34.2}], "currentWorkOrder": 2, "currentWorkOrderProgress": 60, "displayWorkOrder": {"workOrderNumber": "WO905990", "targetQuantity": 306, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-16T16:59:31.424796", "status": "completed", "mesForwardedCount": 8, "completionRate": 34.2}, "mesErrors": [{"timestamp": "2025-06-13T12:29:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050004"}}, {"timestamp": "2025-06-12T20:16:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050004"}}, {"timestamp": "2025-06-13T03:03:31.424796", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050004"}}, {"timestamp": "2025-06-13T10:29:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050004"}}, {"timestamp": "2025-06-13T07:11:31.424796", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050004"}}], "recentLogs": [{"message": "測試條碼-b607ee6b", "timestamp": "2025-06-13T16:49:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-028ccc61", "timestamp": "2025-06-13T16:06:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-4db3b5a4", "timestamp": "2025-06-13T16:53:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-b8152ad0", "timestamp": "2025-06-13T16:53:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-6ed6d6c7", "timestamp": "2025-06-13T16:12:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-071eb84e", "timestamp": "2025-06-13T16:15:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-d5e6a7ed", "timestamp": "2025-06-13T16:47:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-d5cf9cf9", "timestamp": "2025-06-13T16:50:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-b8e9f700", "timestamp": "2025-06-13T16:37:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-bc231c5a", "timestamp": "2025-06-13T16:06:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-1879efa6", "timestamp": "2025-06-13T16:53:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-ee0468c3", "timestamp": "2025-06-13T16:06:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-0824ea07", "timestamp": "2025-06-13T16:56:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}]}, "S720050005": {"deviceId": "S720050005", "lineName": "B1-01", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_03", "isActive": true, "isMonitoring": true, "productCount": 431, "forwardedCount": 423, "unforwardedCount": 8, "lastLog": "測試產品條碼-c2137894", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2024-12-08T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO652056", "targetQuantity": 57, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-10T16:59:31.424796", "status": "pending", "mesForwardedCount": 56, "completionRate": 93.1}, {"workOrderNumber": "WO897406", "targetQuantity": 100, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-14T16:59:31.424796", "status": "pending", "mesForwardedCount": 53, "completionRate": 6.6}, {"workOrderNumber": "WO591440", "targetQuantity": 335, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-21T16:59:31.424796", "status": "completed", "mesForwardedCount": 25, "completionRate": 11.5}], "currentWorkOrder": 0, "currentWorkOrderProgress": 79, "displayWorkOrder": {"workOrderNumber": "WO652056", "targetQuantity": 57, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-10T16:59:31.424796", "status": "pending", "mesForwardedCount": 56, "completionRate": 93.1}, "mesErrors": [{"timestamp": "2025-06-13T13:12:31.424796", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050005"}}], "recentLogs": [{"message": "測試條碼-90a450bc", "timestamp": "2025-06-13T16:27:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-80584e5f", "timestamp": "2025-06-13T16:40:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-3721261e", "timestamp": "2025-06-13T16:42:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-336e1259", "timestamp": "2025-06-13T16:31:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-5b617e6a", "timestamp": "2025-06-13T16:20:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-2d717508", "timestamp": "2025-06-13T16:42:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-11fac15f", "timestamp": "2025-06-13T16:45:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a3520c1d", "timestamp": "2025-06-13T16:28:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}]}, "S720050006": {"deviceId": "S720050006", "lineName": "A2-02", "sectionName": "ASSEMBLY", "groupName": "INJECTION", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 724, "forwardedCount": 15, "unforwardedCount": 709, "lastLog": "測試產品條碼-3d1b38cc", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2025-01-12T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO321191", "targetQuantity": 60, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-03T16:59:31.424796", "status": "completed", "mesForwardedCount": 12, "completionRate": 46.1}, {"workOrderNumber": "WO358628", "targetQuantity": 475, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-02T16:59:31.424796", "status": "completed", "mesForwardedCount": 55, "completionRate": 83.1}, {"workOrderNumber": "WO884850", "targetQuantity": 211, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-16T16:59:31.424796", "status": "active", "mesForwardedCount": 74, "completionRate": 86.0}], "currentWorkOrder": 0, "currentWorkOrderProgress": 49, "displayWorkOrder": {"workOrderNumber": "WO321191", "targetQuantity": 60, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-03T16:59:31.424796", "status": "completed", "mesForwardedCount": 12, "completionRate": 46.1}, "mesErrors": [{"timestamp": "2025-06-13T11:08:31.424796", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050006"}}], "recentLogs": [{"message": "測試條碼-948ab6a3", "timestamp": "2025-06-13T15:59:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-0d3d708d", "timestamp": "2025-06-13T16:48:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-01660261", "timestamp": "2025-06-13T16:47:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-3a5e8282", "timestamp": "2025-06-13T16:06:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-2aa507fd", "timestamp": "2025-06-13T16:28:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-38cb0db3", "timestamp": "2025-06-13T16:25:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-344ad7a4", "timestamp": "2025-06-13T16:38:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-af31cf1e", "timestamp": "2025-06-13T16:37:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}]}, "S720050007": {"deviceId": "S720050007", "lineName": "B1-01", "sectionName": "TESTING", "groupName": "TEST", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 821, "forwardedCount": 635, "unforwardedCount": 186, "lastLog": "測試產品條碼-5755c6d6", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2024-12-22T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO462197", "targetQuantity": 94, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-30T16:59:31.424796", "status": "completed", "mesForwardedCount": 20, "completionRate": 81.4}, {"workOrderNumber": "WO355078", "targetQuantity": 354, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-10T16:59:31.424796", "status": "active", "mesForwardedCount": 73, "completionRate": 99.9}, {"workOrderNumber": "WO985377", "targetQuantity": 456, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-12T16:59:31.424796", "status": "pending", "mesForwardedCount": 82, "completionRate": 66.0}], "currentWorkOrder": 1, "currentWorkOrderProgress": 43, "displayWorkOrder": {"workOrderNumber": "WO355078", "targetQuantity": 354, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-10T16:59:31.424796", "status": "active", "mesForwardedCount": 73, "completionRate": 99.9}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-e7eaf6b2", "timestamp": "2025-06-13T16:33:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-01e4a93e", "timestamp": "2025-06-13T16:33:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-5931c51d", "timestamp": "2025-06-13T16:47:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-2cf11430", "timestamp": "2025-06-13T16:15:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-da208b50", "timestamp": "2025-06-13T16:26:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-e66fa8d4", "timestamp": "2025-06-13T16:45:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-37969aa5", "timestamp": "2025-06-13T16:55:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-cf3cf96d", "timestamp": "2025-06-13T16:42:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-3411b2b3", "timestamp": "2025-06-13T16:41:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-cf3c1662", "timestamp": "2025-06-13T16:02:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-511a029c", "timestamp": "2025-06-13T16:13:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-9375ac63", "timestamp": "2025-06-13T16:45:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1ec0cfa6", "timestamp": "2025-06-13T16:58:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-5fccfd3a", "timestamp": "2025-06-13T16:01:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-0e34326a", "timestamp": "2025-06-13T16:01:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}]}, "S720050008": {"deviceId": "S720050008", "lineName": "B1-01", "sectionName": "INJECTION", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 193, "forwardedCount": 118, "unforwardedCount": 75, "lastLog": "測試產品條碼-cff9f5cd", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2024-11-21T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO850603", "targetQuantity": 296, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-18T16:59:31.424796", "status": "pending", "mesForwardedCount": 99, "completionRate": 40.1}, {"workOrderNumber": "WO859743", "targetQuantity": 251, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-16T16:59:31.424796", "status": "pending", "mesForwardedCount": 5, "completionRate": 42.0}, {"workOrderNumber": "WO618799", "targetQuantity": 330, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-16T16:59:31.424796", "status": "pending", "mesForwardedCount": 94, "completionRate": 21.4}], "currentWorkOrder": 1, "currentWorkOrderProgress": 87, "displayWorkOrder": {"workOrderNumber": "WO859743", "targetQuantity": 251, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-16T16:59:31.424796", "status": "pending", "mesForwardedCount": 5, "completionRate": 42.0}, "mesErrors": [{"timestamp": "2025-06-13T02:09:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050008"}}], "recentLogs": [{"message": "測試條碼-a9f14112", "timestamp": "2025-06-13T16:37:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-57eeb097", "timestamp": "2025-06-13T16:19:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-6b5adc09", "timestamp": "2025-06-13T16:46:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-efffaaf8", "timestamp": "2025-06-13T16:28:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d0bec88c", "timestamp": "2025-06-13T16:53:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-3fb0650d", "timestamp": "2025-06-13T16:10:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-661d67fc", "timestamp": "2025-06-13T16:56:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-b88230cf", "timestamp": "2025-06-13T16:52:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-83656017", "timestamp": "2025-06-13T16:38:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-41a8567c", "timestamp": "2025-06-13T16:11:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-3e079f53", "timestamp": "2025-06-13T16:47:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-cbc90d58", "timestamp": "2025-06-13T16:20:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-fac2389d", "timestamp": "2025-06-13T16:20:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-e123bf85", "timestamp": "2025-06-13T16:39:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-57376602", "timestamp": "2025-06-13T16:57:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-bb99a3bf", "timestamp": "2025-06-13T16:44:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-dabf02ad", "timestamp": "2025-06-13T16:27:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-a28f2163", "timestamp": "2025-06-13T16:05:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-72b8d62a", "timestamp": "2025-06-13T16:52:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}]}, "S720050009": {"deviceId": "S720050009", "lineName": "A2-02", "sectionName": "TESTING", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 242, "forwardedCount": 192, "unforwardedCount": 50, "lastLog": "測試產品條碼-ff5115aa", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2025-05-16T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO126462", "targetQuantity": 489, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-28T16:59:31.424796", "status": "active", "mesForwardedCount": 98, "completionRate": 81.7}, {"workOrderNumber": "WO777116", "targetQuantity": 376, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-22T16:59:31.424796", "status": "completed", "mesForwardedCount": 3, "completionRate": 57.7}, {"workOrderNumber": "WO558002", "targetQuantity": 309, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-08T16:59:31.424796", "status": "completed", "mesForwardedCount": 44, "completionRate": 42.9}], "currentWorkOrder": 0, "currentWorkOrderProgress": 76, "displayWorkOrder": {"workOrderNumber": "WO126462", "targetQuantity": 489, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-28T16:59:31.424796", "status": "active", "mesForwardedCount": 98, "completionRate": 81.7}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-521dd5c0", "timestamp": "2025-06-13T16:04:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-c86d9be2", "timestamp": "2025-06-13T16:28:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-38b4758e", "timestamp": "2025-06-13T16:27:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-96900ea9", "timestamp": "2025-06-13T16:03:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-a91e070f", "timestamp": "2025-06-13T16:01:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-3fd2c211", "timestamp": "2025-06-13T16:38:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}]}, "S720050010": {"deviceId": "S720050010", "lineName": "A1-01", "sectionName": "ASSEMBLY", "groupName": "TEST", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 756, "forwardedCount": 10, "unforwardedCount": 746, "lastLog": "測試產品條碼-994dc53e", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2024-08-13T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO433013", "targetQuantity": 80, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-27T16:59:31.424796", "status": "completed", "mesForwardedCount": 28, "completionRate": 10.9}, {"workOrderNumber": "WO849457", "targetQuantity": 152, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-05T16:59:31.424796", "status": "active", "mesForwardedCount": 68, "completionRate": 34.6}, {"workOrderNumber": "WO182032", "targetQuantity": 478, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-07T16:59:31.424796", "status": "completed", "mesForwardedCount": 28, "completionRate": 31.7}], "currentWorkOrder": 2, "currentWorkOrderProgress": 13, "displayWorkOrder": {"workOrderNumber": "WO182032", "targetQuantity": 478, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-07T16:59:31.424796", "status": "completed", "mesForwardedCount": 28, "completionRate": 31.7}, "mesErrors": [{"timestamp": "2025-06-13T09:47:31.424796", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050010"}}, {"timestamp": "2025-06-13T02:25:31.424796", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050010"}}, {"timestamp": "2025-06-12T21:31:31.424796", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050010"}}, {"timestamp": "2025-06-12T21:13:31.424796", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050010"}}], "recentLogs": [{"message": "測試條碼-37ca2fbb", "timestamp": "2025-06-13T16:50:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a8c0b7f8", "timestamp": "2025-06-13T16:42:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-190ece40", "timestamp": "2025-06-13T16:49:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1b79faea", "timestamp": "2025-06-13T16:05:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-2638d9b3", "timestamp": "2025-06-13T16:12:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-c995bc7f", "timestamp": "2025-06-13T16:31:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-6c8e7bd9", "timestamp": "2025-06-13T16:33:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e5597cdd", "timestamp": "2025-06-13T16:19:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a474c9eb", "timestamp": "2025-06-13T16:54:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}]}, "S720050011": {"deviceId": "S720050011", "lineName": "A2-01", "sectionName": "INJECTION", "groupName": "ASSEMBLY", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 867, "forwardedCount": 138, "unforwardedCount": 729, "lastLog": "測試產品條碼-373c29f6", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2024-10-24T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO975743", "targetQuantity": 416, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-10T16:59:31.424796", "status": "pending", "mesForwardedCount": 8, "completionRate": 90.9}, {"workOrderNumber": "WO503597", "targetQuantity": 264, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-01T16:59:31.424796", "status": "pending", "mesForwardedCount": 47, "completionRate": 79.2}, {"workOrderNumber": "WO707220", "targetQuantity": 282, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-05T16:59:31.424796", "status": "completed", "mesForwardedCount": 68, "completionRate": 18.8}], "currentWorkOrder": 0, "currentWorkOrderProgress": 55, "displayWorkOrder": {"workOrderNumber": "WO975743", "targetQuantity": 416, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-10T16:59:31.424796", "status": "pending", "mesForwardedCount": 8, "completionRate": 90.9}, "mesErrors": [{"timestamp": "2025-06-13T13:03:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050011"}}, {"timestamp": "2025-06-13T11:35:31.424796", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050011"}}, {"timestamp": "2025-06-13T02:34:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050011"}}, {"timestamp": "2025-06-12T21:14:31.424796", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050011"}}], "recentLogs": [{"message": "測試條碼-ef669c98", "timestamp": "2025-06-13T16:28:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-ededff2f", "timestamp": "2025-06-13T16:25:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-87a1ec6d", "timestamp": "2025-06-13T16:35:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-62bdc9bc", "timestamp": "2025-06-13T16:07:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-2afe9e50", "timestamp": "2025-06-13T16:46:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-c2982ac0", "timestamp": "2025-06-13T16:51:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-dabe0413", "timestamp": "2025-06-13T16:34:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-10007063", "timestamp": "2025-06-13T16:51:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-51e1c8f9", "timestamp": "2025-06-13T16:43:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-c61bd923", "timestamp": "2025-06-13T16:00:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-c965c41c", "timestamp": "2025-06-13T16:42:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-fb168df1", "timestamp": "2025-06-13T16:45:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-38e65550", "timestamp": "2025-06-13T16:04:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-d5642cd9", "timestamp": "2025-06-13T16:56:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}]}, "S720050012": {"deviceId": "S720050012", "lineName": "A2-02", "sectionName": "ASSEMBLY", "groupName": "PACK", "stationName": "STATION_03", "isActive": true, "isMonitoring": true, "productCount": 660, "forwardedCount": 4, "unforwardedCount": 656, "lastLog": "測試產品條碼-4faee2c3", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2024-07-22T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO866907", "targetQuantity": 479, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-31T16:59:31.424796", "status": "active", "mesForwardedCount": 36, "completionRate": 63.1}, {"workOrderNumber": "WO682728", "targetQuantity": 459, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-10T16:59:31.424796", "status": "pending", "mesForwardedCount": 8, "completionRate": 54.6}, {"workOrderNumber": "WO979197", "targetQuantity": 175, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-04T16:59:31.424796", "status": "active", "mesForwardedCount": 61, "completionRate": 12.5}], "currentWorkOrder": 1, "currentWorkOrderProgress": 6, "displayWorkOrder": {"workOrderNumber": "WO682728", "targetQuantity": 459, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-10T16:59:31.424796", "status": "pending", "mesForwardedCount": 8, "completionRate": 54.6}, "mesErrors": [{"timestamp": "2025-06-13T14:19:31.424796", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050012"}}, {"timestamp": "2025-06-13T12:34:31.424796", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050012"}}], "recentLogs": [{"message": "測試條碼-7b9949f4", "timestamp": "2025-06-13T16:33:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-27f07524", "timestamp": "2025-06-13T16:56:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-988840ef", "timestamp": "2025-06-13T16:36:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-4efac54e", "timestamp": "2025-06-13T16:27:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-4e213e80", "timestamp": "2025-06-13T16:34:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}]}, "S720050013": {"deviceId": "S720050013", "lineName": "B1-01", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 712, "forwardedCount": 7, "unforwardedCount": 705, "lastLog": "測試產品條碼-3872529c", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2025-03-02T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO321237", "targetQuantity": 278, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-22T16:59:31.424796", "status": "completed", "mesForwardedCount": 62, "completionRate": 76.0}, {"workOrderNumber": "WO672487", "targetQuantity": 50, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-17T16:59:31.424796", "status": "active", "mesForwardedCount": 93, "completionRate": 50.6}, {"workOrderNumber": "WO340165", "targetQuantity": 59, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-23T16:59:31.424796", "status": "pending", "mesForwardedCount": 20, "completionRate": 89.8}], "currentWorkOrder": 2, "currentWorkOrderProgress": 22, "displayWorkOrder": {"workOrderNumber": "WO340165", "targetQuantity": 59, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-23T16:59:31.424796", "status": "pending", "mesForwardedCount": 20, "completionRate": 89.8}, "mesErrors": [{"timestamp": "2025-06-13T16:48:31.424796", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050013"}}], "recentLogs": [{"message": "測試條碼-0e736ca7", "timestamp": "2025-06-13T16:12:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-7e5dd0f1", "timestamp": "2025-06-13T16:39:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-4ed8b8ae", "timestamp": "2025-06-13T16:58:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-2a01c45d", "timestamp": "2025-06-13T16:29:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-71adaeb3", "timestamp": "2025-06-13T16:10:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-b53ad61b", "timestamp": "2025-06-13T16:04:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-a1b20d81", "timestamp": "2025-06-13T16:58:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-6b1bab14", "timestamp": "2025-06-13T16:16:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}]}, "S720050014": {"deviceId": "S720050014", "lineName": "A2-01", "sectionName": "ASSEMBLY", "groupName": "TEST", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 559, "forwardedCount": 423, "unforwardedCount": 136, "lastLog": "測試產品條碼-0dda7fe2", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2025-02-26T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO418389", "targetQuantity": 478, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-09T16:59:31.424796", "status": "active", "mesForwardedCount": 11, "completionRate": 84.2}, {"workOrderNumber": "WO913736", "targetQuantity": 206, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-11T16:59:31.424796", "status": "completed", "mesForwardedCount": 27, "completionRate": 83.6}, {"workOrderNumber": "WO124483", "targetQuantity": 356, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-16T16:59:31.424796", "status": "pending", "mesForwardedCount": 21, "completionRate": 98.8}], "currentWorkOrder": 2, "currentWorkOrderProgress": 63, "displayWorkOrder": {"workOrderNumber": "WO124483", "targetQuantity": 356, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-16T16:59:31.424796", "status": "pending", "mesForwardedCount": 21, "completionRate": 98.8}, "mesErrors": [{"timestamp": "2025-06-13T15:35:31.424796", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050014"}}, {"timestamp": "2025-06-12T21:19:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050014"}}, {"timestamp": "2025-06-12T19:03:31.424796", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050014"}}, {"timestamp": "2025-06-13T05:38:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050014"}}, {"timestamp": "2025-06-13T16:22:31.424796", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050014"}}], "recentLogs": [{"message": "測試條碼-64a23c87", "timestamp": "2025-06-13T16:57:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-572c81b5", "timestamp": "2025-06-13T16:00:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-47997494", "timestamp": "2025-06-13T16:03:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-c549a880", "timestamp": "2025-06-13T16:25:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-f3d30d5c", "timestamp": "2025-06-13T16:38:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-c00831b4", "timestamp": "2025-06-13T16:45:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-e63da18a", "timestamp": "2025-06-13T16:01:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-83a56140", "timestamp": "2025-06-13T16:15:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-df173ce6", "timestamp": "2025-06-13T16:42:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-d7929a0d", "timestamp": "2025-06-13T16:17:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-b32e579f", "timestamp": "2025-06-13T16:12:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-32de9210", "timestamp": "2025-06-13T16:40:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-d9c50c36", "timestamp": "2025-06-13T16:06:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-f8234dc5", "timestamp": "2025-06-13T16:33:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-4fc4a953", "timestamp": "2025-06-13T16:46:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-f69ef063", "timestamp": "2025-06-13T16:36:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-841b38db", "timestamp": "2025-06-13T16:34:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}]}, "S720050015": {"deviceId": "S720050015", "lineName": "A2-01", "sectionName": "PACKAGING", "groupName": "INJECTION", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 337, "forwardedCount": 302, "unforwardedCount": 35, "lastLog": "測試產品條碼-2a6e95db", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2024-11-03T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO324564", "targetQuantity": 157, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-18T16:59:31.424796", "status": "active", "mesForwardedCount": 26, "completionRate": 34.7}, {"workOrderNumber": "WO700366", "targetQuantity": 140, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-09T16:59:31.424796", "status": "pending", "mesForwardedCount": 16, "completionRate": 5.1}, {"workOrderNumber": "WO634482", "targetQuantity": 367, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-24T16:59:31.424796", "status": "pending", "mesForwardedCount": 89, "completionRate": 86.5}], "currentWorkOrder": 2, "currentWorkOrderProgress": 7, "displayWorkOrder": {"workOrderNumber": "WO634482", "targetQuantity": 367, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-24T16:59:31.424796", "status": "pending", "mesForwardedCount": 89, "completionRate": 86.5}, "mesErrors": [{"timestamp": "2025-06-12T23:05:31.424796", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050015"}}, {"timestamp": "2025-06-13T04:03:31.424796", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050015"}}, {"timestamp": "2025-06-13T09:57:31.424796", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050015"}}, {"timestamp": "2025-06-13T16:31:31.424796", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050015"}}], "recentLogs": [{"message": "測試條碼-58cced84", "timestamp": "2025-06-13T16:16:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-08e8d39f", "timestamp": "2025-06-13T16:51:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-30db918b", "timestamp": "2025-06-13T16:58:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-8cef77de", "timestamp": "2025-06-13T16:05:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-41b85da4", "timestamp": "2025-06-13T16:03:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-17aa9e6a", "timestamp": "2025-06-13T16:13:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-9e205685", "timestamp": "2025-06-13T16:15:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-0f536dc7", "timestamp": "2025-06-13T16:48:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-fc92be8c", "timestamp": "2025-06-13T16:34:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-3d8935ec", "timestamp": "2025-06-13T16:39:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-0a861e0c", "timestamp": "2025-06-13T16:49:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-97f61f07", "timestamp": "2025-06-13T16:20:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-bf8ff3c8", "timestamp": "2025-06-13T16:23:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}]}, "S720050016": {"deviceId": "S720050016", "lineName": "A1-01", "sectionName": "TESTING", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 125, "forwardedCount": 68, "unforwardedCount": 57, "lastLog": "測試產品條碼-799bcd7d", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2025-03-27T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO707134", "targetQuantity": 230, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-11T16:59:31.424796", "status": "completed", "mesForwardedCount": 80, "completionRate": 88.7}, {"workOrderNumber": "WO356334", "targetQuantity": 269, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-30T16:59:31.424796", "status": "completed", "mesForwardedCount": 89, "completionRate": 43.1}, {"workOrderNumber": "WO504258", "targetQuantity": 146, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-05T16:59:31.424796", "status": "active", "mesForwardedCount": 35, "completionRate": 81.2}], "currentWorkOrder": 1, "currentWorkOrderProgress": 78, "displayWorkOrder": {"workOrderNumber": "WO356334", "targetQuantity": 269, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-30T16:59:31.424796", "status": "completed", "mesForwardedCount": 89, "completionRate": 43.1}, "mesErrors": [{"timestamp": "2025-06-13T03:31:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050016"}}, {"timestamp": "2025-06-13T06:00:31.424796", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050016"}}, {"timestamp": "2025-06-13T06:05:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050016"}}, {"timestamp": "2025-06-13T08:15:31.424796", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050016"}}], "recentLogs": [{"message": "測試條碼-9b123cf3", "timestamp": "2025-06-13T16:19:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-154f1edb", "timestamp": "2025-06-13T16:42:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-c9c972ae", "timestamp": "2025-06-13T16:43:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-0140a35d", "timestamp": "2025-06-13T16:15:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-15fff63f", "timestamp": "2025-06-13T16:33:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-09fb2d0d", "timestamp": "2025-06-13T16:31:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-f6c6a90f", "timestamp": "2025-06-13T16:06:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-27e748e2", "timestamp": "2025-06-13T16:20:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-8b54dd66", "timestamp": "2025-06-13T15:59:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-f7d75b18", "timestamp": "2025-06-13T16:30:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}]}, "S720050017": {"deviceId": "S720050017", "lineName": "B2-02", "sectionName": "INJECTION", "groupName": "TEST", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 998, "forwardedCount": 475, "unforwardedCount": 523, "lastLog": "測試產品條碼-1a50ffec", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2025-02-21T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO987447", "targetQuantity": 114, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-06T16:59:31.424796", "status": "completed", "mesForwardedCount": 26, "completionRate": 15.5}, {"workOrderNumber": "WO368955", "targetQuantity": 155, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-18T16:59:31.424796", "status": "pending", "mesForwardedCount": 54, "completionRate": 7.8}, {"workOrderNumber": "WO128326", "targetQuantity": 353, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-14T16:59:31.424796", "status": "pending", "mesForwardedCount": 15, "completionRate": 0.6}], "currentWorkOrder": 2, "currentWorkOrderProgress": 24, "displayWorkOrder": {"workOrderNumber": "WO128326", "targetQuantity": 353, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-14T16:59:31.424796", "status": "pending", "mesForwardedCount": 15, "completionRate": 0.6}, "mesErrors": [{"timestamp": "2025-06-13T08:43:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050017"}}], "recentLogs": [{"message": "測試條碼-ec1f82ef", "timestamp": "2025-06-13T16:21:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-310cd895", "timestamp": "2025-06-13T16:50:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-dc9840fa", "timestamp": "2025-06-13T16:21:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-4d3e0666", "timestamp": "2025-06-13T16:23:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-7cee324d", "timestamp": "2025-06-13T16:21:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-3efc3504", "timestamp": "2025-06-13T16:05:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-5eb005ff", "timestamp": "2025-06-13T16:52:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-52362957", "timestamp": "2025-06-13T16:04:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-3f5c1b45", "timestamp": "2025-06-13T16:25:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-78562b7f", "timestamp": "2025-06-13T16:32:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-8b7da5ab", "timestamp": "2025-06-13T16:32:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-8e8bb1d1", "timestamp": "2025-06-13T16:16:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-3e0832b2", "timestamp": "2025-06-13T16:29:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-5c9715f7", "timestamp": "2025-06-13T16:29:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-7b43cf77", "timestamp": "2025-06-13T16:35:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-cc53eddf", "timestamp": "2025-06-13T16:46:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d87bcd80", "timestamp": "2025-06-13T16:51:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-2e2946ce", "timestamp": "2025-06-13T16:47:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}]}, "S720050018": {"deviceId": "S720050018", "lineName": "B1-02", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 58, "forwardedCount": 58, "unforwardedCount": 0, "lastLog": "測試產品條碼-a443048b", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2024-09-27T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO682611", "targetQuantity": 417, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-03T16:59:31.424796", "status": "completed", "mesForwardedCount": 77, "completionRate": 1.8}, {"workOrderNumber": "WO304978", "targetQuantity": 437, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-23T16:59:31.424796", "status": "active", "mesForwardedCount": 2, "completionRate": 74.2}, {"workOrderNumber": "WO512520", "targetQuantity": 285, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-26T16:59:31.424796", "status": "active", "mesForwardedCount": 90, "completionRate": 88.7}], "currentWorkOrder": 2, "currentWorkOrderProgress": 90, "displayWorkOrder": {"workOrderNumber": "WO512520", "targetQuantity": 285, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-26T16:59:31.424796", "status": "active", "mesForwardedCount": 90, "completionRate": 88.7}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-658b732f", "timestamp": "2025-06-13T16:46:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-c0005935", "timestamp": "2025-06-13T16:45:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-f66d6e74", "timestamp": "2025-06-13T16:21:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-538f480a", "timestamp": "2025-06-13T16:03:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-fe2f08f1", "timestamp": "2025-06-13T16:30:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-81594090", "timestamp": "2025-06-13T16:09:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-11c59e40", "timestamp": "2025-06-13T16:51:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-59ca655d", "timestamp": "2025-06-13T16:25:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-85213179", "timestamp": "2025-06-13T16:57:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-d6e9b3e8", "timestamp": "2025-06-13T16:42:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}]}, "S720050019": {"deviceId": "S720050019", "lineName": "A1-01", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 405, "forwardedCount": 73, "unforwardedCount": 332, "lastLog": "測試產品條碼-04f69e17", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2024-12-15T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO179164", "targetQuantity": 448, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-08T16:59:31.424796", "status": "completed", "mesForwardedCount": 54, "completionRate": 90.9}, {"workOrderNumber": "WO853146", "targetQuantity": 369, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-18T16:59:31.424796", "status": "completed", "mesForwardedCount": 78, "completionRate": 36.2}, {"workOrderNumber": "WO632990", "targetQuantity": 495, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-29T16:59:31.424796", "status": "active", "mesForwardedCount": 17, "completionRate": 94.8}], "currentWorkOrder": 1, "currentWorkOrderProgress": 97, "displayWorkOrder": {"workOrderNumber": "WO853146", "targetQuantity": 369, "modelName": "4102161402", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-18T16:59:31.424796", "status": "completed", "mesForwardedCount": 78, "completionRate": 36.2}, "mesErrors": [{"timestamp": "2025-06-13T16:01:31.424796", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050019"}}], "recentLogs": [{"message": "測試條碼-3dd1170b", "timestamp": "2025-06-13T16:24:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-c8e6d6b6", "timestamp": "2025-06-13T16:48:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-8a8f84b2", "timestamp": "2025-06-13T16:19:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-c2e1f47e", "timestamp": "2025-06-13T16:41:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-7c1313c9", "timestamp": "2025-06-13T16:12:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-9bd3de88", "timestamp": "2025-06-13T16:01:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ada446c5", "timestamp": "2025-06-13T16:38:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-49eb3253", "timestamp": "2025-06-13T16:20:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-b64bcebf", "timestamp": "2025-06-13T16:27:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-990842c3", "timestamp": "2025-06-13T16:37:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-8d2a7cb7", "timestamp": "2025-06-13T16:06:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-af3be074", "timestamp": "2025-06-13T16:45:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-1b80a2fb", "timestamp": "2025-06-13T16:07:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-2fce6cd8", "timestamp": "2025-06-13T16:25:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-3097a87d", "timestamp": "2025-06-13T16:55:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-f693d5c5", "timestamp": "2025-06-13T15:59:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}]}, "S720050020": {"deviceId": "S720050020", "lineName": "A2-01", "sectionName": "PACKAGING", "groupName": "ASSEMBLY", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 627, "forwardedCount": 580, "unforwardedCount": 47, "lastLog": "測試產品條碼-44aa932c", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2024-11-09T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO723757", "targetQuantity": 157, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-10T16:59:31.424796", "status": "pending", "mesForwardedCount": 2, "completionRate": 91.9}, {"workOrderNumber": "WO800944", "targetQuantity": 364, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-12T16:59:31.424796", "status": "completed", "mesForwardedCount": 51, "completionRate": 30.0}, {"workOrderNumber": "WO486088", "targetQuantity": 169, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-04T16:59:31.424796", "status": "completed", "mesForwardedCount": 24, "completionRate": 33.7}], "currentWorkOrder": 2, "currentWorkOrderProgress": 83, "displayWorkOrder": {"workOrderNumber": "WO486088", "targetQuantity": 169, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-04T16:59:31.424796", "status": "completed", "mesForwardedCount": 24, "completionRate": 33.7}, "mesErrors": [{"timestamp": "2025-06-13T04:51:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050020"}}, {"timestamp": "2025-06-12T20:17:31.424796", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050020"}}, {"timestamp": "2025-06-13T01:31:31.424796", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050020"}}, {"timestamp": "2025-06-13T00:12:31.424796", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050020"}}], "recentLogs": [{"message": "測試條碼-45d292c7", "timestamp": "2025-06-13T16:29:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-5a92e75a", "timestamp": "2025-06-13T16:44:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-88eda02f", "timestamp": "2025-06-13T16:58:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-1dbf084a", "timestamp": "2025-06-13T16:17:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-dc2cf069", "timestamp": "2025-06-13T16:19:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-637ddbf4", "timestamp": "2025-06-13T16:35:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}]}, "S720050021": {"deviceId": "S720050021", "lineName": "B1-02", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 377, "forwardedCount": 164, "unforwardedCount": 213, "lastLog": "測試產品條碼-720792c0", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2024-08-16T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO238892", "targetQuantity": 477, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-11T16:59:31.424796", "status": "active", "mesForwardedCount": 10, "completionRate": 94.7}, {"workOrderNumber": "WO418626", "targetQuantity": 295, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-03T16:59:31.424796", "status": "active", "mesForwardedCount": 75, "completionRate": 13.6}, {"workOrderNumber": "WO813982", "targetQuantity": 68, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-18T16:59:31.424796", "status": "active", "mesForwardedCount": 31, "completionRate": 10.4}], "currentWorkOrder": 1, "currentWorkOrderProgress": 29, "displayWorkOrder": {"workOrderNumber": "WO418626", "targetQuantity": 295, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-03T16:59:31.424796", "status": "active", "mesForwardedCount": 75, "completionRate": 13.6}, "mesErrors": [{"timestamp": "2025-06-13T14:39:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050021"}}, {"timestamp": "2025-06-13T03:33:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050021"}}, {"timestamp": "2025-06-13T10:20:31.424796", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050021"}}, {"timestamp": "2025-06-13T05:45:31.424796", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050021"}}], "recentLogs": [{"message": "測試條碼-bda26abe", "timestamp": "2025-06-13T16:51:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-ac3b06c1", "timestamp": "2025-06-13T16:37:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-c080759d", "timestamp": "2025-06-13T16:16:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-7faa1a4e", "timestamp": "2025-06-13T16:28:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-64edf494", "timestamp": "2025-06-13T16:39:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-ac25dc55", "timestamp": "2025-06-13T16:25:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-d4671da3", "timestamp": "2025-06-13T16:34:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-09724a31", "timestamp": "2025-06-13T16:51:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-2f2e6121", "timestamp": "2025-06-13T16:41:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-a8f6376b", "timestamp": "2025-06-13T16:51:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-974d125e", "timestamp": "2025-06-13T16:14:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-a47a32bb", "timestamp": "2025-06-13T16:44:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-cfb811f4", "timestamp": "2025-06-13T16:19:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-360216f0", "timestamp": "2025-06-13T16:48:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-87610786", "timestamp": "2025-06-13T16:15:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-af616827", "timestamp": "2025-06-13T16:34:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-72e105ca", "timestamp": "2025-06-13T16:35:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-0ed76dce", "timestamp": "2025-06-13T16:57:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-50d21986", "timestamp": "2025-06-13T16:13:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}]}, "S720050022": {"deviceId": "S720050022", "lineName": "A1-01", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 840, "forwardedCount": 244, "unforwardedCount": 596, "lastLog": "測試產品條碼-ba234acd", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2024-09-26T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO329915", "targetQuantity": 68, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-30T16:59:31.424796", "status": "completed", "mesForwardedCount": 72, "completionRate": 73.8}, {"workOrderNumber": "WO855972", "targetQuantity": 163, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-06T16:59:31.424796", "status": "active", "mesForwardedCount": 41, "completionRate": 30.0}, {"workOrderNumber": "WO102343", "targetQuantity": 439, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-09T16:59:31.424796", "status": "active", "mesForwardedCount": 82, "completionRate": 9.5}], "currentWorkOrder": 2, "currentWorkOrderProgress": 54, "displayWorkOrder": {"workOrderNumber": "WO102343", "targetQuantity": 439, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-09T16:59:31.424796", "status": "active", "mesForwardedCount": 82, "completionRate": 9.5}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-27ee37c4", "timestamp": "2025-06-13T16:20:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-2e686439", "timestamp": "2025-06-13T16:47:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-ddf7cd9f", "timestamp": "2025-06-13T16:33:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-d174ea5a", "timestamp": "2025-06-13T16:54:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-1e67e32b", "timestamp": "2025-06-13T16:55:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-6454b516", "timestamp": "2025-06-13T16:28:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-b94e9220", "timestamp": "2025-06-13T16:21:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-ea1a78d1", "timestamp": "2025-06-13T16:17:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-2661699d", "timestamp": "2025-06-13T16:33:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a76f7da7", "timestamp": "2025-06-13T16:22:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-f5a0b40d", "timestamp": "2025-06-13T16:14:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-45f9c1ed", "timestamp": "2025-06-13T16:36:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f95bd620", "timestamp": "2025-06-13T16:53:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-4e2478f5", "timestamp": "2025-06-13T16:05:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-d139426e", "timestamp": "2025-06-13T16:39:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-aa557f34", "timestamp": "2025-06-13T16:21:31.424796", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}]}, "S720050023": {"deviceId": "S720050023", "lineName": "A1-02", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 885, "forwardedCount": 362, "unforwardedCount": 523, "lastLog": "測試產品條碼-29cf07f3", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2024-08-16T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO947032", "targetQuantity": 358, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-25T16:59:31.424796", "status": "pending", "mesForwardedCount": 56, "completionRate": 6.8}, {"workOrderNumber": "WO934023", "targetQuantity": 140, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-16T16:59:31.424796", "status": "completed", "mesForwardedCount": 4, "completionRate": 66.4}, {"workOrderNumber": "WO705868", "targetQuantity": 216, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-28T16:59:31.424796", "status": "active", "mesForwardedCount": 13, "completionRate": 90.2}], "currentWorkOrder": 2, "currentWorkOrderProgress": 95, "displayWorkOrder": {"workOrderNumber": "WO705868", "targetQuantity": 216, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-28T16:59:31.424796", "status": "active", "mesForwardedCount": 13, "completionRate": 90.2}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-f45982fe", "timestamp": "2025-06-13T16:25:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-769a7806", "timestamp": "2025-06-13T16:05:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1b9a989e", "timestamp": "2025-06-13T16:30:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-a5c53379", "timestamp": "2025-06-13T16:46:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-9dfb5e91", "timestamp": "2025-06-13T16:31:31.424796", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-60d5b832", "timestamp": "2025-06-13T16:49:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-fc4392dc", "timestamp": "2025-06-13T16:30:31.424796", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-3239e5af", "timestamp": "2025-06-13T16:23:31.424796", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}]}, "S720050024": {"deviceId": "S720050024", "lineName": "A1-02", "sectionName": "ASSEMBLY", "groupName": "TEST", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 411, "forwardedCount": 228, "unforwardedCount": 183, "lastLog": "測試產品條碼-45d6396a", "lastUpdateTime": "2025-06-13T16:59:31.424796", "createdAt": "2025-03-21T16:59:31.424796", "workOrders": [{"workOrderNumber": "WO332934", "targetQuantity": 242, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-21T16:59:31.424796", "status": "completed", "mesForwardedCount": 77, "completionRate": 51.7}, {"workOrderNumber": "WO497242", "targetQuantity": 106, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-01T16:59:31.424796", "status": "active", "mesForwardedCount": 32, "completionRate": 13.7}, {"workOrderNumber": "WO966379", "targetQuantity": 428, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-06T16:59:31.424796", "status": "completed", "mesForwardedCount": 77, "completionRate": 23.0}], "currentWorkOrder": 1, "currentWorkOrderProgress": 63, "displayWorkOrder": {"workOrderNumber": "WO497242", "targetQuantity": 106, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-01T16:59:31.424796", "status": "active", "mesForwardedCount": 32, "completionRate": 13.7}, "mesErrors": [{"timestamp": "2025-06-12T18:43:31.424796", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050024"}}, {"timestamp": "2025-06-13T13:07:31.424796", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050024"}}, {"timestamp": "2025-06-13T03:20:31.424796", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050024"}}, {"timestamp": "2025-06-13T11:40:31.424796", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050024"}}], "recentLogs": [{"message": "測試條碼-0695fa1e", "timestamp": "2025-06-13T16:50:31.440274", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-3a83edf2", "timestamp": "2025-06-13T16:14:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-21db7292", "timestamp": "2025-06-13T16:27:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-794222b8", "timestamp": "2025-06-13T16:39:31.440274", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-d066475c", "timestamp": "2025-06-13T16:38:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-d281b8f2", "timestamp": "2025-06-13T16:54:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-7a9ea9d1", "timestamp": "2025-06-13T16:28:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-511bff33", "timestamp": "2025-06-13T16:46:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-c4bc6134", "timestamp": "2025-06-13T16:50:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-ed703098", "timestamp": "2025-06-13T16:11:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-4503d502", "timestamp": "2025-06-13T16:46:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-defd6a6f", "timestamp": "2025-06-13T16:47:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-bfa0a813", "timestamp": "2025-06-13T16:19:31.440274", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}]}, "S720050025": {"deviceId": "S720050025", "lineName": "A1-02", "sectionName": "INJECTION", "groupName": "INJECTION", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 622, "forwardedCount": 421, "unforwardedCount": 201, "lastLog": "測試產品條碼-502138f6", "lastUpdateTime": "2025-06-13T16:59:31.440274", "createdAt": "2025-03-27T16:59:31.440274", "workOrders": [{"workOrderNumber": "WO399181", "targetQuantity": 104, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-09T16:59:31.440274", "status": "pending", "mesForwardedCount": 98, "completionRate": 19.9}, {"workOrderNumber": "WO413481", "targetQuantity": 349, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-26T16:59:31.440274", "status": "active", "mesForwardedCount": 67, "completionRate": 22.9}, {"workOrderNumber": "WO304099", "targetQuantity": 409, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-17T16:59:31.440274", "status": "pending", "mesForwardedCount": 76, "completionRate": 35.7}], "currentWorkOrder": 1, "currentWorkOrderProgress": 89, "displayWorkOrder": {"workOrderNumber": "WO413481", "targetQuantity": 349, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-26T16:59:31.440274", "status": "active", "mesForwardedCount": 67, "completionRate": 22.9}, "mesErrors": [{"timestamp": "2025-06-13T06:09:31.440274", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050025"}}, {"timestamp": "2025-06-13T06:05:31.440274", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050025"}}], "recentLogs": [{"message": "測試條碼-33ca67e2", "timestamp": "2025-06-13T16:01:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-47725aca", "timestamp": "2025-06-13T16:00:31.440274", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-212408f5", "timestamp": "2025-06-13T16:03:31.440274", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-202f4359", "timestamp": "2025-06-13T16:38:31.440274", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-c5448762", "timestamp": "2025-06-13T16:02:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-660e91bd", "timestamp": "2025-06-13T16:17:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-7cce69c1", "timestamp": "2025-06-13T16:40:31.440274", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}]}, "S720050026": {"deviceId": "S720050026", "lineName": "B2-01", "sectionName": "PACKAGING", "groupName": "ASSEMBLY", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 391, "forwardedCount": 224, "unforwardedCount": 167, "lastLog": "測試產品條碼-122e0b04", "lastUpdateTime": "2025-06-13T16:59:31.440274", "createdAt": "2025-06-01T16:59:31.440274", "workOrders": [{"workOrderNumber": "WO527404", "targetQuantity": 336, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-15T16:59:31.440274", "status": "pending", "mesForwardedCount": 41, "completionRate": 19.4}, {"workOrderNumber": "WO401470", "targetQuantity": 299, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-21T16:59:31.440274", "status": "active", "mesForwardedCount": 53, "completionRate": 78.1}, {"workOrderNumber": "WO881738", "targetQuantity": 238, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-11T16:59:31.440274", "status": "completed", "mesForwardedCount": 95, "completionRate": 76.3}], "currentWorkOrder": 1, "currentWorkOrderProgress": 21, "displayWorkOrder": {"workOrderNumber": "WO401470", "targetQuantity": 299, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-21T16:59:31.440274", "status": "active", "mesForwardedCount": 53, "completionRate": 78.1}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-69966971", "timestamp": "2025-06-13T16:33:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-720cf39e", "timestamp": "2025-06-13T16:23:31.440274", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-6a825021", "timestamp": "2025-06-13T16:44:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-aca267b3", "timestamp": "2025-06-13T16:55:31.440274", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-e10d93e7", "timestamp": "2025-06-13T16:24:31.440274", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-67de8b7b", "timestamp": "2025-06-13T16:55:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-e23e2d84", "timestamp": "2025-06-13T16:23:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-f9ed6b07", "timestamp": "2025-06-13T16:58:31.440274", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-4d353816", "timestamp": "2025-06-13T16:52:31.440274", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-af8a460f", "timestamp": "2025-06-13T16:11:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-ebd403a7", "timestamp": "2025-06-13T16:39:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}]}, "S720050027": {"deviceId": "S720050027", "lineName": "A1-01", "sectionName": "PACKAGING", "groupName": "TEST", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 85, "forwardedCount": 17, "unforwardedCount": 68, "lastLog": "測試產品條碼-bdd37df3", "lastUpdateTime": "2025-06-13T16:59:31.440274", "createdAt": "2024-10-07T16:59:31.440274", "workOrders": [{"workOrderNumber": "WO720317", "targetQuantity": 140, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-17T16:59:31.440274", "status": "active", "mesForwardedCount": 71, "completionRate": 91.1}, {"workOrderNumber": "WO109715", "targetQuantity": 152, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-25T16:59:31.440274", "status": "active", "mesForwardedCount": 16, "completionRate": 92.6}, {"workOrderNumber": "WO179717", "targetQuantity": 56, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-17T16:59:31.440274", "status": "completed", "mesForwardedCount": 6, "completionRate": 83.5}], "currentWorkOrder": 0, "currentWorkOrderProgress": 47, "displayWorkOrder": {"workOrderNumber": "WO720317", "targetQuantity": 140, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-17T16:59:31.440274", "status": "active", "mesForwardedCount": 71, "completionRate": 91.1}, "mesErrors": [{"timestamp": "2025-06-12T20:15:31.440274", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050027"}}, {"timestamp": "2025-06-12T23:34:31.440274", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050027"}}, {"timestamp": "2025-06-13T07:09:31.440274", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050027"}}, {"timestamp": "2025-06-13T03:32:31.440274", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050027"}}], "recentLogs": [{"message": "測試條碼-c5091f34", "timestamp": "2025-06-13T16:56:31.440274", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-305598d0", "timestamp": "2025-06-13T16:11:31.440274", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-dcfe0af7", "timestamp": "2025-06-13T16:57:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-2ff596f1", "timestamp": "2025-06-13T16:20:31.440274", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-4b42e13a", "timestamp": "2025-06-13T16:00:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-24fb4aec", "timestamp": "2025-06-13T16:23:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-7ffa9dcc", "timestamp": "2025-06-13T16:11:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-744720a6", "timestamp": "2025-06-13T16:50:31.440274", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-64b42888", "timestamp": "2025-06-13T16:35:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-e56366e6", "timestamp": "2025-06-13T16:16:31.440274", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-85750a0a", "timestamp": "2025-06-13T16:15:31.440274", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ac8e3ac8", "timestamp": "2025-06-13T16:33:31.440274", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-b44d8d22", "timestamp": "2025-06-13T16:55:31.440274", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-b49a2dc8", "timestamp": "2025-06-13T16:47:31.440274", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-697101ea", "timestamp": "2025-06-13T16:44:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-d92d1adc", "timestamp": "2025-06-13T16:14:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-86ddc221", "timestamp": "2025-06-13T16:26:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}]}, "S720050028": {"deviceId": "S720050028", "lineName": "B1-01", "sectionName": "PACKAGING", "groupName": "ASSEMBLY", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 333, "forwardedCount": 312, "unforwardedCount": 21, "lastLog": "測試產品條碼-55d73464", "lastUpdateTime": "2025-06-13T16:59:31.440274", "createdAt": "2025-02-20T16:59:31.440274", "workOrders": [{"workOrderNumber": "WO626406", "targetQuantity": 57, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-25T16:59:31.440274", "status": "active", "mesForwardedCount": 78, "completionRate": 35.0}, {"workOrderNumber": "WO292385", "targetQuantity": 328, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-02T16:59:31.440274", "status": "completed", "mesForwardedCount": 7, "completionRate": 64.6}, {"workOrderNumber": "WO313975", "targetQuantity": 160, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-23T16:59:31.440274", "status": "completed", "mesForwardedCount": 8, "completionRate": 24.1}], "currentWorkOrder": 0, "currentWorkOrderProgress": 3, "displayWorkOrder": {"workOrderNumber": "WO626406", "targetQuantity": 57, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-25T16:59:31.440274", "status": "active", "mesForwardedCount": 78, "completionRate": 35.0}, "mesErrors": [{"timestamp": "2025-06-12T17:35:31.440274", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050028"}}, {"timestamp": "2025-06-13T06:06:31.440274", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050028"}}, {"timestamp": "2025-06-13T00:20:31.440274", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050028"}}, {"timestamp": "2025-06-13T12:54:31.440274", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050028"}}, {"timestamp": "2025-06-13T16:08:31.440274", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050028"}}], "recentLogs": [{"message": "測試條碼-2a5845a9", "timestamp": "2025-06-13T16:29:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-b6b0cfa0", "timestamp": "2025-06-13T16:27:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-32a8f5e1", "timestamp": "2025-06-13T16:02:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-f5468d6c", "timestamp": "2025-06-13T16:45:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-68d10156", "timestamp": "2025-06-13T16:27:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-26b1d8ea", "timestamp": "2025-06-13T16:07:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-8d0c56b3", "timestamp": "2025-06-13T16:10:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-0a5b25b9", "timestamp": "2025-06-13T16:50:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-5d884f72", "timestamp": "2025-06-13T16:19:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-86d2b0f8", "timestamp": "2025-06-13T16:48:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-1d111691", "timestamp": "2025-06-13T16:33:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-620ed921", "timestamp": "2025-06-13T16:06:31.440274", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-f15cf62d", "timestamp": "2025-06-13T16:58:31.440274", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-3afb2f1d", "timestamp": "2025-06-13T16:16:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-da9c518c", "timestamp": "2025-06-13T16:33:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-7695f5a7", "timestamp": "2025-06-13T16:41:31.440274", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-3baf23d3", "timestamp": "2025-06-13T16:18:31.440274", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-9615beca", "timestamp": "2025-06-13T16:41:31.440274", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}]}, "S720050029": {"deviceId": "S720050029", "lineName": "A1-02", "sectionName": "ASSEMBLY", "groupName": "INJECTION", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 126, "forwardedCount": 19, "unforwardedCount": 107, "lastLog": "測試產品條碼-42902842", "lastUpdateTime": "2025-06-13T16:59:31.441401", "createdAt": "2025-02-08T16:59:31.441401", "workOrders": [{"workOrderNumber": "WO402955", "targetQuantity": 494, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-09T16:59:31.441401", "status": "pending", "mesForwardedCount": 54, "completionRate": 18.8}, {"workOrderNumber": "WO667108", "targetQuantity": 164, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-31T16:59:31.441401", "status": "active", "mesForwardedCount": 63, "completionRate": 84.6}, {"workOrderNumber": "WO291787", "targetQuantity": 126, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-30T16:59:31.441401", "status": "pending", "mesForwardedCount": 17, "completionRate": 89.9}], "currentWorkOrder": 2, "currentWorkOrderProgress": 16, "displayWorkOrder": {"workOrderNumber": "WO291787", "targetQuantity": 126, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-30T16:59:31.441401", "status": "pending", "mesForwardedCount": 17, "completionRate": 89.9}, "mesErrors": [{"timestamp": "2025-06-13T02:25:31.441401", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050029"}}, {"timestamp": "2025-06-12T19:50:31.441401", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050029"}}, {"timestamp": "2025-06-13T11:44:31.441401", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050029"}}], "recentLogs": [{"message": "測試條碼-2161f88f", "timestamp": "2025-06-13T16:08:31.441401", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-2453e837", "timestamp": "2025-06-13T16:25:31.441401", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-9d3f9647", "timestamp": "2025-06-13T16:40:31.441401", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-554c27ca", "timestamp": "2025-06-13T16:08:31.441401", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-664a1bf0", "timestamp": "2025-06-13T16:25:31.441401", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-d0b1d168", "timestamp": "2025-06-13T16:16:31.441401", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-114938d2", "timestamp": "2025-06-13T16:16:31.441526", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-96d29e82", "timestamp": "2025-06-13T16:21:31.441526", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-dcb6efc9", "timestamp": "2025-06-13T16:32:31.441526", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}]}, "S720050030": {"deviceId": "S720050030", "lineName": "A1-02", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 228, "forwardedCount": 117, "unforwardedCount": 111, "lastLog": "測試產品條碼-a5f791b6", "lastUpdateTime": "2025-06-13T16:59:31.441526", "createdAt": "2025-06-12T16:59:31.441526", "workOrders": [{"workOrderNumber": "WO132227", "targetQuantity": 305, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-06-12T16:59:31.441526", "status": "pending", "mesForwardedCount": 66, "completionRate": 40.7}, {"workOrderNumber": "WO795633", "targetQuantity": 368, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-14T16:59:31.441526", "status": "completed", "mesForwardedCount": 65, "completionRate": 79.6}, {"workOrderNumber": "WO235724", "targetQuantity": 129, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-06-08T16:59:31.441526", "status": "pending", "mesForwardedCount": 93, "completionRate": 56.4}], "currentWorkOrder": 1, "currentWorkOrderProgress": 23, "displayWorkOrder": {"workOrderNumber": "WO795633", "targetQuantity": 368, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-05-14T16:59:31.441526", "status": "completed", "mesForwardedCount": 65, "completionRate": 79.6}, "mesErrors": [{"timestamp": "2025-06-13T01:08:31.441526", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050030"}}, {"timestamp": "2025-06-12T21:19:31.441526", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050030"}}, {"timestamp": "2025-06-13T00:51:31.441526", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050030"}}, {"timestamp": "2025-06-13T01:52:31.441526", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050030"}}, {"timestamp": "2025-06-13T01:22:31.441526", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050030"}}], "recentLogs": [{"message": "測試條碼-3b91853e", "timestamp": "2025-06-13T16:55:31.441526", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-221cc9d3", "timestamp": "2025-06-13T16:08:31.441526", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-0f7155f7", "timestamp": "2025-06-13T16:31:31.441526", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-13266fb2", "timestamp": "2025-06-13T16:10:31.441526", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-3e616306", "timestamp": "2025-06-13T16:18:31.441526", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-c750290d", "timestamp": "2025-06-13T16:14:31.441526", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-abf093a7", "timestamp": "2025-06-13T16:57:31.441526", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-ea20ee1c", "timestamp": "2025-06-13T16:51:31.441526", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-10f32b7b", "timestamp": "2025-06-13T16:10:31.441526", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-0d9c7de9", "timestamp": "2025-06-13T16:02:31.441526", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-0ec7e433", "timestamp": "2025-06-13T16:46:31.441526", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}]}, "S720050031": {"deviceId": "S720050031", "lineName": "A2-01", "sectionName": "INJECTION", "groupName": "INJECTION", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 648, "forwardedCount": 609, "unforwardedCount": 39, "lastLog": "測試產品條碼-c82b656c", "lastUpdateTime": "2025-06-13T16:59:31.441526", "createdAt": "2024-07-04T16:59:31.441526", "workOrders": [{"workOrderNumber": "WO248185", "targetQuantity": 211, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-28T16:59:31.441526", "status": "pending", "mesForwardedCount": 43, "completionRate": 70.2}, {"workOrderNumber": "WO738790", "targetQuantity": 209, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-12T16:59:31.441526", "status": "pending", "mesForwardedCount": 24, "completionRate": 84.8}, {"workOrderNumber": "WO881618", "targetQuantity": 123, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-07T16:59:31.441526", "status": "active", "mesForwardedCount": 35, "completionRate": 63.3}], "currentWorkOrder": 0, "currentWorkOrderProgress": 12, "displayWorkOrder": {"workOrderNumber": "WO248185", "targetQuantity": 211, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-28T16:59:31.441526", "status": "pending", "mesForwardedCount": 43, "completionRate": 70.2}, "mesErrors": [{"timestamp": "2025-06-12T22:09:31.441526", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050031"}}, {"timestamp": "2025-06-12T21:18:31.441526", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050031"}}, {"timestamp": "2025-06-13T15:44:31.441526", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050031"}}], "recentLogs": [{"message": "測試條碼-db96e088", "timestamp": "2025-06-13T15:59:31.441526", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-880f896d", "timestamp": "2025-06-13T16:52:31.441526", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-40576440", "timestamp": "2025-06-13T16:52:31.441526", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-8ae46829", "timestamp": "2025-06-13T16:58:31.441526", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a8dc3928", "timestamp": "2025-06-13T16:40:31.441526", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}]}, "S720050032": {"deviceId": "S720050032", "lineName": "B1-02", "sectionName": "INJECTION", "groupName": "ASSEMBLY", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 537, "forwardedCount": 87, "unforwardedCount": 450, "lastLog": "測試產品條碼-63f4d4e0", "lastUpdateTime": "2025-06-13T16:59:31.441526", "createdAt": "2025-05-26T16:59:31.441526", "workOrders": [{"workOrderNumber": "WO331477", "targetQuantity": 161, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-04T16:59:31.441526", "status": "pending", "mesForwardedCount": 50, "completionRate": 97.7}, {"workOrderNumber": "WO104377", "targetQuantity": 274, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-13T16:59:31.441526", "status": "active", "mesForwardedCount": 7, "completionRate": 10.9}, {"workOrderNumber": "WO172105", "targetQuantity": 159, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-31T16:59:31.441526", "status": "pending", "mesForwardedCount": 92, "completionRate": 28.5}], "currentWorkOrder": 1, "currentWorkOrderProgress": 72, "displayWorkOrder": {"workOrderNumber": "WO104377", "targetQuantity": 274, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-13T16:59:31.441526", "status": "active", "mesForwardedCount": 7, "completionRate": 10.9}, "mesErrors": [{"timestamp": "2025-06-12T23:24:31.441526", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050032"}}, {"timestamp": "2025-06-13T00:27:31.441526", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050032"}}, {"timestamp": "2025-06-13T16:41:31.441526", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050032"}}], "recentLogs": [{"message": "測試條碼-3f7b59c1", "timestamp": "2025-06-13T16:25:31.441526", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-821eed32", "timestamp": "2025-06-13T16:48:31.441526", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-3951abab", "timestamp": "2025-06-13T16:02:31.441526", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-c83192f5", "timestamp": "2025-06-13T16:16:31.441526", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-f42b9adb", "timestamp": "2025-06-13T16:23:31.441526", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-151714fc", "timestamp": "2025-06-13T16:38:31.441526", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-55437bc7", "timestamp": "2025-06-13T16:18:31.441526", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-1a292b08", "timestamp": "2025-06-13T16:53:31.441526", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-472742c2", "timestamp": "2025-06-13T16:32:31.441526", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-9beedcc4", "timestamp": "2025-06-13T16:36:31.441526", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-1fbcb80f", "timestamp": "2025-06-13T16:24:31.441526", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-5b5e116e", "timestamp": "2025-06-13T16:39:31.441526", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-7f4212ad", "timestamp": "2025-06-13T16:22:31.441526", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-cfd4d465", "timestamp": "2025-06-13T16:09:31.441526", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-98028887", "timestamp": "2025-06-13T16:47:31.441526", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-bfb7f381", "timestamp": "2025-06-13T16:44:31.441526", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}]}, "S720050033": {"deviceId": "S720050033", "lineName": "A1-02", "sectionName": "INJECTION", "groupName": "ASSEMBLY", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 487, "forwardedCount": 260, "unforwardedCount": 227, "lastLog": "測試產品條碼-7f1eaa51", "lastUpdateTime": "2025-06-13T16:59:31.442140", "createdAt": "2025-03-23T16:59:31.442140", "workOrders": [{"workOrderNumber": "WO443662", "targetQuantity": 459, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-06T16:59:31.441526", "status": "pending", "mesForwardedCount": 26, "completionRate": 60.0}, {"workOrderNumber": "WO757313", "targetQuantity": 130, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-07T16:59:31.441526", "status": "active", "mesForwardedCount": 59, "completionRate": 4.8}, {"workOrderNumber": "WO302776", "targetQuantity": 243, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-06T16:59:31.441526", "status": "pending", "mesForwardedCount": 31, "completionRate": 96.5}], "currentWorkOrder": 2, "currentWorkOrderProgress": 20, "displayWorkOrder": {"workOrderNumber": "WO302776", "targetQuantity": 243, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-06T16:59:31.441526", "status": "pending", "mesForwardedCount": 31, "completionRate": 96.5}, "mesErrors": [{"timestamp": "2025-06-13T16:17:31.442140", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050033"}}], "recentLogs": [{"message": "測試條碼-cce9bbb3", "timestamp": "2025-06-13T16:00:31.442140", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-49ab1909", "timestamp": "2025-06-13T16:13:31.442140", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d965b7f1", "timestamp": "2025-06-13T16:50:31.442140", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-0d1957dd", "timestamp": "2025-06-13T16:46:31.442140", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-d796f766", "timestamp": "2025-06-13T16:33:31.442140", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-76e5ee64", "timestamp": "2025-06-13T16:55:31.442140", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-4f946477", "timestamp": "2025-06-13T16:06:31.442140", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-8c93ba32", "timestamp": "2025-06-13T16:35:31.442140", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-9d83fe22", "timestamp": "2025-06-13T16:31:31.442140", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-0520fe7b", "timestamp": "2025-06-13T16:18:31.442140", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-43367d0a", "timestamp": "2025-06-13T16:16:31.442140", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-56b7c6b8", "timestamp": "2025-06-13T16:55:31.442140", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-18ba9d41", "timestamp": "2025-06-13T16:20:31.442140", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-0dd3e4cc", "timestamp": "2025-06-13T16:10:31.442140", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-bc1506af", "timestamp": "2025-06-13T16:27:31.442140", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-d4133c61", "timestamp": "2025-06-13T16:56:31.442292", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-dcc3138e", "timestamp": "2025-06-13T15:59:31.442292", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-bf383739", "timestamp": "2025-06-13T16:42:31.442292", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}]}, "S720050034": {"deviceId": "S720050034", "lineName": "A1-01", "sectionName": "INJECTION", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 820, "forwardedCount": 368, "unforwardedCount": 452, "lastLog": "測試產品條碼-b2282b23", "lastUpdateTime": "2025-06-13T16:59:31.442292", "createdAt": "2025-06-08T16:59:31.442292", "workOrders": [{"workOrderNumber": "WO626948", "targetQuantity": 269, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-15T16:59:31.442292", "status": "active", "mesForwardedCount": 25, "completionRate": 10.8}, {"workOrderNumber": "WO217764", "targetQuantity": 350, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-06-10T16:59:31.442292", "status": "pending", "mesForwardedCount": 18, "completionRate": 95.7}, {"workOrderNumber": "WO588307", "targetQuantity": 120, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-26T16:59:31.442292", "status": "completed", "mesForwardedCount": 62, "completionRate": 98.8}], "currentWorkOrder": 2, "currentWorkOrderProgress": 78, "displayWorkOrder": {"workOrderNumber": "WO588307", "targetQuantity": 120, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-26T16:59:31.442292", "status": "completed", "mesForwardedCount": 62, "completionRate": 98.8}, "mesErrors": [{"timestamp": "2025-06-13T02:10:31.442292", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050034"}}], "recentLogs": [{"message": "測試條碼-ba670590", "timestamp": "2025-06-13T16:32:31.442292", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-48a946e6", "timestamp": "2025-06-13T16:41:31.442292", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-35cb00cb", "timestamp": "2025-06-13T16:01:31.442292", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-e8bb5e23", "timestamp": "2025-06-13T16:43:31.442292", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-cef3cb3d", "timestamp": "2025-06-13T15:59:31.442292", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-551c474b", "timestamp": "2025-06-13T16:37:31.442292", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-7058ccd1", "timestamp": "2025-06-13T16:32:31.442292", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-7422ab63", "timestamp": "2025-06-13T16:58:31.442292", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-1b1bf2c5", "timestamp": "2025-06-13T16:41:31.442292", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-7c4fa905", "timestamp": "2025-06-13T16:27:31.442292", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-fe56a3f8", "timestamp": "2025-06-13T16:27:31.442292", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-a7bfb83a", "timestamp": "2025-06-13T16:44:31.442292", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-d025d402", "timestamp": "2025-06-13T16:11:31.442292", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-d730d3e9", "timestamp": "2025-06-13T16:01:31.442292", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}]}, "S720050035": {"deviceId": "S720050035", "lineName": "B1-02", "sectionName": "TESTING", "groupName": "TEST", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 91, "forwardedCount": 3, "unforwardedCount": 88, "lastLog": "測試產品條碼-3214d477", "lastUpdateTime": "2025-06-13T16:59:31.442292", "createdAt": "2024-12-05T16:59:31.442292", "workOrders": [{"workOrderNumber": "WO958690", "targetQuantity": 361, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-19T16:59:31.442292", "status": "active", "mesForwardedCount": 44, "completionRate": 23.3}, {"workOrderNumber": "WO325081", "targetQuantity": 483, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-19T16:59:31.442292", "status": "active", "mesForwardedCount": 100, "completionRate": 83.1}, {"workOrderNumber": "WO527269", "targetQuantity": 124, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-31T16:59:31.442292", "status": "active", "mesForwardedCount": 10, "completionRate": 70.1}], "currentWorkOrder": 1, "currentWorkOrderProgress": 15, "displayWorkOrder": {"workOrderNumber": "WO325081", "targetQuantity": 483, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-19T16:59:31.442292", "status": "active", "mesForwardedCount": 100, "completionRate": 83.1}, "mesErrors": [{"timestamp": "2025-06-12T17:38:31.442292", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050035"}}, {"timestamp": "2025-06-13T13:02:31.442292", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050035"}}, {"timestamp": "2025-06-12T20:11:31.442292", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050035"}}, {"timestamp": "2025-06-13T06:42:31.442292", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050035"}}], "recentLogs": [{"message": "測試條碼-c867e557", "timestamp": "2025-06-13T16:12:31.442292", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-cf036914", "timestamp": "2025-06-13T15:59:31.442292", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-92d787df", "timestamp": "2025-06-13T16:00:31.442292", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-290846b3", "timestamp": "2025-06-13T16:32:31.442292", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-d94bdd78", "timestamp": "2025-06-13T16:20:31.442292", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-afb5fdd3", "timestamp": "2025-06-13T16:28:31.442292", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-6dcbf997", "timestamp": "2025-06-13T16:46:31.442292", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-a4292d63", "timestamp": "2025-06-13T16:28:31.442292", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-7e525b02", "timestamp": "2025-06-13T16:45:31.442292", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-1c4cdbe7", "timestamp": "2025-06-13T16:06:31.442292", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-c5fa19d4", "timestamp": "2025-06-13T16:27:31.442292", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-1da34272", "timestamp": "2025-06-13T16:51:31.442292", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-1dcf8987", "timestamp": "2025-06-13T16:50:31.442292", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-30ef85f0", "timestamp": "2025-06-13T16:57:31.442292", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-34266abe", "timestamp": "2025-06-13T16:03:31.442292", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-2fa558dd", "timestamp": "2025-06-13T16:23:31.442292", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}]}, "S720050036": {"deviceId": "S720050036", "lineName": "A2-02", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 99, "forwardedCount": 20, "unforwardedCount": 79, "lastLog": "測試產品條碼-7f58e95b", "lastUpdateTime": "2025-06-13T16:59:31.442292", "createdAt": "2024-12-01T16:59:31.442292", "workOrders": [{"workOrderNumber": "WO861118", "targetQuantity": 99, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-07T16:59:31.442292", "status": "active", "mesForwardedCount": 82, "completionRate": 78.8}, {"workOrderNumber": "WO506460", "targetQuantity": 202, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-14T16:59:31.442292", "status": "pending", "mesForwardedCount": 8, "completionRate": 5.5}, {"workOrderNumber": "WO427295", "targetQuantity": 108, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-23T16:59:31.442292", "status": "active", "mesForwardedCount": 42, "completionRate": 17.4}], "currentWorkOrder": 0, "currentWorkOrderProgress": 78, "displayWorkOrder": {"workOrderNumber": "WO861118", "targetQuantity": 99, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-07T16:59:31.442292", "status": "active", "mesForwardedCount": 82, "completionRate": 78.8}, "mesErrors": [{"timestamp": "2025-06-12T18:31:31.442292", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050036"}}], "recentLogs": [{"message": "測試條碼-93fb64ad", "timestamp": "2025-06-13T16:47:31.442292", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-d799d7ae", "timestamp": "2025-06-13T16:12:31.442292", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-6a66d917", "timestamp": "2025-06-13T16:11:31.442724", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-73c85e25", "timestamp": "2025-06-13T16:39:31.442724", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-9c92c34c", "timestamp": "2025-06-13T16:50:31.442724", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-3161690d", "timestamp": "2025-06-13T16:08:31.442778", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-46851065", "timestamp": "2025-06-13T16:22:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-e8b9e972", "timestamp": "2025-06-13T16:07:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-4bb3df45", "timestamp": "2025-06-13T16:26:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-8f90a472", "timestamp": "2025-06-13T16:20:31.442778", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-83e7f2b3", "timestamp": "2025-06-13T16:01:31.442778", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-d722374e", "timestamp": "2025-06-13T16:00:31.442778", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-e42b4615", "timestamp": "2025-06-13T16:50:31.442778", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-7642d8a1", "timestamp": "2025-06-13T16:11:31.442778", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-b7797216", "timestamp": "2025-06-13T16:43:31.442778", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-07cb0f36", "timestamp": "2025-06-13T16:17:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-b6e9fcc3", "timestamp": "2025-06-13T16:37:31.442778", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-c130931c", "timestamp": "2025-06-13T16:24:31.442778", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-aaf5a96c", "timestamp": "2025-06-13T16:34:31.442778", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}]}, "S720050037": {"deviceId": "S720050037", "lineName": "B1-02", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 262, "forwardedCount": 130, "unforwardedCount": 132, "lastLog": "測試產品條碼-0e859f61", "lastUpdateTime": "2025-06-13T16:59:31.442778", "createdAt": "2024-07-20T16:59:31.442778", "workOrders": [{"workOrderNumber": "WO945424", "targetQuantity": 311, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-02T16:59:31.442778", "status": "active", "mesForwardedCount": 2, "completionRate": 34.0}, {"workOrderNumber": "WO995912", "targetQuantity": 308, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-17T16:59:31.442778", "status": "pending", "mesForwardedCount": 54, "completionRate": 82.1}, {"workOrderNumber": "WO404398", "targetQuantity": 269, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-06-09T16:59:31.442778", "status": "active", "mesForwardedCount": 14, "completionRate": 22.7}], "currentWorkOrder": 1, "currentWorkOrderProgress": 86, "displayWorkOrder": {"workOrderNumber": "WO995912", "targetQuantity": 308, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-17T16:59:31.442778", "status": "pending", "mesForwardedCount": 54, "completionRate": 82.1}, "mesErrors": [{"timestamp": "2025-06-13T01:44:31.442778", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050037"}}, {"timestamp": "2025-06-13T12:27:31.442778", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050037"}}, {"timestamp": "2025-06-12T18:46:31.442778", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050037"}}], "recentLogs": [{"message": "測試條碼-e3c29a54", "timestamp": "2025-06-13T16:47:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-7e2b066b", "timestamp": "2025-06-13T16:15:31.442778", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-32c64bda", "timestamp": "2025-06-13T16:03:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-b108e277", "timestamp": "2025-06-13T16:23:31.442778", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-14e11d6d", "timestamp": "2025-06-13T16:21:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-69344043", "timestamp": "2025-06-13T16:03:31.442778", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-b14db405", "timestamp": "2025-06-13T16:19:31.442778", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-0e12620a", "timestamp": "2025-06-13T16:18:31.442778", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-a684a0cd", "timestamp": "2025-06-13T16:19:31.442778", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-403ef0f0", "timestamp": "2025-06-13T16:48:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-7959ea10", "timestamp": "2025-06-13T16:25:31.442778", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-52ef3013", "timestamp": "2025-06-13T16:45:31.442778", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-09d30c63", "timestamp": "2025-06-13T16:41:31.442778", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-877fe1c6", "timestamp": "2025-06-13T16:05:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-c05c4efb", "timestamp": "2025-06-13T16:46:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-a03d0eea", "timestamp": "2025-06-13T16:25:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-b12a30b4", "timestamp": "2025-06-13T16:08:31.442778", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-b27e54fd", "timestamp": "2025-06-13T16:51:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}]}, "S720050038": {"deviceId": "S720050038", "lineName": "A2-01", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_02", "isActive": true, "isMonitoring": true, "productCount": 548, "forwardedCount": 308, "unforwardedCount": 240, "lastLog": "測試產品條碼-a1d34627", "lastUpdateTime": "2025-06-13T16:59:31.442778", "createdAt": "2024-06-16T16:59:31.442778", "workOrders": [{"workOrderNumber": "WO305465", "targetQuantity": 76, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-28T16:59:31.442778", "status": "active", "mesForwardedCount": 64, "completionRate": 13.7}, {"workOrderNumber": "WO653240", "targetQuantity": 161, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-04T16:59:31.442778", "status": "active", "mesForwardedCount": 62, "completionRate": 43.2}, {"workOrderNumber": "WO153572", "targetQuantity": 124, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-28T16:59:31.442778", "status": "active", "mesForwardedCount": 64, "completionRate": 90.2}], "currentWorkOrder": 2, "currentWorkOrderProgress": 72, "displayWorkOrder": {"workOrderNumber": "WO153572", "targetQuantity": 124, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-28T16:59:31.442778", "status": "active", "mesForwardedCount": 64, "completionRate": 90.2}, "mesErrors": [{"timestamp": "2025-06-13T11:12:31.442778", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050038"}}, {"timestamp": "2025-06-12T22:18:31.442778", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050038"}}, {"timestamp": "2025-06-12T17:46:31.442778", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050038"}}], "recentLogs": [{"message": "測試條碼-ec267ad1", "timestamp": "2025-06-13T16:14:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-62f58adc", "timestamp": "2025-06-13T16:41:31.442778", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-8d848184", "timestamp": "2025-06-13T16:58:31.442778", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-da183e47", "timestamp": "2025-06-13T16:11:31.442778", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-2dfc2df8", "timestamp": "2025-06-13T16:44:31.442778", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-bf077258", "timestamp": "2025-06-13T16:18:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-9b501610", "timestamp": "2025-06-13T16:53:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-de51a932", "timestamp": "2025-06-13T16:25:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-e689e6a3", "timestamp": "2025-06-13T16:32:31.442778", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-36ef406a", "timestamp": "2025-06-13T16:24:31.442778", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-6c652a3a", "timestamp": "2025-06-13T16:57:31.442778", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-657c0666", "timestamp": "2025-06-13T16:56:31.442778", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-540af3b3", "timestamp": "2025-06-13T16:43:31.442778", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-82f43691", "timestamp": "2025-06-13T16:09:31.442778", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-53781d1e", "timestamp": "2025-06-13T16:39:31.442778", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-38ef459c", "timestamp": "2025-06-13T16:14:31.442778", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-31e9f706", "timestamp": "2025-06-13T16:14:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-8cb1f431", "timestamp": "2025-06-13T16:29:31.442778", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}]}, "S720050039": {"deviceId": "S720050039", "lineName": "B1-01", "sectionName": "TESTING", "groupName": "ASSEMBLY", "stationName": "STATION_04", "isActive": true, "isMonitoring": true, "productCount": 598, "forwardedCount": 139, "unforwardedCount": 459, "lastLog": "測試產品條碼-5f927963", "lastUpdateTime": "2025-06-13T16:59:31.443365", "createdAt": "2024-12-23T16:59:31.443365", "workOrders": [{"workOrderNumber": "WO874070", "targetQuantity": 273, "modelName": "4102161402", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-06-03T16:59:31.442778", "status": "completed", "mesForwardedCount": 57, "completionRate": 86.9}, {"workOrderNumber": "WO889335", "targetQuantity": 75, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-08T16:59:31.442778", "status": "pending", "mesForwardedCount": 56, "completionRate": 74.4}, {"workOrderNumber": "WO166747", "targetQuantity": 201, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-18T16:59:31.442778", "status": "active", "mesForwardedCount": 36, "completionRate": 63.5}], "currentWorkOrder": 1, "currentWorkOrderProgress": 43, "displayWorkOrder": {"workOrderNumber": "WO889335", "targetQuantity": 75, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-08T16:59:31.442778", "status": "pending", "mesForwardedCount": 56, "completionRate": 74.4}, "mesErrors": [{"timestamp": "2025-06-13T03:09:31.443365", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050039"}}], "recentLogs": [{"message": "測試條碼-4c8541ba", "timestamp": "2025-06-13T16:12:31.443365", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-d226d1fd", "timestamp": "2025-06-13T16:17:31.443365", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-156ee502", "timestamp": "2025-06-13T16:15:31.443365", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-e7fa2fc3", "timestamp": "2025-06-13T16:34:31.443365", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-a797b467", "timestamp": "2025-06-13T16:25:31.443365", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-acfa69e0", "timestamp": "2025-06-13T16:35:31.443365", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f39918d4", "timestamp": "2025-06-13T16:13:31.443365", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-60cb55f3", "timestamp": "2025-06-13T16:52:31.443365", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-60138ab8", "timestamp": "2025-06-13T16:43:31.443365", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}]}, "S720050040": {"deviceId": "S720050040", "lineName": "B1-02", "sectionName": "ASSEMBLY", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 207, "forwardedCount": 140, "unforwardedCount": 67, "lastLog": "測試產品條碼-d26caaa1", "lastUpdateTime": "2025-06-13T16:59:31.443365", "createdAt": "2024-07-05T16:59:31.443365", "workOrders": [{"workOrderNumber": "WO925059", "targetQuantity": 231, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-22T16:59:31.443365", "status": "active", "mesForwardedCount": 31, "completionRate": 80.1}, {"workOrderNumber": "WO481606", "targetQuantity": 342, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-24T16:59:31.443365", "status": "pending", "mesForwardedCount": 75, "completionRate": 88.9}, {"workOrderNumber": "WO561457", "targetQuantity": 462, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-27T16:59:31.443365", "status": "pending", "mesForwardedCount": 67, "completionRate": 86.9}], "currentWorkOrder": 0, "currentWorkOrderProgress": 51, "displayWorkOrder": {"workOrderNumber": "WO925059", "targetQuantity": 231, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-22T16:59:31.443365", "status": "active", "mesForwardedCount": 31, "completionRate": 80.1}, "mesErrors": [{"timestamp": "2025-06-13T16:23:31.443365", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050040"}}, {"timestamp": "2025-06-12T18:01:31.443365", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050040"}}, {"timestamp": "2025-06-12T21:15:31.443365", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050040"}}, {"timestamp": "2025-06-13T10:31:31.443365", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050040"}}, {"timestamp": "2025-06-12T18:00:31.443365", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050040"}}], "recentLogs": [{"message": "測試條碼-ec1a861c", "timestamp": "2025-06-13T16:56:31.443365", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a6375fbb", "timestamp": "2025-06-13T16:58:31.443365", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-aadc99c9", "timestamp": "2025-06-13T16:51:31.443576", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-5fb7fc19", "timestamp": "2025-06-13T15:59:31.443576", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-785ec177", "timestamp": "2025-06-13T16:00:31.443576", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-b122d77c", "timestamp": "2025-06-13T16:51:31.443576", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-6bcee852", "timestamp": "2025-06-13T16:58:31.443576", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-18dd67f4", "timestamp": "2025-06-13T16:39:31.443576", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d98a3b48", "timestamp": "2025-06-13T16:44:31.443576", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-9d5e8d7a", "timestamp": "2025-06-13T16:36:31.443576", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-14b91c85", "timestamp": "2025-06-13T16:12:31.443576", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-423389c0", "timestamp": "2025-06-13T16:29:31.443576", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-e2a0117a", "timestamp": "2025-06-13T16:58:31.443576", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-75e1f99c", "timestamp": "2025-06-13T16:45:31.443576", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}]}, "S720050041": {"deviceId": "S720050041", "lineName": "B1-02", "sectionName": "TESTING", "groupName": "TEST", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 242, "forwardedCount": 15, "unforwardedCount": 227, "lastLog": "測試產品條碼-59af3f74", "lastUpdateTime": "2025-06-13T16:59:31.443576", "createdAt": "2025-01-31T16:59:31.443576", "workOrders": [{"workOrderNumber": "WO604384", "targetQuantity": 273, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-31T16:59:31.443576", "status": "completed", "mesForwardedCount": 90, "completionRate": 25.5}, {"workOrderNumber": "WO949351", "targetQuantity": 314, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-13T16:59:31.443576", "status": "active", "mesForwardedCount": 16, "completionRate": 84.9}, {"workOrderNumber": "WO824352", "targetQuantity": 218, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-23T16:59:31.443576", "status": "pending", "mesForwardedCount": 99, "completionRate": 8.6}], "currentWorkOrder": 0, "currentWorkOrderProgress": 64, "displayWorkOrder": {"workOrderNumber": "WO604384", "targetQuantity": 273, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 2, "createdAt": "2025-05-31T16:59:31.443576", "status": "completed", "mesForwardedCount": 90, "completionRate": 25.5}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-cf5a61bf", "timestamp": "2025-06-13T16:04:31.443576", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-c6a86b8a", "timestamp": "2025-06-13T16:20:31.443576", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-5ef0ecde", "timestamp": "2025-06-13T16:18:31.443576", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-48f570dd", "timestamp": "2025-06-13T16:30:31.443576", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-89441162", "timestamp": "2025-06-13T16:04:31.443576", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-7ab1492d", "timestamp": "2025-06-13T16:31:31.443576", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-15d9ca28", "timestamp": "2025-06-13T16:18:31.443576", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-7e794520", "timestamp": "2025-06-13T16:17:31.443576", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-f5ecbc55", "timestamp": "2025-06-13T16:57:31.443576", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-aee68a3e", "timestamp": "2025-06-13T16:02:31.443576", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-6c0d209a", "timestamp": "2025-06-13T16:05:31.443576", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-f48b4a63", "timestamp": "2025-06-13T16:53:31.443576", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-2a788b57", "timestamp": "2025-06-13T16:49:31.443576", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}]}, "S720050042": {"deviceId": "S720050042", "lineName": "A1-01", "sectionName": "ASSEMBLY", "groupName": "TEST", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 325, "forwardedCount": 280, "unforwardedCount": 45, "lastLog": "測試產品條碼-6f4d6db0", "lastUpdateTime": "2025-06-13T16:59:31.443576", "createdAt": "2025-01-20T16:59:31.443576", "workOrders": [{"workOrderNumber": "WO669955", "targetQuantity": 275, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-07T16:59:31.443576", "status": "active", "mesForwardedCount": 47, "completionRate": 67.3}, {"workOrderNumber": "WO467975", "targetQuantity": 156, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-24T16:59:31.443576", "status": "pending", "mesForwardedCount": 94, "completionRate": 19.2}, {"workOrderNumber": "WO250339", "targetQuantity": 450, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-06-12T16:59:31.443576", "status": "pending", "mesForwardedCount": 58, "completionRate": 9.4}], "currentWorkOrder": 0, "currentWorkOrderProgress": 27, "displayWorkOrder": {"workOrderNumber": "WO669955", "targetQuantity": 275, "modelName": "4102161401", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-07T16:59:31.443576", "status": "active", "mesForwardedCount": 47, "completionRate": 67.3}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-7b6c45f6", "timestamp": "2025-06-13T16:19:31.443576", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-d3631d2a", "timestamp": "2025-06-13T16:07:31.443576", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-89d0f450", "timestamp": "2025-06-13T16:33:31.443576", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-a2a61d34", "timestamp": "2025-06-13T16:38:31.443576", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-62beda60", "timestamp": "2025-06-13T16:58:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-cc5f0ef4", "timestamp": "2025-06-13T16:43:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-7586267f", "timestamp": "2025-06-13T16:58:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}]}, "S720050043": {"deviceId": "S720050043", "lineName": "A1-01", "sectionName": "INJECTION", "groupName": "INJECTION", "stationName": "STATION_03", "isActive": true, "isMonitoring": true, "productCount": 984, "forwardedCount": 734, "unforwardedCount": 250, "lastLog": "測試產品條碼-ccd6be2d", "lastUpdateTime": "2025-06-13T16:59:31.443959", "createdAt": "2025-03-22T16:59:31.443959", "workOrders": [{"workOrderNumber": "WO110008", "targetQuantity": 400, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-29T16:59:31.443959", "status": "completed", "mesForwardedCount": 80, "completionRate": 53.4}, {"workOrderNumber": "WO996386", "targetQuantity": 241, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-06T16:59:31.443959", "status": "completed", "mesForwardedCount": 79, "completionRate": 33.2}, {"workOrderNumber": "WO356507", "targetQuantity": 316, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-22T16:59:31.443959", "status": "completed", "mesForwardedCount": 49, "completionRate": 82.3}], "currentWorkOrder": 0, "currentWorkOrderProgress": 7, "displayWorkOrder": {"workOrderNumber": "WO110008", "targetQuantity": 400, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-29T16:59:31.443959", "status": "completed", "mesForwardedCount": 80, "completionRate": 53.4}, "mesErrors": [{"timestamp": "2025-06-13T02:16:31.443959", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050043"}}, {"timestamp": "2025-06-13T06:01:31.443959", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050043"}}, {"timestamp": "2025-06-12T23:25:31.443959", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050043"}}, {"timestamp": "2025-06-13T07:33:31.443959", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050043"}}, {"timestamp": "2025-06-12T23:22:31.443959", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 5", "card_response": "HTTP 200 | 測試響應 5", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050043"}}], "recentLogs": [{"message": "測試條碼-ca0bcbe4", "timestamp": "2025-06-13T16:12:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-6932805c", "timestamp": "2025-06-13T16:42:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-1aae7e18", "timestamp": "2025-06-13T16:28:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d79eae08", "timestamp": "2025-06-13T16:37:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-19a25b9a", "timestamp": "2025-06-13T16:00:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-e666409a", "timestamp": "2025-06-13T16:45:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-d7c3287c", "timestamp": "2025-06-13T16:38:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-910acd43", "timestamp": "2025-06-13T16:33:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-d2b239e8", "timestamp": "2025-06-13T16:38:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-6395e38d", "timestamp": "2025-06-13T16:24:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-51ce2d35", "timestamp": "2025-06-13T16:06:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-0e136dbc", "timestamp": "2025-06-13T16:31:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-f50c94fe", "timestamp": "2025-06-13T16:54:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-1e8d77ca", "timestamp": "2025-06-13T15:59:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-00f0325a", "timestamp": "2025-06-13T16:56:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-ce021ede", "timestamp": "2025-06-13T16:10:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-70218743", "timestamp": "2025-06-13T16:39:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-65487af3", "timestamp": "2025-06-13T16:11:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-5cdc8745", "timestamp": "2025-06-13T16:05:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-a3ffbb60", "timestamp": "2025-06-13T16:01:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}]}, "S720050044": {"deviceId": "S720050044", "lineName": "B2-02", "sectionName": "PACKAGING", "groupName": "PACK", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 471, "forwardedCount": 348, "unforwardedCount": 123, "lastLog": "測試產品條碼-415961c4", "lastUpdateTime": "2025-06-13T16:59:31.443959", "createdAt": "2025-02-11T16:59:31.443959", "workOrders": [{"workOrderNumber": "WO423474", "targetQuantity": 217, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-25T16:59:31.443959", "status": "pending", "mesForwardedCount": 20, "completionRate": 67.7}, {"workOrderNumber": "WO369795", "targetQuantity": 425, "modelName": "4102161401", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-05-18T16:59:31.443959", "status": "pending", "mesForwardedCount": 20, "completionRate": 43.1}, {"workOrderNumber": "WO635697", "targetQuantity": 495, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-31T16:59:31.443959", "status": "pending", "mesForwardedCount": 58, "completionRate": 66.7}], "currentWorkOrder": 2, "currentWorkOrderProgress": 68, "displayWorkOrder": {"workOrderNumber": "WO635697", "targetQuantity": 495, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-31T16:59:31.443959", "status": "pending", "mesForwardedCount": 58, "completionRate": 66.7}, "mesErrors": [{"timestamp": "2025-06-12T22:51:31.443959", "message": "✅ 成功: OK", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050044"}}, {"timestamp": "2025-06-13T16:06:31.443959", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050044"}}], "recentLogs": [{"message": "測試條碼-18c777e4", "timestamp": "2025-06-13T16:16:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-45688b15", "timestamp": "2025-06-13T16:48:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-c29aa081", "timestamp": "2025-06-13T16:19:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-d50077e4", "timestamp": "2025-06-13T16:47:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-620b84d3", "timestamp": "2025-06-13T16:21:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-db995717", "timestamp": "2025-06-13T16:58:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-7aaafcfa", "timestamp": "2025-06-13T16:10:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-4aa98973", "timestamp": "2025-06-13T16:16:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-9136c468", "timestamp": "2025-06-13T16:53:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-2d0842c4", "timestamp": "2025-06-13T16:40:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}]}, "S720050045": {"deviceId": "S720050045", "lineName": "A1-02", "sectionName": "INJECTION", "groupName": "PACK", "stationName": "STATION_04", "isActive": true, "isMonitoring": false, "productCount": 810, "forwardedCount": 658, "unforwardedCount": 152, "lastLog": "測試產品條碼-31fa4792", "lastUpdateTime": "2025-06-13T16:59:31.443959", "createdAt": "2025-05-12T16:59:31.443959", "workOrders": [{"workOrderNumber": "WO950893", "targetQuantity": 185, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-12T16:59:31.443959", "status": "active", "mesForwardedCount": 5, "completionRate": 99.3}, {"workOrderNumber": "WO608935", "targetQuantity": 413, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-22T16:59:31.443959", "status": "pending", "mesForwardedCount": 43, "completionRate": 32.3}, {"workOrderNumber": "WO641368", "targetQuantity": 384, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-17T16:59:31.443959", "status": "active", "mesForwardedCount": 38, "completionRate": 40.9}], "currentWorkOrder": 2, "currentWorkOrderProgress": 14, "displayWorkOrder": {"workOrderNumber": "WO641368", "targetQuantity": 384, "modelName": "4102161400", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-05-17T16:59:31.443959", "status": "active", "mesForwardedCount": 38, "completionRate": 40.9}, "mesErrors": [{"timestamp": "2025-06-13T01:12:31.443959", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050045"}}, {"timestamp": "2025-06-13T13:06:31.443959", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050045"}}, {"timestamp": "2025-06-13T05:56:31.443959", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050045"}}, {"timestamp": "2025-06-13T13:58:31.443959", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 4", "card_response": "HTTP 200 | 測試響應 4", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050045"}}], "recentLogs": [{"message": "測試條碼-ece895f1", "timestamp": "2025-06-13T16:39:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-6071b537", "timestamp": "2025-06-13T16:33:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-a4249949", "timestamp": "2025-06-13T16:44:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-a1a6f933", "timestamp": "2025-06-13T16:22:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-53116380", "timestamp": "2025-06-13T16:27:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ec56a934", "timestamp": "2025-06-13T16:27:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-9fabba16", "timestamp": "2025-06-13T16:57:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-0e7b93f9", "timestamp": "2025-06-13T16:47:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-d3e712af", "timestamp": "2025-06-13T16:34:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-89bc7f5a", "timestamp": "2025-06-13T16:03:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-23d08637", "timestamp": "2025-06-13T16:29:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-a5daf473", "timestamp": "2025-06-13T16:55:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-5d648c2a", "timestamp": "2025-06-13T16:10:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-ae3e30f1", "timestamp": "2025-06-13T16:04:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-cce90141", "timestamp": "2025-06-13T16:06:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-02a7d5e9", "timestamp": "2025-06-13T16:00:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-6d5c566e", "timestamp": "2025-06-13T16:28:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-6bf9b1e0", "timestamp": "2025-06-13T16:42:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-da60ccd2", "timestamp": "2025-06-13T15:59:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}]}, "S720050046": {"deviceId": "S720050046", "lineName": "B1-01", "sectionName": "INJECTION", "groupName": "PACK", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 393, "forwardedCount": 213, "unforwardedCount": 180, "lastLog": "測試產品條碼-ba7693eb", "lastUpdateTime": "2025-06-13T16:59:31.443959", "createdAt": "2025-01-30T16:59:31.443959", "workOrders": [{"workOrderNumber": "WO923921", "targetQuantity": 500, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-05-24T16:59:31.443959", "status": "pending", "mesForwardedCount": 88, "completionRate": 43.3}, {"workOrderNumber": "WO960518", "targetQuantity": 160, "modelName": "4102161404", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-05-17T16:59:31.443959", "status": "active", "mesForwardedCount": 83, "completionRate": 74.6}, {"workOrderNumber": "WO948890", "targetQuantity": 152, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-31T16:59:31.443959", "status": "completed", "mesForwardedCount": 44, "completionRate": 66.7}], "currentWorkOrder": 2, "currentWorkOrderProgress": 38, "displayWorkOrder": {"workOrderNumber": "WO948890", "targetQuantity": 152, "modelName": "4102161404", "description": "測試工單 3", "cavityCount": 3, "createdAt": "2025-05-31T16:59:31.443959", "status": "completed", "mesForwardedCount": 44, "completionRate": 66.7}, "mesErrors": [{"timestamp": "2025-06-13T03:28:31.443959", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050046"}}, {"timestamp": "2025-06-13T04:07:31.443959", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050046"}}, {"timestamp": "2025-06-13T09:53:31.443959", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050046"}}], "recentLogs": [{"message": "測試條碼-35d45ec0", "timestamp": "2025-06-13T16:48:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-3654a50c", "timestamp": "2025-06-13T16:02:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-50da2873", "timestamp": "2025-06-13T16:14:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-8db27ba4", "timestamp": "2025-06-13T16:57:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-47127a71", "timestamp": "2025-06-13T16:30:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-d71b50b4", "timestamp": "2025-06-13T16:07:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-3dbb2eed", "timestamp": "2025-06-13T16:23:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-c501ff47", "timestamp": "2025-06-13T16:18:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-4b5d12b1", "timestamp": "2025-06-13T16:03:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-eaab481b", "timestamp": "2025-06-13T16:15:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-47823793", "timestamp": "2025-06-13T16:09:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-e1f0cc6b", "timestamp": "2025-06-13T16:37:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-66e519f5", "timestamp": "2025-06-13T16:56:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-3527fd0b", "timestamp": "2025-06-13T16:05:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-d538c8ec", "timestamp": "2025-06-13T16:27:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-73e21190", "timestamp": "2025-06-13T16:11:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-3ae07b9d", "timestamp": "2025-06-13T16:47:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-db684d0f", "timestamp": "2025-06-13T16:38:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-39aa770a", "timestamp": "2025-06-13T16:21:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-39c9faff", "timestamp": "2025-06-13T16:50:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}]}, "S720050047": {"deviceId": "S720050047", "lineName": "B1-01", "sectionName": "ASSEMBLY", "groupName": "ASSEMBLY", "stationName": "STATION_01", "isActive": true, "isMonitoring": false, "productCount": 378, "forwardedCount": 191, "unforwardedCount": 187, "lastLog": "測試產品條碼-5634e3e5", "lastUpdateTime": "2025-06-13T16:59:31.443959", "createdAt": "2024-10-27T16:59:31.443959", "workOrders": [{"workOrderNumber": "WO253534", "targetQuantity": 479, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 4, "createdAt": "2025-06-02T16:59:31.443959", "status": "completed", "mesForwardedCount": 85, "completionRate": 86.4}, {"workOrderNumber": "WO834169", "targetQuantity": 251, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 3, "createdAt": "2025-06-08T16:59:31.443959", "status": "completed", "mesForwardedCount": 71, "completionRate": 57.7}, {"workOrderNumber": "WO191875", "targetQuantity": 153, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-07T16:59:31.443959", "status": "pending", "mesForwardedCount": 59, "completionRate": 27.8}], "currentWorkOrder": 2, "currentWorkOrderProgress": 24, "displayWorkOrder": {"workOrderNumber": "WO191875", "targetQuantity": 153, "modelName": "4102161402", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-07T16:59:31.443959", "status": "pending", "mesForwardedCount": 59, "completionRate": 27.8}, "mesErrors": [{"timestamp": "2025-06-13T08:57:31.443959", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050047"}}, {"timestamp": "2025-06-12T23:20:31.443959", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050047"}}, {"timestamp": "2025-06-13T03:16:31.443959", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050047"}}], "recentLogs": [{"message": "測試條碼-8113a968", "timestamp": "2025-06-13T16:20:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-a3c711b7", "timestamp": "2025-06-13T16:30:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-1da69f63", "timestamp": "2025-06-13T16:23:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-aca138c5", "timestamp": "2025-06-13T16:57:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-f6115d19", "timestamp": "2025-06-13T16:38:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-79b6a6a0", "timestamp": "2025-06-13T16:30:31.443959", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-811daa0c", "timestamp": "2025-06-13T16:40:31.443959", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-c7f72bb1", "timestamp": "2025-06-13T16:43:31.443959", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-872e0760", "timestamp": "2025-06-13T16:41:31.443959", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-d589a5d5", "timestamp": "2025-06-13T16:44:31.445024", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-a8679d46", "timestamp": "2025-06-13T16:34:31.445024", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-a55deabd", "timestamp": "2025-06-13T16:39:31.445024", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-30f48102", "timestamp": "2025-06-13T16:15:31.445024", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-8a4395c0", "timestamp": "2025-06-13T16:56:31.445024", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-c5787689", "timestamp": "2025-06-13T16:18:31.445024", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-cd6999b5", "timestamp": "2025-06-13T16:28:31.445024", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-1a8ac0fc", "timestamp": "2025-06-13T16:51:31.445024", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-e44adb2e", "timestamp": "2025-06-13T16:12:31.445024", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-e6bc38c4", "timestamp": "2025-06-13T16:42:31.445024", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": false}]}, "S720050048": {"deviceId": "S720050048", "lineName": "A2-01", "sectionName": "INJECTION", "groupName": "TEST", "stationName": "STATION_03", "isActive": true, "isMonitoring": false, "productCount": 325, "forwardedCount": 119, "unforwardedCount": 206, "lastLog": "測試產品條碼-76b7b177", "lastUpdateTime": "2025-06-13T16:59:31.445024", "createdAt": "2025-02-17T16:59:31.445024", "workOrders": [{"workOrderNumber": "WO538960", "targetQuantity": 237, "modelName": "4102161403", "description": "測試工單 1", "cavityCount": 1, "createdAt": "2025-05-28T16:59:31.445024", "status": "completed", "mesForwardedCount": 73, "completionRate": 15.2}, {"workOrderNumber": "WO506848", "targetQuantity": 384, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-11T16:59:31.445024", "status": "pending", "mesForwardedCount": 60, "completionRate": 14.5}, {"workOrderNumber": "WO756429", "targetQuantity": 329, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 1, "createdAt": "2025-05-19T16:59:31.445024", "status": "active", "mesForwardedCount": 27, "completionRate": 64.5}], "currentWorkOrder": 1, "currentWorkOrderProgress": 14, "displayWorkOrder": {"workOrderNumber": "WO506848", "targetQuantity": 384, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 2, "createdAt": "2025-06-11T16:59:31.445024", "status": "pending", "mesForwardedCount": 60, "completionRate": 14.5}, "mesErrors": [{"timestamp": "2025-06-13T10:37:31.445024", "message": "❌ 失敗: NETWORK_TIMEOUT", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050048"}}, {"timestamp": "2025-06-12T21:32:31.445024", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050048"}}, {"timestamp": "2025-06-13T13:40:31.445024", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050048"}}], "recentLogs": [{"message": "測試條碼-abd60446", "timestamp": "2025-06-13T16:49:31.445024", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-8154c756", "timestamp": "2025-06-13T16:53:31.445024", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-df41c572", "timestamp": "2025-06-13T16:44:31.445024", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-dc010dc2", "timestamp": "2025-06-13T16:50:31.445024", "unitCount": 1, "actualProductCount": 4, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-790bca81", "timestamp": "2025-06-13T16:56:31.445024", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-1b4d72f2", "timestamp": "2025-06-13T16:30:31.445024", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-79def4f2", "timestamp": "2025-06-13T16:43:31.445024", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-d8e5301b", "timestamp": "2025-06-13T16:22:31.445024", "unitCount": 1, "actualProductCount": 3, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-965af90d", "timestamp": "2025-06-13T16:35:31.445024", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-7571c1ea", "timestamp": "2025-06-13T16:04:31.445024", "unitCount": 1, "actualProductCount": 2, "cavityCount": 3, "isForwarded": false}]}, "S720050049": {"deviceId": "S720050049", "lineName": "A2-01", "sectionName": "TESTING", "groupName": "PACK", "stationName": "STATION_02", "isActive": true, "isMonitoring": false, "productCount": 947, "forwardedCount": 34, "unforwardedCount": 913, "lastLog": "測試產品條碼-9fc04b52", "lastUpdateTime": "2025-06-13T16:59:31.445024", "createdAt": "2025-05-29T16:59:31.445024", "workOrders": [{"workOrderNumber": "WO759764", "targetQuantity": 213, "modelName": "4102161404", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-05-19T16:59:31.445024", "status": "active", "mesForwardedCount": 83, "completionRate": 70.4}, {"workOrderNumber": "WO827957", "targetQuantity": 435, "modelName": "4102161400", "description": "測試工單 2", "cavityCount": 1, "createdAt": "2025-06-04T16:59:31.445024", "status": "pending", "mesForwardedCount": 40, "completionRate": 16.4}, {"workOrderNumber": "WO405587", "targetQuantity": 80, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-27T16:59:31.445024", "status": "completed", "mesForwardedCount": 60, "completionRate": 36.8}], "currentWorkOrder": 2, "currentWorkOrderProgress": 0, "displayWorkOrder": {"workOrderNumber": "WO405587", "targetQuantity": 80, "modelName": "4102161403", "description": "測試工單 3", "cavityCount": 4, "createdAt": "2025-05-27T16:59:31.445024", "status": "completed", "mesForwardedCount": 60, "completionRate": 36.8}, "mesErrors": [{"timestamp": "2025-06-13T03:03:31.445024", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 1", "card_response": "HTTP 200 | 測試響應 1", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050049"}}, {"timestamp": "2025-06-13T00:01:31.445024", "message": "❌ 失敗: STATION NOT EXIST OR INVALID", "full_response": "HTTP 200 | 測試響應數據 2", "card_response": "HTTP 200 | 測試響應 2", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050049"}}, {"timestamp": "2025-06-13T01:31:31.445024", "message": "❌ 失敗: MO_HAS_FULL", "full_response": "HTTP 200 | 測試響應數據 3", "card_response": "HTTP 200 | 測試響應 3", "request_data": {"url": "http://test.mes.api/endpoint", "method": "POST", "device_id": "S720050049"}}], "recentLogs": [{"message": "測試條碼-b8b94913", "timestamp": "2025-06-13T16:41:31.445024", "unitCount": 1, "actualProductCount": 1, "cavityCount": 3, "isForwarded": true}, {"message": "測試條碼-ef0310a2", "timestamp": "2025-06-13T16:07:31.445024", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-79dea153", "timestamp": "2025-06-13T16:18:31.445024", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": false}, {"message": "測試條碼-3a5bd3a3", "timestamp": "2025-06-13T16:13:31.445024", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-5f59a291", "timestamp": "2025-06-13T16:24:31.445024", "unitCount": 1, "actualProductCount": 3, "cavityCount": 3, "isForwarded": false}, {"message": "測試條碼-39c99226", "timestamp": "2025-06-13T16:55:31.445024", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-8d6e5259", "timestamp": "2025-06-13T16:05:31.445024", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-7511e92e", "timestamp": "2025-06-13T16:47:31.445024", "unitCount": 1, "actualProductCount": 1, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-5979b072", "timestamp": "2025-06-13T16:05:31.445024", "unitCount": 1, "actualProductCount": 4, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-7857b435", "timestamp": "2025-06-13T16:55:31.445024", "unitCount": 1, "actualProductCount": 2, "cavityCount": 1, "isForwarded": false}, {"message": "測試條碼-e0047871", "timestamp": "2025-06-13T16:49:31.445024", "unitCount": 1, "actualProductCount": 4, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-70c74069", "timestamp": "2025-06-13T16:05:31.445024", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-c2547e5e", "timestamp": "2025-06-13T16:26:31.445024", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": true}, {"message": "測試條碼-4a77e13f", "timestamp": "2025-06-13T16:39:31.445024", "unitCount": 1, "actualProductCount": 3, "cavityCount": 4, "isForwarded": false}, {"message": "測試條碼-ab531f85", "timestamp": "2025-06-13T16:00:31.445024", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}]}, "S720050050": {"deviceId": "S720050050", "lineName": "A2-02", "sectionName": "INJECTION", "groupName": "PACK", "stationName": "STATION_01", "isActive": true, "isMonitoring": true, "productCount": 731, "forwardedCount": 187, "unforwardedCount": 544, "lastLog": "測試產品條碼-bf86d2ab", "lastUpdateTime": "2025-06-13T16:59:31.445024", "createdAt": "2025-05-30T16:59:31.445024", "workOrders": [{"workOrderNumber": "WO932756", "targetQuantity": 431, "modelName": "4102161400", "description": "測試工單 1", "cavityCount": 3, "createdAt": "2025-06-06T16:59:31.445024", "status": "active", "mesForwardedCount": 14, "completionRate": 35.7}, {"workOrderNumber": "WO989465", "targetQuantity": 88, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-22T16:59:31.445024", "status": "pending", "mesForwardedCount": 38, "completionRate": 41.0}, {"workOrderNumber": "WO768843", "targetQuantity": 50, "modelName": "4102161401", "description": "測試工單 3", "cavityCount": 2, "createdAt": "2025-06-08T16:59:31.445024", "status": "completed", "mesForwardedCount": 93, "completionRate": 40.6}], "currentWorkOrder": 1, "currentWorkOrderProgress": 81, "displayWorkOrder": {"workOrderNumber": "WO989465", "targetQuantity": 88, "modelName": "4102161403", "description": "測試工單 2", "cavityCount": 4, "createdAt": "2025-05-22T16:59:31.445024", "status": "pending", "mesForwardedCount": 38, "completionRate": 41.0}, "mesErrors": [], "recentLogs": [{"message": "測試條碼-7309f8e9", "timestamp": "2025-06-13T16:20:31.445024", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-e927c368", "timestamp": "2025-06-13T16:14:31.445024", "unitCount": 1, "actualProductCount": 2, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-38a79455", "timestamp": "2025-06-13T16:37:31.445024", "unitCount": 1, "actualProductCount": 1, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-f53587c8", "timestamp": "2025-06-13T16:18:31.445024", "unitCount": 1, "actualProductCount": 1, "cavityCount": 1, "isForwarded": true}, {"message": "測試條碼-698bd97b", "timestamp": "2025-06-13T16:19:31.445024", "unitCount": 1, "actualProductCount": 4, "cavityCount": 2, "isForwarded": true}, {"message": "測試條碼-42030a76", "timestamp": "2025-06-13T16:45:31.445024", "unitCount": 1, "actualProductCount": 3, "cavityCount": 2, "isForwarded": false}]}}