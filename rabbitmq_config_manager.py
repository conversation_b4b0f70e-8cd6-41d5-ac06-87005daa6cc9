#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RabbitMQ配置管理器
管理RabbitMQ連接配置和設置
"""

import json
import os
from datetime import datetime
from config import DEFAULT_RABBITMQ_CONFIG, RABBITMQ_ROUTING_OPTIONS

class RabbitMQConfigManager:
    def __init__(self, config_file='rabbitmq_config.json'):
        self.config_file = config_file
        self.default_config = DEFAULT_RABBITMQ_CONFIG.copy()
        self.default_config['last_updated'] = datetime.now().isoformat()
        self.config = self.load_config()

    def load_config(self):
        """加載配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 確保所有必需的字段都存在
                    for key, value in self.default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            else:
                return self.default_config.copy()
        except Exception as e:
            print(f"❌ 加載RabbitMQ配置失敗: {e}")
            return self.default_config.copy()

    def save_config(self):
        """保存配置到文件"""
        try:
            self.config['last_updated'] = datetime.now().isoformat()
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            print(f"✅ RabbitMQ配置已保存到 {self.config_file}")
            return True
        except Exception as e:
            print(f"❌ 保存RabbitMQ配置失敗: {e}")
            return False

    def get_config(self):
        """獲取當前配置"""
        return self.config.copy()

    def update_config(self, new_config):
        """更新配置"""
        try:
            # 驗證必需字段
            required_fields = ['host', 'port', 'username', 'password', 'exchange_name']
            for field in required_fields:
                if field not in new_config:
                    raise ValueError(f"缺少必需字段: {field}")

            # 驗證端口號
            if not isinstance(new_config['port'], int) or new_config['port'] <= 0:
                raise ValueError("端口號必須是正整數")

            # 更新配置
            for key, value in new_config.items():
                if key in self.default_config:
                    self.config[key] = value

            # 保存配置
            return self.save_config()

        except Exception as e:
            print(f"❌ 更新RabbitMQ配置失敗: {e}")
            return False

    def get_routing_keys(self, device_ids):
        """根據設備ID列表生成routing_key列表"""
        pattern = self.config.get('routing_key_pattern', self.default_config['routing_key_pattern'])
        routing_keys = []
        
        for device_id in device_ids:
            routing_key = pattern.format(device_id=device_id)
            routing_keys.append(routing_key)
        
        return routing_keys

    def validate_config(self):
        """驗證配置有效性"""
        try:
            required_fields = ['host', 'port', 'username', 'password', 'exchange_name']
            for field in required_fields:
                if not self.config.get(field):
                    return False, f"缺少必需字段: {field}"

            if not isinstance(self.config['port'], int) or self.config['port'] <= 0:
                return False, "端口號必須是正整數"

            return True, "配置有效"

        except Exception as e:
            return False, f"配置驗證失敗: {e}"

    def get_connection_info(self):
        """獲取連接信息摘要"""
        return {
            'host': self.config['host'],
            'port': self.config['port'],
            'exchange': self.config['exchange_name'],
            'username': self.config['username'],
            'last_updated': self.config.get('last_updated', 'Unknown')
        }

    def get_routing_options(self):
        """獲取可用的Routing Key選項"""
        return RABBITMQ_ROUTING_OPTIONS

    def get_message_type_from_pattern(self, pattern):
        """從routing key模式中提取消息類型"""
        if 'UnitsDeparted' in pattern:
            return 'UnitsDeparted'
        elif 'WorkCompleted' in pattern:
            return 'WorkCompleted'
        return 'UnitsDeparted'  # 默認值
