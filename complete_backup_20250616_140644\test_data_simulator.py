"""
測試數據模擬器
用於模擬Kafka消息，測試MES轉發功能
"""

import json
import time
from datetime import datetime
from device_manager import DeviceManager
from kafka_monitor import KafkaMonitor
from config import TEST_DATA

class TestDataSimulator:
    def __init__(self):
        self.device_manager = DeviceManager()
        self.kafka_monitor = KafkaMonitor(self.device_manager)
    
    def create_test_device(self):
        """創建測試設備"""
        try:
            device_id = self.device_manager.add_device(
                device_id='SC21100803',
                line_name='MAG-H71',
                section_name='測試段',
                group_name='測試組',
                station_name='測試站',
                work_order='WO20250610001',
                model='TestModel-A'
            )
            # 啟用監控
            self.device_manager.toggle_device_monitoring(device_id, True)
            print(f"✅ 測試設備創建成功: {device_id}")
            return device_id
        except ValueError as e:
            if "已存在" in str(e):
                print("ℹ️  測試設備已存在，使用現有設備")
                # 確保監控已啟用
                self.device_manager.toggle_device_monitoring('SC21100803', True)
                return 'SC21100803'
            else:
                raise
    
    def simulate_kafka_message(self, unit_identifier='5490', status='Pass'):
        """模擬Kafka消息"""
        # 複製測試數據並修改相關字段
        test_message = TEST_DATA['sample_kafka_message'].copy()
        
        # 更新時間戳
        current_time = datetime.now().isoformat() + '+00:00'
        test_message['MessageTime'] = current_time
        test_message['Data']['ProcessedTime'] = current_time
        test_message['Data']['RawData']['TimeStamp'] = current_time
        
        # 更新產品數據
        test_message['Data']['RawData']['MessageBody']['Units'][0]['UnitIdentifier'] = unit_identifier
        test_message['Data']['RawData']['MessageBody']['Units'][0]['Status'] = status
        test_message['Data']['RawData']['MessageBody']['Units'][0]['PositionName'] = f'{unit_identifier}_1'
        
        return test_message
    
    def test_single_message(self, unit_identifier='5490', status='Pass'):
        """測試單個消息處理"""
        print(f"\n🧪 測試單個消息處理")
        print(f"產品條碼: {unit_identifier}, 狀態: {status}")
        print("="*60)
        
        # 創建模擬消息
        test_message = self.simulate_kafka_message(unit_identifier, status)
        
        # 模擬Kafka消息對象
        class MockMessage:
            def __init__(self, value, timestamp):
                self.value = value
                self.timestamp = timestamp * 1000  # 轉換為毫秒
                self.topic = 'EAP.CZ.MAG.MAG-H71.DEVICE_CFX.CFX.Production.UnitsDeparted'
                self.partition = 0
                self.offset = 12345
        
        mock_message = MockMessage(test_message, time.time())
        
        # 處理消息
        self.kafka_monitor._process_message(mock_message)
    
    def test_multiple_messages(self):
        """測試多個消息處理"""
        print(f"\n🧪 測試多個消息處理")
        print("="*60)
        
        test_cases = [
            ('5490', 'Pass'),
            ('5491', 'Pass'),
            ('5492', 'Fail'),
            ('5493', 'Pass'),
            ('5494', 'Pass')
        ]
        
        for i, (unit_id, status) in enumerate(test_cases, 1):
            print(f"\n--- 測試消息 {i}/{len(test_cases)} ---")
            self.test_single_message(unit_id, status)
            time.sleep(2)  # 間隔2秒
    
    def test_unmonitored_device(self):
        """測試未監控設備的消息"""
        print(f"\n🧪 測試未監控設備消息")
        print("="*60)
        
        # 創建一個未監控設備的消息
        test_message = self.simulate_kafka_message()
        test_message['Data']['Meta']['DeviceID'] = 'UNKNOWN_DEVICE'
        
        class MockMessage:
            def __init__(self, value, timestamp):
                self.value = value
                self.timestamp = timestamp * 1000
                self.topic = 'EAP.CZ.MAG.MAG-H71.DEVICE_CFX.CFX.Production.UnitsDeparted'
                self.partition = 0
                self.offset = 12346
        
        mock_message = MockMessage(test_message, time.time())
        self.kafka_monitor._process_message(mock_message)
    
    def run_all_tests(self):
        """運行所有測試"""
        print("🚀 開始MES轉發功能測試")
        print("="*80)
        
        # 1. 創建測試設備
        self.create_test_device()
        
        # 2. 測試單個消息
        self.test_single_message()
        
        # 3. 測試多個消息
        self.test_multiple_messages()
        
        # 4. 測試未監控設備
        self.test_unmonitored_device()
        
        print("\n" + "="*80)
        print("✅ 所有測試完成")
        print("="*80)

if __name__ == '__main__':
    simulator = TestDataSimulator()
    simulator.run_all_tests()
