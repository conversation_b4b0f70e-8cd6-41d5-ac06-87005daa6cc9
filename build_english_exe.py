#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MES Data Forwarding System Build Script (English Version)
Build Flask application into standalone Windows executable for factory deployment
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_and_install_dependencies():
    """檢查並安裝必要的依賴"""
    required_packages = [
        'flask',
        'requests',
        'kafka-python',
        'pika',  # RabbitMQ客戶端
        'pyinstaller'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            # 特殊處理包名映射
            import_name = package
            if package == 'kafka-python':
                import_name = 'kafka'
            elif package == 'pyinstaller':
                import_name = 'PyInstaller'

            __import__(import_name)
            print(f"✅ {package} 已安裝")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安裝")

    if missing_packages:
        print(f"\n⚠️ 發現缺少依賴: {', '.join(missing_packages)}")
        print("正在嘗試安裝缺少的依賴...")

        for package in missing_packages:
            try:
                print(f"📦 安裝 {package}...")
                result = subprocess.run([sys.executable, '-m', 'pip', 'install', package],
                                      capture_output=True, text=True, check=True)
                print(f"✅ {package} 安裝成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {package} 安裝失敗: {e}")
                print(f"錯誤輸出: {e.stderr}")
                return False

    return True

def create_main_spec_file():
    """Create PyInstaller spec file for main application"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
        ('devices.json', '.'),
        ('kafka_config.json', '.'),
        ('rabbitmq_config.json', '.'),
        ('test_config.py', '.'),
        ('fake_data_generator.py', '.'),
        ('test_data_generator.py', '.'),
        ('pqm_api.py', '.'),
    ],
    hiddenimports=[
        'flask',
        'kafka',
        'pika',
        'json',
        'threading',
        'datetime',
        'requests',
        'uuid',
        'time',
        'os',
        'sys',
        'hashlib',
        'urllib.request',
        'urllib.error'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='MES_Upload_Manager',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('mes_upload_manager.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print("✅ Created PyInstaller spec file: mes_upload_manager.spec")

def create_browser_spec_file():
    """Create PyInstaller spec file for browser launcher"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['open_browser.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'webbrowser',
        'time',
        'sys',
        'subprocess',
        'os',
        'urllib.request',
        'urllib.error'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Open_Browser',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('open_browser_english.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Created browser launcher spec file: open_browser_english.spec")

def build_executable():
    """Build executable files"""
    print("\n🔨 Building executable files...")
    
    try:
        # Build main application
        print("Building main application...")
        result = subprocess.run([
            'pyinstaller',
            '--clean',
            'mes_upload_manager.spec'
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode != 0:
            print(f"❌ Main application build failed: {result.stderr}")
            return False
        
        print("✅ Main application built successfully")
        
        # Build browser launcher
        print("Building browser launcher...")
        result = subprocess.run([
            'pyinstaller', 
            '--clean',
            'open_browser_english.spec'
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode != 0:
            print(f"❌ Browser launcher build failed: {result.stderr}")
            return False
        
        print("✅ Browser launcher built successfully")
        return True
            
    except Exception as e:
        print(f"❌ Build process error: {e}")
        return False

def create_deployment_package():
    """Create deployment package"""
    print("\n📦 Creating deployment package...")
    
    # Create deployment directory
    deploy_dir = "MES_Upload_Manager_v0.0.1-beta-1"
    if os.path.exists(deploy_dir):
        shutil.rmtree(deploy_dir)
    os.makedirs(deploy_dir)
    
    # Copy main program
    exe_source = "dist/MES_Upload_Manager.exe"
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, os.path.join(deploy_dir, "MES_Upload_Manager.exe"))
        print("✅ Copied main program")
    else:
        print("❌ Main program file not found")
        return False
    
    # Copy browser launcher
    browser_source = "dist/Open_Browser.exe"
    if os.path.exists(browser_source):
        shutil.copy2(browser_source, os.path.join(deploy_dir, "Open_Browser.exe"))
        print("✅ Copied browser launcher")
    else:
        print("❌ Browser launcher file not found")
        return False
    
    # Copy configuration files
    config_files = ['devices.json', 'kafka_config.json', 'rabbitmq_config.json']
    for config_file in config_files:
        if os.path.exists(config_file):
            shutil.copy2(config_file, deploy_dir)
            print(f"✅ Copied config file: {config_file}")

    # Copy test mode files (optional)
    test_files = ['test_config.py', 'fake_data_generator.py', 'test_data_generator.py', 'pqm_api.py']
    for test_file in test_files:
        if os.path.exists(test_file):
            shutil.copy2(test_file, deploy_dir)
            print(f"✅ Copied test file: {test_file}")
        else:
            print(f"⚠️ Test file not found (optional): {test_file}")
    
    # Copy static files and templates
    for folder in ['static', 'templates']:
        if os.path.exists(folder):
            shutil.copytree(folder, os.path.join(deploy_dir, folder))
            print(f"✅ Copied folder: {folder}")
    
    # Create launch script
    create_launch_script(deploy_dir)
    
    # Create readme file
    create_readme(deploy_dir)
    
    print("✅ Deployment package created successfully")
    return True

def create_launch_script(deploy_dir):
    """Create launch script in English"""
    script_content = '''@echo off
title MES Upload Manager v0.0.1-beta-1
echo ========================================
echo    MES Upload Manager v0.0.1-beta-1
echo ========================================
echo.
echo Starting system...
echo.

REM Start the main MES Upload Manager
start /b "MES Upload Manager" "MES_Upload_Manager.exe"

REM Wait for system to initialize
echo Waiting for system to start...
ping 127.0.0.1 -n 5 > nul

REM Open browser - try only one method at a time
echo Opening web interface...

REM Try default browser first
start http://localhost:5000

echo.
echo ========================================
echo   SYSTEM IS RUNNING
echo ========================================
echo.
echo Web Interface: http://localhost:5000
echo.
echo If browser didn't open automatically:
echo Please manually open your browser and visit the above URL
echo.
echo Quick Start:
echo 1. Configure EAP or SIE settings
echo 2. Add production devices
echo 3. Set up work orders
echo 4. Start monitoring
echo.
echo Press any key to STOP the system
echo ========================================
pause > nul

echo.
echo Stopping system...
taskkill /f /im "MES_Upload_Manager.exe" > nul 2>&1
echo System stopped.
echo.
pause
'''

    script_path = os.path.join(deploy_dir, "Launch_MES_Upload_Manager.bat")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)

    print("✅ Created launch script")

def create_readme(deploy_dir):
    """Create readme file in English"""
    readme_content = '''MES Upload Manager v0.0.1-beta-1 - Deployment Guide

SYSTEM REQUIREMENTS:
- Windows 10 or higher
- No Python installation required
- Network access to EAP/SIE servers and MES system

INSTALLATION:
1. Copy the entire folder to target computer
2. Double-click "Launch_MES_Upload_Manager.bat" to start the system
3. System will automatically open web interface in browser

USAGE:
1. Access URL: http://localhost:5000
2. EAP Settings: Configure EAP server connection
3. SIE Settings: Configure SIE server connection
4. Device Management: Add and manage production devices
5. Work Order Management: Configure device work orders
6. Monitoring: Start EAP or SIE monitoring

FEATURES:
- Real-time device monitoring
- Automatic MES data upload/forwarding
- Work order management with progress tracking
- Dual data source support (EAP Kafka + SIE RabbitMQ)
- Web-based management interface

IMPORTANT NOTES:
- Ensure target computer can access EAP/SIE servers
- Verify MES server connectivity
- Do not close command window while system is running
- To stop system: Press Ctrl+C or close command window
- Port 5000 must be available

TROUBLESHOOTING:
1. Cannot start: Check firewall settings
2. Cannot connect to EAP/SIE: Verify network and server addresses
3. MES upload fails: Check MES server configuration and connectivity
4. Browser doesn't open: Manually visit http://localhost:5000

TECHNICAL SUPPORT:
Contact system administrator for assistance

Version: 0.0.1-beta-1
Build Date: 2025-06-13
'''

    readme_path = os.path.join(deploy_dir, "README.txt")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)

    print("✅ Created readme file")

def main():
    """Main function"""
    print("🏭 MES Upload Manager v0.0.1-beta-1 - Build Tool")
    print("=" * 60)

    # Check and install dependencies first
    print("🔍 檢查依賴...")
    if not check_and_install_dependencies():
        print("❌ 依賴檢查失敗，無法繼續打包")
        return False
    print("✅ 所有依賴已就緒\n")

    # Check required files
    required_files = ['app.py', 'templates', 'static', 'open_browser.py']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ Missing required file: {file}")
            return False

    # Create spec files
    create_main_spec_file()
    create_browser_spec_file()

    # Build executable files
    if not build_executable():
        return False

    # Create deployment package
    if not create_deployment_package():
        return False

    print("\n🎉 Build completed successfully!")
    print("📁 Deployment package location: MES_Upload_Manager_v0.0.1-beta-1/")
    print("🚀 Copy the entire MES_Upload_Manager_v0.0.1-beta-1 folder to target computer")
    print("💡 Run Launch_MES_Upload_Manager.bat on target computer to start the system")

    return True

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)
