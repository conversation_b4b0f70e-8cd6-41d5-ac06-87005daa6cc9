MES Upload Manager v0.0.1-beta-1 - Deployment Guide

SYSTEM REQUIREMENTS:
- Windows 10 or higher
- No Python installation required
- Network access to EAP/SIE servers and MES system

INSTALLATION:
1. Copy the entire folder to target computer
2. Double-click "Launch_MES_Upload_Manager.bat" to start the system
3. System will automatically open web interface in browser

USAGE:
1. Access URL: http://localhost:5000
2. EAP Settings: Configure EAP server connection
3. SIE Settings: Configure SIE server connection
4. Device Management: Add and manage production devices
5. Work Order Management: Configure device work orders
6. Monitoring: Start EAP or SIE monitoring

FEATURES:
- Real-time device monitoring
- Automatic MES data upload/forwarding
- Work order management with progress tracking
- Dual data source support (EAP Kafka + SIE RabbitMQ)
- Web-based management interface

IMPORTANT NOTES:
- Ensure target computer can access EAP/SIE servers
- Verify MES server connectivity
- Do not close command window while system is running
- To stop system: Press Ctrl+C or close command window
- Port 5000 must be available

TROUBLESHOOTING:
1. Cannot start: Check firewall settings
2. Cannot connect to EAP/SIE: Verify network and server addresses
3. MES upload fails: Check MES server configuration and connectivity
4. <PERSON><PERSON><PERSON> doesn't open: Manually visit http://localhost:5000

TECHNICAL SUPPORT:
Contact system administrator for assistance

Version: 0.0.1-beta-1
Build Date: 2025-06-13
