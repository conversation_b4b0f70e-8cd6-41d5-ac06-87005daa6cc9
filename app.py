from flask import Flask, render_template, request, jsonify
import json
import os
from datetime import datetime
from device_manager import Devi<PERSON>Mana<PERSON>
from kafka_monitor import KafkaMonitor
from kafka_config_manager import kafka_config_manager
from rabbitmq_config_manager import RabbitMQConfigManager
from rabbitmq_monitor import RabbitMQMonitor
import threading

# 測試模式支持
try:
    from test_config import test_config, load_test_devices_to_system
    from fake_data_generator import init_fake_data_generator, get_fake_data_generator
    TEST_MODE_AVAILABLE = True
    FAKE_DATA_AVAILABLE = True
except ImportError:
    TEST_MODE_AVAILABLE = False
    FAKE_DATA_AVAILABLE = False
    print("⚠️ 測試模式不可用 - test_config.py 或 fake_data_generator.py 未找到")

app = Flask(__name__)
device_manager = DeviceManager()
kafka_monitor = None

# RabbitMQ相關
rabbitmq_config_manager = RabbitMQConfigManager()
rabbitmq_monitor = None

# 初始化模擬數據生成器（稍後在需要時初始化，因為需要MES API實例）
fake_data_generator = None

@app.route('/')
def index():
    """主頁面"""
    return render_template('index.html')

@app.route('/favicon.ico')
def favicon():
    """處理favicon請求，避免404錯誤"""
    return '', 204  # 返回空內容，狀態碼204 (No Content)

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康檢查端點"""
    try:
        # 檢查設備管理器
        device_count = len(device_manager.get_all_devices())

        # 檢查Kafka監控狀態
        kafka_status = kafka_monitor.is_running() if kafka_monitor else False

        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'device_count': device_count,
            'kafka_running': kafka_status
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/devices', methods=['GET'])
def get_devices():
    """獲取所有設備"""
    try:
        devices = device_manager.get_all_devices()

        # 為每個設備添加最近的生產數據和工單信息
        for device_id, device in devices.items():
            try:
                last_frontend_update = device.get('lastFrontendUpdate')
                recent_data = device_manager.get_recent_production_data(device_id, last_frontend_update)

                # 計算期間內的統計
                period_total = sum(log['unitCount'] for log in recent_data)
                period_forwarded = sum(log['unitCount'] for log in recent_data if log['isForwarded'])
                period_unforwarded = sum(log['unitCount'] for log in recent_data if not log['isForwarded'])

                device['periodData'] = {
                    'logs': recent_data,
                    'totalCount': period_total,
                    'forwardedCount': period_forwarded,
                    'unforwardedCount': period_unforwarded,
                    'sinceTime': last_frontend_update
                }

                # 添加工單顯示信息
                work_order_info = device_manager.get_display_work_order_info(device_id)
                device['displayWorkOrder'] = work_order_info

                # 添加MES錯誤信息（如果存在）
                if 'mesErrors' not in device:
                    device['mesErrors'] = []

                # 更新前端時間戳
                device_manager.update_frontend_timestamp(device_id)

            except Exception as e:
                print(f"處理設備 {device_id} 數據時出錯: {e}")
                # 為出錯的設備提供默認數據
                device['periodData'] = {
                    'logs': [],
                    'totalCount': 0,
                    'forwardedCount': 0,
                    'unforwardedCount': 0,
                    'sinceTime': None
                }
                device['displayWorkOrder'] = {
                    'workOrderNumber': '',
                    'modelName': '',
                    'isActive': False
                }

        return jsonify(devices)

    except Exception as e:
        print(f"獲取設備數據時出錯: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/devices', methods=['POST'])
def add_device():
    """添加新設備"""
    data = request.json
    try:
        device_id = device_manager.add_device(
            device_id=data['deviceId'],
            line_name=data['lineName'],
            section_name=data['sectionName'],
            group_name=data['groupName'],
            station_name=data['stationName']
        )
        return jsonify({'success': True, 'deviceId': device_id})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/api/devices/<device_id>', methods=['PUT'])
def update_device(device_id):
    """更新設備信息"""
    data = request.json
    try:
        device_manager.update_device(device_id, data)
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/api/devices/<device_id>', methods=['DELETE'])
def delete_device(device_id):
    """刪除設備"""
    try:
        device_manager.delete_device(device_id)
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/api/kafka/start', methods=['POST'])
def start_kafka_monitoring():
    """開始Kafka監控"""
    global kafka_monitor
    try:
        data = request.get_json() or {}
        enable_all_devices = data.get('enableAllDevices', False)

        if kafka_monitor is None or not kafka_monitor.is_running():
            kafka_monitor = KafkaMonitor(device_manager)
            # 在後台線程中啟動Kafka監控
            kafka_thread = threading.Thread(target=kafka_monitor.start_monitoring)
            kafka_thread.daemon = True
            kafka_thread.start()

            enabled_devices_count = 0
            if enable_all_devices:
                # 批量啟用所有有可用工單的設備的MES轉發
                enabled_devices_count = device_manager.enable_all_available_devices()

            return jsonify({
                'success': True,
                'message': 'Kafka監控已啟動',
                'enabledDevicesCount': enabled_devices_count
            })
        else:
            return jsonify({'success': False, 'message': 'Kafka監控已在運行中'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/kafka/stop', methods=['POST'])
def stop_kafka_monitoring():
    """停止Kafka監控"""
    global kafka_monitor
    try:
        if kafka_monitor and kafka_monitor.is_running():
            kafka_monitor.stop_monitoring()
            return jsonify({'success': True, 'message': 'Kafka監控已停止'})
        else:
            return jsonify({'success': False, 'message': 'Kafka監控未在運行'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/kafka/status', methods=['GET'])
def get_kafka_status():
    """獲取Kafka監控狀態"""
    global kafka_monitor
    is_running = kafka_monitor is not None and kafka_monitor.is_running()
    return jsonify({'isRunning': is_running})

@app.route('/api/devices/<device_id>/monitoring', methods=['POST'])
def toggle_device_monitoring(device_id):
    """切換單個設備的監控狀態"""
    data = request.json
    is_monitoring = data.get('isMonitoring', False)

    try:
        if is_monitoring:
            # 檢查是否可以開始MES轉發
            can_start, message = device_manager.can_start_mes_forwarding(device_id)
            if not can_start:
                return jsonify({
                    'success': False,
                    'error': message
                }), 400

        device_manager.toggle_device_monitoring(device_id, is_monitoring)
        action = "啟動" if is_monitoring else "停止"
        return jsonify({'success': True, 'message': f'設備 {device_id} MES轉發已{action}'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400



@app.route('/api/kafka/config', methods=['GET'])
def get_kafka_config():
    """獲取Kafka配置"""
    try:
        config = kafka_config_manager.get_config()
        return jsonify(config)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/kafka/config', methods=['POST'])
def update_kafka_config():
    """更新Kafka配置"""
    try:
        data = request.json
        if kafka_config_manager.update_config(data):
            return jsonify({'success': True, 'message': 'Kafka配置更新成功'})
        else:
            return jsonify({'success': False, 'error': '配置驗證失敗'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# RabbitMQ配置管理API
@app.route('/api/rabbitmq/config', methods=['GET'])
def get_rabbitmq_config():
    """獲取RabbitMQ配置"""
    try:
        config = rabbitmq_config_manager.get_config()
        return jsonify(config)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/rabbitmq/config', methods=['POST'])
def update_rabbitmq_config():
    """更新RabbitMQ配置"""
    try:
        data = request.json
        if rabbitmq_config_manager.update_config(data):
            return jsonify({'success': True, 'message': 'RabbitMQ配置更新成功'})
        else:
            return jsonify({'success': False, 'error': '配置驗證失敗'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# RabbitMQ監控管理API
@app.route('/api/rabbitmq/start', methods=['POST'])
def start_rabbitmq_monitoring():
    """開始RabbitMQ監控"""
    global rabbitmq_monitor
    try:
        data = request.get_json() or {}
        enable_all_devices = data.get('enableAllDevices', False)

        if rabbitmq_monitor is None or not rabbitmq_monitor.is_monitoring:
            # 需要導入MES API
            from mes_api import MESApi
            mes_api = MESApi(device_manager)

            rabbitmq_monitor = RabbitMQMonitor(device_manager, mes_api)
            success, message = rabbitmq_monitor.start_monitoring()

            if success:
                enabled_devices_count = 0
                if enable_all_devices:
                    # 批量啟用所有有可用工單的設備的MES轉發
                    enabled_devices_count = device_manager.enable_all_available_devices()

                return jsonify({
                    'success': True,
                    'message': message,
                    'enabledDevicesCount': enabled_devices_count
                })
            else:
                return jsonify({'success': False, 'error': message}), 500
        else:
            return jsonify({'success': False, 'message': 'RabbitMQ監控已在運行中'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/rabbitmq/stop', methods=['POST'])
def stop_rabbitmq_monitoring():
    """停止RabbitMQ監控"""
    global rabbitmq_monitor
    try:
        if rabbitmq_monitor and rabbitmq_monitor.is_monitoring:
            success, message = rabbitmq_monitor.stop_monitoring()
            if success:
                return jsonify({'success': True, 'message': message})
            else:
                return jsonify({'success': False, 'error': message}), 500
        else:
            return jsonify({'success': False, 'message': 'RabbitMQ監控未在運行'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/rabbitmq/status', methods=['GET'])
def get_rabbitmq_status():
    """獲取RabbitMQ監控狀態"""
    global rabbitmq_monitor
    try:
        if rabbitmq_monitor:
            status = rabbitmq_monitor.get_status()
            return jsonify(status)
        else:
            return jsonify({
                'is_monitoring': False,
                'device_count': 0,
                'config': rabbitmq_config_manager.get_connection_info()
            })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 工單管理API
@app.route('/api/devices/<device_id>/work-orders', methods=['GET'])
def get_work_orders(device_id):
    """獲取設備工單列表"""
    try:
        work_orders = device_manager.get_work_orders(device_id)
        current_work_order = device_manager.get_current_work_order(device_id)

        return jsonify({
            'workOrders': work_orders,
            'currentWorkOrder': current_work_order
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/devices/<device_id>/work-orders', methods=['POST'])
def set_work_orders(device_id):
    """設置設備工單列表"""
    try:
        data = request.get_json()
        work_orders = data.get('workOrders', [])

        # 驗證工單數據
        for work_order in work_orders:
            if not work_order.get('workOrderNumber'):
                return jsonify({'error': '工單號不能為空'}), 400
            if not isinstance(work_order.get('targetQuantity'), int) or work_order.get('targetQuantity') <= 0:
                return jsonify({'error': '目標數量必須為正整數'}), 400
            if not work_order.get('modelName'):
                return jsonify({'error': '機種名不能為空'}), 400

            # 驗證過站類型
            station_type = work_order.get('stationType', '無條碼過站')
            valid_station_types = ['無條碼過站', '有條碼過站', '有條碼投入']
            if station_type not in valid_station_types:
                return jsonify({'error': f'無效的過站類型: {station_type}'}), 400

            # 目前只支持無條碼過站
            if station_type != '無條碼過站':
                return jsonify({'error': f'過站類型 "{station_type}" 暫不支持，目前僅支持"無條碼過站"'}), 400

        device_manager.set_work_orders(device_id, work_orders)

        return jsonify({
            'success': True,
            'message': f'已設置 {len(work_orders)} 個工單'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/devices/<device_id>/work-orders/<int:work_order_index>', methods=['DELETE'])
def delete_work_order(device_id, work_order_index):
    """刪除指定工單"""
    try:
        device_manager.delete_work_order(device_id, work_order_index)
        return jsonify({
            'success': True,
            'message': '工單已刪除'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/devices/<device_id>/can-start-mes', methods=['GET'])
def can_start_mes(device_id):
    """檢查是否可以開始MES轉發"""
    try:
        can_start, message = device_manager.can_start_mes_forwarding(device_id)
        return jsonify({
            'canStart': can_start,
            'message': message
        })
    except Exception as e:
        return jsonify({'canStart': False, 'message': str(e)}), 500

# 測試模式API
@app.route('/api/test/load-devices', methods=['POST'])
def load_test_devices():
    """載入測試設備數據"""
    if not TEST_MODE_AVAILABLE:
        return jsonify({'success': False, 'error': '測試模式不可用'}), 400

    try:
        data = request.get_json() or {}
        device_count = data.get('deviceCount', 50)

        # 驗證設備數量
        if not isinstance(device_count, int) or device_count < 1 or device_count > 200:
            return jsonify({'success': False, 'error': '設備數量必須在1-200之間'}), 400

        # 載入測試設備
        success = load_test_devices_to_system(device_manager, device_count)

        if success:
            return jsonify({
                'success': True,
                'message': f'已載入 {device_count} 個測試設備',
                'deviceCount': device_count
            })
        else:
            return jsonify({'success': False, 'error': '載入測試設備失敗'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/test/status', methods=['GET'])
def get_test_status():
    """獲取測試模式狀態"""
    fake_data_status = {}
    if FAKE_DATA_AVAILABLE:
        fake_generator = get_fake_data_generator()
        if fake_generator:
            fake_data_status = fake_generator.get_status()

    return jsonify({
        'testModeAvailable': TEST_MODE_AVAILABLE,
        'fakeDataAvailable': FAKE_DATA_AVAILABLE,
        'currentDeviceCount': len(device_manager.get_all_devices()),
        'isTestMode': TEST_MODE_AVAILABLE and test_config.is_test_mode() if TEST_MODE_AVAILABLE else False,
        'fakeDataStatus': fake_data_status
    })

@app.route('/api/test/clear-devices', methods=['POST'])
def clear_test_devices():
    """清空所有設備"""
    try:
        device_manager.devices = {}
        device_manager.save_devices()

        return jsonify({
            'success': True,
            'message': '已清空所有設備'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# 模擬數據生成API
@app.route('/api/test/fake-data/start', methods=['POST'])
def start_fake_data_generation():
    """開始模擬數據生成"""
    global fake_data_generator

    if not FAKE_DATA_AVAILABLE:
        return jsonify({'success': False, 'error': '模擬數據生成器不可用'}), 400

    try:
        data = request.get_json() or {}
        interval = data.get('interval', 3)

        # 驗證間隔
        if not isinstance(interval, (int, float)) or interval < 1 or interval > 60:
            return jsonify({'success': False, 'error': '生成間隔必須在1-60秒之間'}), 400

        # 初始化模擬數據生成器（如果還沒有初始化）
        if not fake_data_generator:
            # 創建MES API實例
            from mes_api import MESApi
            mes_api = MESApi(device_manager)
            fake_data_generator = init_fake_data_generator(device_manager, mes_api)
            print("✅ 模擬數據生成器已初始化（包含MES API）")

        success = fake_data_generator.start_generation(interval)

        if success:
            return jsonify({
                'success': True,
                'message': f'模擬數據生成已啟動，間隔: {interval}秒',
                'interval': interval
            })
        else:
            return jsonify({'success': False, 'error': '模擬數據生成器已在運行中'}), 400

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/test/fake-data/stop', methods=['POST'])
def stop_fake_data_generation():
    """停止模擬數據生成"""
    if not FAKE_DATA_AVAILABLE:
        return jsonify({'success': False, 'error': '模擬數據生成器不可用'}), 400

    try:
        fake_generator = get_fake_data_generator()
        if not fake_generator:
            return jsonify({'success': False, 'error': '模擬數據生成器未初始化'}), 500

        success = fake_generator.stop_generation()

        if success:
            return jsonify({
                'success': True,
                'message': '模擬數據生成已停止'
            })
        else:
            return jsonify({'success': False, 'error': '模擬數據生成器未在運行'}), 400

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/test/fake-data/status', methods=['GET'])
def get_fake_data_status():
    """獲取模擬數據生成狀態"""
    if not FAKE_DATA_AVAILABLE:
        return jsonify({
            'available': False,
            'is_running': False,
            'error': '模擬數據生成器不可用'
        })

    try:
        fake_generator = get_fake_data_generator()
        if not fake_generator:
            return jsonify({
                'available': False,
                'is_running': False,
                'error': '模擬數據生成器未初始化'
            })

        status = fake_generator.get_status()
        status['available'] = True

        return jsonify(status)

    except Exception as e:
        return jsonify({
            'available': False,
            'is_running': False,
            'error': str(e)
        }), 500

@app.route('/api/test/fake-data/interval', methods=['POST'])
def set_fake_data_interval():
    """設置模擬數據生成間隔"""
    if not FAKE_DATA_AVAILABLE:
        return jsonify({'success': False, 'error': '模擬數據生成器不可用'}), 400

    try:
        data = request.get_json() or {}
        interval = data.get('interval', 3)

        # 驗證間隔
        if not isinstance(interval, (int, float)) or interval < 1 or interval > 60:
            return jsonify({'success': False, 'error': '生成間隔必須在1-60秒之間'}), 400

        fake_generator = get_fake_data_generator()
        if not fake_generator:
            return jsonify({'success': False, 'error': '模擬數據生成器未初始化'}), 500

        fake_generator.set_interval(interval)

        return jsonify({
            'success': True,
            'message': f'生成間隔已設置為 {interval} 秒',
            'interval': interval
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/devices/import', methods=['POST'])
def import_devices_config():
    """導入設備配置"""
    try:
        devices_data = request.get_json()

        if not devices_data or not isinstance(devices_data, dict):
            return jsonify({'success': False, 'error': '無效的設備配置數據'}), 400

        # 驗證設備數據格式
        for device_id, device_data in devices_data.items():
            # 檢查必要字段，支持新舊字段名
            required_fields = [
                ('lineName', 'line'),  # 新字段名, 舊字段名
                ('stationName', 'station'),
                ('isActive', 'isActive')
            ]

            for new_field, old_field in required_fields:
                # 檢查新字段名或舊字段名是否存在
                if new_field not in device_data and old_field not in device_data:
                    return jsonify({
                        'success': False,
                        'error': f'設備 {device_id} 缺少必要字段: {new_field} 或 {old_field}'
                    }), 400

        # 清空現有設備數據
        device_manager.devices.clear()

        # 導入新的設備數據
        device_manager.devices.update(devices_data)

        # 保存到文件
        device_manager.save_devices()

        return jsonify({
            'success': True,
            'message': f'成功導入 {len(devices_data)} 台設備配置',
            'deviceCount': len(devices_data)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    # 創建必要的目錄
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
