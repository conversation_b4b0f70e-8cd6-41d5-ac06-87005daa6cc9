<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>設備數據轉發管理系統</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <div class="header-left">
                    <img src="{{ url_for('static', filename='logo.png') }}" alt="Logo" class="header-logo">
                    <span class="department-info">T2 SI解決方案實施部</span>
                </div>
                <div class="header-center">
                    <h1>🏭 設備數據上傳MES監控</h1>
                </div>
            </div>
        </header>

        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="monitoring-status">
                <div class="kafka-status">
                    <span id="kafka-status-text">EAP監控狀態: 未知</span>
                    <button id="kafka-toggle-btn" class="btn btn-monitor">啟動EAP監控</button>
                </div>
                <div class="rabbitmq-status">
                    <span id="rabbitmq-status-text">SIE監控狀態: 未知</span>
                    <button id="rabbitmq-toggle-btn" class="btn btn-monitor">啟動SIE監控</button>
                </div>
            </div>
            <div class="control-buttons">
                <button id="add-device-btn" class="btn btn-custom">➕ 添加設備</button>
                <button id="kafka-settings-btn" class="btn btn-custom">⚙️ EAP設置</button>
                <button id="rabbitmq-settings-btn" class="btn btn-custom">⚙️ SIE設置</button>
                <button id="export-config-btn" class="btn btn-custom">📤 導出配置</button>
                <button id="import-config-btn" class="btn btn-custom">📥 導入配置</button>
                <button id="test-mode-btn" class="btn btn-warning" style="display: none;">🧪 測試模式</button>
            </div>
        </div>

        <!-- 設備卡片容器 -->
        <div id="devices-container" class="devices-container">
            <!-- 設備卡片將動態生成 -->
        </div>



        <!-- 添加設備模態框 -->
        <div id="device-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modal-title">添加設備</h2>
                    <span class="close">&times;</span>
                </div>
                <form id="device-form">
                    <div class="form-group">
                        <label for="deviceId">設備ID *</label>
                        <input type="text" id="deviceId" name="deviceId" required>
                    </div>
                    <div class="form-group">
                        <label for="lineName">線名 *</label>
                        <input type="text" id="lineName" name="lineName" required>
                    </div>
                    <div class="form-group">
                        <label for="sectionName">段名 *</label>
                        <input type="text" id="sectionName" name="sectionName" required>
                    </div>
                    <div class="form-group">
                        <label for="groupName">組名 *</label>
                        <input type="text" id="groupName" name="groupName" required>
                    </div>
                    <div class="form-group">
                        <label for="stationName">站名 *</label>
                        <input type="text" id="stationName" name="stationName" required>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="cancel-btn">取消</button>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- EAP設置模態框 -->
        <div id="kafka-settings-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>EAP設置</h2>
                    <span class="close">&times;</span>
                </div>
                <form id="kafka-settings-form">
                    <div class="form-group">
                        <label for="bootstrap_servers">EAP服務器地址 *</label>
                        <input type="text" id="bootstrap_servers" name="bootstrap_servers"
                               placeholder="例如: **************:9092" required>
                        <small>格式: IP地址:端口號</small>
                    </div>
                    <div class="form-group">
                        <label for="group_id">消費者組ID *</label>
                        <input type="text" id="group_id" name="group_id"
                               placeholder="例如: mes_forwarder_group" required>
                    </div>
                    <div class="form-group">
                        <label for="factory">廠區名 *</label>
                        <input type="text" id="factory" name="factory"
                               placeholder="例如: CZ" required>
                    </div>
                    <div class="form-group">
                        <label for="mfg_plant_code">產品廠名 *</label>
                        <input type="text" id="mfg_plant_code" name="mfg_plant_code"
                               placeholder="例如: MAG" required>
                    </div>
                    <div class="form-group">
                        <label for="message_type">消息類型</label>
                        <input type="text" id="message_type" name="message_type"
                               value="DEVICE_CFX" readonly>
                    </div>
                    <div class="form-group">
                        <label for="message_name">消息名稱 *</label>
                        <select id="message_name" name="message_name" required>
                            <option value="CFX.Production.UnitsDeparted">UnitsDeparted (產品離站)</option>
                            <option value="CFX.Production.WorkCompleted" selected>WorkCompleted (工作完成)</option>
                        </select>
                        <small>選擇要監控的消息類型</small>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="kafka-cancel-btn">取消</button>
                        <button type="submit" class="btn btn-primary">保存設置</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- SIE設置模態框 -->
        <div id="rabbitmq-settings-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>SIE設置</h2>
                    <span class="close">&times;</span>
                </div>
                <form id="rabbitmq-settings-form">
                    <div class="form-group">
                        <label for="rabbitmq_host">SIE服務器地址 *</label>
                        <input type="text" id="rabbitmq_host" name="host"
                               placeholder="例如: ************" required>
                        <small>SIE服務器IP地址</small>
                    </div>
                    <div class="form-group">
                        <label for="rabbitmq_port">端口號 *</label>
                        <input type="number" id="rabbitmq_port" name="port"
                               placeholder="例如: 30025" min="1" max="65535" required>
                    </div>
                    <div class="form-group">
                        <label for="rabbitmq_username">用戶名 *</label>
                        <input type="text" id="rabbitmq_username" name="username"
                               placeholder="例如: guest" required>
                    </div>
                    <div class="form-group">
                        <label for="rabbitmq_password">密碼 *</label>
                        <input type="password" id="rabbitmq_password" name="password"
                               placeholder="輸入密碼" required>
                    </div>
                    <div class="form-group">
                        <label for="rabbitmq_exchange">Exchange名稱 *</label>
                        <input type="text" id="rabbitmq_exchange" name="exchange_name"
                               placeholder="例如: message-bus" required>
                    </div>
                    <div class="form-group">
                        <label for="rabbitmq_exchange_type">Exchange類型</label>
                        <input type="text" id="rabbitmq_exchange_type" name="exchange_type"
                               value="topic" readonly>
                    </div>
                    <div class="form-group">
                        <label for="rabbitmq_routing_pattern">Routing Key模式 *</label>
                        <select id="rabbitmq_routing_pattern" name="routing_key_pattern" required>
                            <option value="edadata.cfx.#.{device_id}.CFX.Production.UnitsDeparted">UnitsDeparted (產品離站)</option>
                            <option value="edadata.cfx.#.{device_id}.CFX.Production.WorkCompleted" selected>WorkCompleted (工作完成)</option>
                        </select>
                        <small>選擇要監控的消息類型，{device_id} 會被替換為實際的設備ID</small>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="rabbitmq-cancel-btn">取消</button>
                        <button type="submit" class="btn btn-primary">保存設置</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 工單管理模態框 -->
        <div id="work-order-modal" class="modal">
            <div class="modal-content modal-large">
                <div class="modal-header">
                    <h2>工單管理</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="work-order-manager">
                    <!-- 添加新工單 -->
                    <div class="add-work-order-section">
                        <h3>添加新工單</h3>
                        <div class="add-work-order-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="new-work-order-number">工單號 *</label>
                                    <input type="text" id="new-work-order-number" placeholder="輸入工單號">
                                </div>
                                <div class="form-group">
                                    <label for="new-model-name">機種名 *</label>
                                    <input type="text" id="new-model-name" placeholder="輸入機種名">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="new-target-quantity">目標數量 *</label>
                                    <input type="number" id="new-target-quantity" min="1" placeholder="輸入目標數量">
                                </div>
                                <div class="form-group">
                                    <label for="new-description">描述 (可選)</label>
                                    <input type="text" id="new-description" placeholder="輸入工單描述">
                                </div>
                                <div class="form-group">
                                    <label for="new-cavity-count">模穴數 (可選，默認1)</label>
                                    <input type="number" id="new-cavity-count" min="1" step="1" value="1" placeholder="1">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="new-station-type">過站類型 *</label>
                                    <select id="new-station-type" required>
                                        <option value="無條碼過站" selected>無條碼過站</option>
                                        <option value="有條碼過站" disabled>有條碼過站 (開發中)</option>
                                        <option value="有條碼投入" disabled>有條碼投入 (開發中)</option>
                                    </select>
                                    <small>目前僅支持無條碼過站，其他類型正在開發中</small>
                                </div>
                            </div>
                            <button type="button" class="btn btn-success" onclick="deviceManager.addWorkOrder()">
                                ➕ 添加工單
                            </button>
                        </div>
                    </div>

                    <!-- 工單列表 -->
                    <div class="work-order-list-section">
                        <h3>工單列表 <span class="sort-hint">（拖拽調整執行順序）</span></h3>
                        <div id="work-order-list" class="work-order-list sortable">
                            <!-- 工單項目將動態生成 -->
                        </div>
                    </div>

                    <!-- 操作按鈕 -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="deviceManager.hideWorkOrderModal()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="deviceManager.saveWorkOrders()">保存工單</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- MES錯誤信息模態框 -->
        <div id="mes-errors-modal" class="modal">
            <div class="modal-content modal-large">
                <div class="modal-header">
                    <h2>MES轉發錯誤信息</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="mes-errors-content">
                    <div class="device-info">
                        <h3 id="mes-errors-device-title">設備: </h3>
                        <p id="mes-errors-device-details"></p>
                    </div>
                    <div class="mes-errors-list-section">
                        <div class="mes-errors-header">
                            <h3>錯誤歷史記錄 (最近50筆)</h3>
                        </div>
                        <div id="mes-errors-list" class="mes-errors-list">
                            <!-- 錯誤記錄將動態生成 -->
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="deviceManager.hideMesErrorsModal()">關閉</button>
                </div>
            </div>
        </div>

        <!-- 消息提示 -->
        <div id="message" class="message"></div>

        <!-- 页面底部信息 -->
        <div class="footer-info">
            <span class="auto-refresh-info">🔄 頁面每5秒自動刷新設備狀態</span>
            <span class="version-separator">---</span>
            <span class="version-info">版本: 0.0.1-beta-1</span>
        </div>
    </div>

    <!-- 測試模式模態框 -->
    <div id="test-mode-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🧪 測試模式</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="test-device-count">測試設備數量:</label>
                    <select id="test-device-count" class="form-control">
                        <option value="40">40 台設備</option>
                        <option value="50" selected>50 台設備</option>
                        <option value="60">60 台設備</option>
                        <option value="70">70 台設備</option>
                        <option value="100">100 台設備</option>
                    </select>
                </div>
                <div class="form-group">
                    <p class="test-warning">⚠️ 注意：載入測試數據將清空現有的所有設備數據</p>
                </div>
                <div class="test-status" id="test-status">
                    <p>當前設備數量: <span id="current-device-count">0</span></p>
                    <p>測試模式狀態: <span id="test-mode-status">檢查中...</span></p>
                    <p>模擬數據生成: <span id="fake-data-status">檢查中...</span></p>
                </div>

                <div class="form-group" id="fake-data-controls" style="display: none;">
                    <label for="fake-data-interval">模擬數據生成間隔 (秒):</label>
                    <select id="fake-data-interval" class="form-control">
                        <option value="1">1 秒 (快速)</option>
                        <option value="2">2 秒</option>
                        <option value="3" selected>3 秒 (默認)</option>
                        <option value="5">5 秒</option>
                        <option value="10">10 秒 (慢速)</option>
                    </select>
                    <div class="fake-data-info">
                        <p>📊 活躍設備: <span id="active-devices-count">0</span> 個</p>
                        <p>⚡ 生成狀態: <span id="generation-status">已停止</span></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="deviceManager.hideTestModeModal()">取消</button>
                <button type="button" class="btn btn-danger" onclick="deviceManager.clearAllDevices()">清空設備</button>
                <button type="button" class="btn btn-warning" onclick="deviceManager.loadTestDevices()">載入測試數據</button>
                <button type="button" class="btn btn-success" id="start-fake-data-btn" onclick="deviceManager.startFakeDataGeneration()" style="display: none;">開始模擬數據</button>
                <button type="button" class="btn btn-secondary" id="stop-fake-data-btn" onclick="deviceManager.stopFakeDataGeneration()" style="display: none;">停止模擬數據</button>
            </div>
        </div>
    </div>

    <!-- 導入配置模態框 -->
    <div id="import-config-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📥 導入設備配置</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="config-file-input">選擇配置文件 (JSON格式):</label>
                    <input type="file" id="config-file-input" accept=".json" class="form-control">
                    <p class="import-info">⚠️ 注意：導入配置將覆蓋現有的所有設備數據</p>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="backup-before-import">
                        導入前自動備份當前配置
                    </label>
                </div>

                <div id="import-preview" class="import-preview" style="display: none;">
                    <h4>配置預覽:</h4>
                    <div id="import-preview-content"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="deviceManager.hideImportModal()">取消</button>
                <button type="button" class="btn btn-primary" id="confirm-import-btn" onclick="deviceManager.confirmImport()" disabled>確認導入</button>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
