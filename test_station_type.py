#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試過站類型功能
驗證工單管理中的過站類型選擇和MES上傳時的條碼處理
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from device_manager import DeviceManager
from mes_api import MESApi

def test_work_order_station_type():
    """測試工單過站類型功能"""
    print("=== 測試工單過站類型功能 ===")
    
    device_manager = DeviceManager()
    
    # 添加測試設備
    test_device_id = 'TEST_DEVICE_001'
    device_manager.add_device(
        device_id=test_device_id,
        line_name='TEST_LINE',
        station_name='TEST_STATION',
        section_name='TEST_SECTION',
        group_name='TEST_GROUP'
    )
    
    print(f"✅ 已添加測試設備: {test_device_id}")
    
    # 測試添加不同過站類型的工單
    test_work_orders = [
        {
            'workOrderNumber': 'WO001',
            'targetQuantity': 100,
            'modelName': 'MODEL_A',
            'description': '無條碼過站測試',
            'cavityCount': 1,
            'stationType': '無條碼過站'
        },
        {
            'workOrderNumber': 'WO002',
            'targetQuantity': 200,
            'modelName': 'MODEL_B',
            'description': '有條碼過站測試',
            'cavityCount': 2,
            'stationType': '有條碼過站'
        },
        {
            'workOrderNumber': 'WO003',
            'targetQuantity': 150,
            'modelName': 'MODEL_C',
            'description': '有條碼投入測試',
            'cavityCount': 1,
            'stationType': '有條碼投入'
        }
    ]
    
    for work_order in test_work_orders:
        try:
            device_manager.add_work_order(
                device_id=test_device_id,
                work_order_number=work_order['workOrderNumber'],
                target_quantity=work_order['targetQuantity'],
                model_name=work_order['modelName'],
                description=work_order['description'],
                cavity_count=work_order['cavityCount'],
                station_type=work_order['stationType']
            )
            print(f"✅ 成功添加工單: {work_order['workOrderNumber']} - {work_order['stationType']}")
        except Exception as e:
            print(f"❌ 添加工單失敗: {work_order['workOrderNumber']} - {e}")
    
    # 檢查工單列表
    work_orders = device_manager.get_work_orders(test_device_id)
    print(f"\n📋 設備 {test_device_id} 的工單列表:")
    for i, wo in enumerate(work_orders):
        print(f"  {i+1}. {wo['workOrderNumber']} - {wo['stationType']} - {wo['modelName']}")
    
    # 測試獲取當前工單
    current_work_order = device_manager.get_current_work_order(test_device_id)
    if current_work_order:
        print(f"\n🎯 當前工單: {current_work_order['workOrderNumber']} - {current_work_order['stationType']}")
    else:
        print("\n⚠️ 沒有當前工單")
    
    print()

def test_mes_api_station_type():
    """測試MES API過站類型處理"""
    print("=== 測試MES API過站類型處理 ===")
    
    device_manager = DeviceManager()
    mes_api = MESApi(device_manager)
    
    # 測試設備信息
    device_info = {
        'deviceId': 'TEST_DEVICE_001',
        'lineName': 'TEST_LINE',
        'sectionName': 'TEST_SECTION',
        'groupName': 'TEST_GROUP',
        'stationName': 'TEST_STATION'
    }
    
    # 測試產品數據
    product_data = {
        'UnitIdentifier': 'REAL_BARCODE_12345',
        'Status': 'Pass'
    }
    
    # 測試不同過站類型的工單信息
    test_cases = [
        {
            'name': '無條碼過站',
            'work_order_info': {
                'workOrder': 'WO001',
                'model': 'MODEL_A',
                'cavityCount': 1,
                'stationType': '無條碼過站'
            },
            'expected_barcode': 'DEFAULT'
        },
        {
            'name': '有條碼過站',
            'work_order_info': {
                'workOrder': 'WO002',
                'model': 'MODEL_B',
                'cavityCount': 2,
                'stationType': '有條碼過站'
            },
            'expected_barcode': 'REAL_BARCODE_12345'
        },
        {
            'name': '有條碼投入',
            'work_order_info': {
                'workOrder': 'WO003',
                'model': 'MODEL_C',
                'cavityCount': 1,
                'stationType': '有條碼投入'
            },
            'expected_barcode': 'REAL_BARCODE_12345'
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 測試 {test_case['name']}:")
        print(f"   原始條碼: {product_data['UnitIdentifier']}")
        print(f"   過站類型: {test_case['work_order_info']['stationType']}")
        print(f"   預期條碼: {test_case['expected_barcode']}")
        
        try:
            # 調用MES API（這會打印詳細的處理信息）
            result = mes_api.upload_production_data(
                device_info,
                test_case['work_order_info'],
                product_data
            )
            
            if result['success']:
                print(f"   ✅ MES上傳成功")
            else:
                print(f"   ❌ MES上傳失敗: {result['message']}")
                
        except Exception as e:
            print(f"   ❌ 測試異常: {e}")
    
    print()

def test_device_manager_mes_forwarding():
    """測試設備管理器的MES轉發功能"""
    print("=== 測試設備管理器MES轉發功能 ===")
    
    device_manager = DeviceManager()
    
    # 確保測試設備存在
    test_device_id = 'TEST_DEVICE_001'
    if test_device_id not in device_manager.devices:
        device_manager.add_device(
            device_id=test_device_id,
            line_name='TEST_LINE',
            station_name='TEST_STATION',
            section_name='TEST_SECTION',
            group_name='TEST_GROUP'
        )
    
    # 確保有工單
    work_orders = device_manager.get_work_orders(test_device_id)
    if not work_orders:
        device_manager.add_work_order(
            device_id=test_device_id,
            work_order_number='WO_TEST',
            target_quantity=50,
            model_name='TEST_MODEL',
            description='測試工單',
            cavity_count=1,
            station_type='無條碼過站'
        )
    
    # 測試產品數據
    test_product_data = {
        'UnitIdentifier': 'ORIGINAL_BARCODE_999',
        'Status': 'Pass'
    }
    
    print(f"📦 測試產品數據: {test_product_data}")
    print(f"🎯 當前工單過站類型: 無條碼過站")
    print(f"🔄 開始MES轉發測試...")
    
    try:
        # 調用設備管理器的MES轉發方法
        success = device_manager.forward_to_mes(test_device_id, test_product_data)
        
        if success:
            print(f"✅ 設備管理器MES轉發成功")
        else:
            print(f"❌ 設備管理器MES轉發失敗")
            
    except Exception as e:
        print(f"❌ MES轉發測試異常: {e}")
    
    print()

if __name__ == "__main__":
    print("開始測試過站類型功能...\n")
    
    try:
        test_work_order_station_type()
        test_mes_api_station_type()
        test_device_manager_mes_forwarding()
        
        print("✅ 所有過站類型功能測試完成！")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
