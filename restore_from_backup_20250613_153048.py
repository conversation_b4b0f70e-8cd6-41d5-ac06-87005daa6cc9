#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
import zipfile

def restore_system():
    """恢復系統到備份狀態"""
    
    backup_zip = "backup_20250613_153048.zip"
    
    if not os.path.exists(backup_zip):
        print(f"❌ 備份文件不存在: {backup_zip}")
        return False
    
    print("🔄 開始恢復系統...")
    
    # 解壓備份文件
    with zipfile.ZipFile(backup_zip, 'r') as zipf:
        zipf.extractall("temp_restore")
    
    # 恢復文件
    restore_items = [
        # 主要應用文件
        'app.py',
        'start.py',
        'open_browser.py',

        # 核心模組
        'device_manager.py',
        'kafka_monitor.py',
        'kafka_config_manager.py',
        'rabbitmq_monitor.py',
        'rabbitmq_config_manager.py',
        'mes_api.py',
        'mes_forwarder.py',
        'config.py',

        # 數據獲取模組
        'getdata.py',
        'getdatafromrabbitmq.py',
        'MESnewtdc.py',

        # 測試文件
        'test_data_simulator.py',
        'test_forwarding_stats.py',
        'test_mes_forwarding_states.py',
        'test_multi_products.py',
        'rengong.py',

        # 遷移腳本
        'migrate_devices.py',
        'migrate_forwarding_stats.py',
        'migrate_period_logs.py',

        # 打包腳本
        'build_exe.py',
        'build_english_exe.py',
        'create_zip.py',
        'create_english_zip.py',
        'create_docker_package.py',

        # 前端資源
        'static/',
        'templates/',

        # 配置文件
        'devices.json',
        'kafka_config.json',
        'rabbitmq_config.json',
        'requirements.txt',

        # 規格文件
        'mes_system.spec',
        'mes_system_english.spec',
        'mes_upload_manager.spec',
        'open_browser_english.spec',
        'Open_Browser.spec',

        # 資源文件
        'logo.png',

        # 文檔文件
        'README.md',
        'README_RabbitMQ.md',
        'COMPLETE_FEATURE_SUMMARY.md',
        'FEATURE_UPDATE.md',
        'FINAL_FEATURE_SUMMARY.md',
        'UPDATE_SUMMARY.md'
    ]
    
    for item in restore_items:
        src = os.path.join("temp_restore", item)
        if os.path.exists(src):
            if os.path.isfile(src):
                shutil.copy2(src, item)
                print(f"✅ 已恢復文件: {item}")
            elif os.path.isdir(src):
                if os.path.exists(item):
                    shutil.rmtree(item)
                shutil.copytree(src, item)
                print(f"✅ 已恢復目錄: {item}")
    
    # 清理臨時文件
    shutil.rmtree("temp_restore")
    
    print("🎉 系統恢復完成！")
    print("請重新啟動應用: python app.py")
    
    return True

if __name__ == "__main__":
    restore_system()
